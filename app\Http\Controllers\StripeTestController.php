<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Stripe\Exception\ApiErrorException;
use Stripe\PaymentIntent;
use Stripe\Stripe;

class StripeTestController extends Controller
{
    public function index()
    {
        return view('stripe-test');
    }

    public function createPaymentIntent(Request $request)
    {
        $request->validate([
            'amount' => 'required|numeric|min:1',
        ]);

        try {
            // Set your secret key
            Stripe::setApiKey(env('STRIPE_SECRET'));

            // Create a PaymentIntent with amount and currency
            $paymentIntent = PaymentIntent::create([
                'amount' => $request->amount, // Amount in cents
                'currency' => 'usd',
                'payment_method_types' => ['card'],
                'description' => 'Test payment',
                'metadata' => [
                    'integration_check' => 'accept_a_payment',
                ],
            ]);

            return response()->json([
                'clientSecret' => $paymentIntent->client_secret,
            ]);
        } catch (ApiErrorException $e) {
            return response()->json([
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function checkoutTest()
    {
        return view('checkout-test');
    }

    public function createCheckoutSession(Request $request)
    {
        $request->validate([
            'amount' => 'required|numeric|min:1',
            'name' => 'required|string',
            'email' => 'required|email',
            'address' => 'required|string',
            'city' => 'required|string',
            'zip' => 'required|string',
        ]);

        try {
            // Set your secret key
            Stripe::setApiKey(env('STRIPE_SECRET'));

            // Create a PaymentIntent with amount and currency
            $paymentIntent = PaymentIntent::create([
                'amount' => $request->amount, // Amount in cents
                'currency' => 'usd',
                'payment_method_types' => ['card'],
                'description' => 'NatRyon - 30 Day Supply',
                'receipt_email' => $request->email,
                'metadata' => [
                    'customer_name' => $request->name,
                    'customer_email' => $request->email,
                    'shipping_address' => $request->address,
                    'shipping_city' => $request->city,
                    'shipping_zip' => $request->zip,
                ],
            ]);

            return response()->json([
                'clientSecret' => $paymentIntent->client_secret,
            ]);
        } catch (ApiErrorException $e) {
            return response()->json([
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}
