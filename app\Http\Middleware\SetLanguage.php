<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Session;
use Symfony\Component\HttpFoundation\Response;

class SetLanguage
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Get locale from session or use default
        $locale = Session::get('locale', config('app.locale'));

        // Always set the application locale
        App::setLocale($locale);

        // Make sure the session has the locale
        if (!Session::has('locale')) {
            Session::put('locale', $locale);
        }

        // Add debugging
        \Illuminate\Support\Facades\Log::info('Current app locale: ' . App::getLocale());
        \Illuminate\Support\Facades\Log::info('Session locale: ' . Session::get('locale'));
        \Illuminate\Support\Facades\Log::info('Config locale: ' . config('app.locale'));

        return $next($request);
    }
}
