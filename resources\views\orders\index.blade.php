@extends('layouts.app')

@section('title', 'My Orders - NATRYON')

@section('content')
    <!-- Hero Section -->
    <section class="py-5 bg-light">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <h1 class="display-5 fw-bold text-primary mb-3">My Orders</h1>
                    <p class="lead text-secondary mb-4">Track and manage your purchase history with NATRYON</p>
                    <a href="{{ route('products.index') }}" class="btn btn-outline-primary">
                        <i class="bi bi-bag-plus me-2"></i>Continue Shopping
                    </a>
                </div>
                <div class="col-lg-6 text-center text-lg-end">
                    <i class="bi bi-receipt-cutoff" style="font-size: 5rem; color: #428677;"></i>
                </div>
            </div>
        </div>
    </section>

    <!-- Orders Section -->
    <section class="py-5">
        <div class="container">
            <div class="card border-0 shadow-sm rounded-3 overflow-hidden">
                <div class="card-header bg-white py-3 border-bottom border-light">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0 text-primary">
                            <i class="bi bi-clock-history me-2"></i>Order History
                        </h5>
                        <span class="badge bg-light text-dark">{{ $orders->count() }} {{ Str::plural('Order', $orders->count()) }}</span>
                    </div>
                </div>
                <div class="card-body p-0">
                    @if($orders->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th class="ps-4">Order #</th>
                                        <th>Date</th>
                                        <th>Status</th>
                                        <th>Total</th>
                                        <th class="text-end pe-4">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($orders as $order)
                                        <tr>
                                            <td class="ps-4 fw-medium">{{ $order->order_number }}</td>
                                            <td>{{ $order->created_at->format('M d, Y') }}</td>
                                            <td>
                                                @if($order->status == 'pending')
                                                    <span class="badge rounded-pill bg-warning text-dark">
                                                        <i class="bi bi-hourglass-split me-1"></i>Pending
                                                    </span>
                                                @elseif($order->status == 'processing')
                                                    <span class="badge rounded-pill bg-info text-dark">
                                                        <i class="bi bi-gear-wide-connected me-1"></i>Processing
                                                    </span>
                                                @elseif($order->status == 'completed')
                                                    <span class="badge rounded-pill bg-success">
                                                        <i class="bi bi-check-circle me-1"></i>Completed
                                                    </span>
                                                @elseif($order->status == 'declined')
                                                    <span class="badge rounded-pill bg-danger">
                                                        <i class="bi bi-x-circle me-1"></i>Declined
                                                    </span>
                                                @endif
                                            </td>
                                            <td class="fw-bold">${{ number_format($order->total, 2) }}</td>
                                            <td class="text-end pe-4">
                                                <a href="{{ route('orders.show', $order->id) }}" class="btn btn-sm btn-primary rounded-pill">
                                                    <i class="bi bi-eye me-1"></i>View Details
                                                </a>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                        <div class="p-4 border-top">
                            {{ $orders->links() }}
                        </div>
                    @else
                        <div class="text-center py-5">
                            <div class="mb-4">
                                <i class="bi bi-bag-x" style="font-size: 4rem; color: #b1b7b8;"></i>
                            </div>
                            <h4 class="text-secondary mb-3">No Orders Found</h4>
                            <p class="text-muted mb-4">You haven't placed any orders with us yet.</p>
                            <a href="{{ route('products.index') }}" class="btn btn-primary rounded-pill px-4 py-2">
                                <i class="bi bi-cart-plus me-2"></i>Browse Our Products
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </section>
@endsection

@push('styles')
<style>
    .table > :not(caption) > * > * {
        padding: 1rem 0.75rem;
    }

    .badge {
        font-weight: 500;
        padding: 0.5em 0.8em;
    }

    .btn-primary {
        background-color: #428677;
        border-color: #428677;
    }

    .btn-primary:hover, .btn-primary:focus {
        background-color: #2d504b;
        border-color: #2d504b;
    }

    .btn-outline-primary {
        color: #428677;
        border-color: #428677;
    }

    .btn-outline-primary:hover, .btn-outline-primary:focus {
        background-color: #428677;
        border-color: #428677;
    }

    .text-primary {
        color: #428677 !important;
    }

    .pagination {
        --bs-pagination-active-bg: #428677;
        --bs-pagination-active-border-color: #428677;
    }
</style>
@endpush
