@extends('layouts.app')

@section('title', 'Affiliate Dashboard - NatRyon')

@section('content')
    <!-- Hero Section -->
    <section class="py-4 bg-light">
        <div class="container">
            <div class="text-center mb-5">
                <h1 class="display-5 fw-bold" style="color: #428677;">Affiliate Dashboard</h1>
                <p class="lead">Manage your affiliate program and track your earnings</p>
            </div>
        </div>
    </section>

    <!-- Affiliate Dashboard Section -->
    <section class="py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-header bg-white py-3">
                            <h5 class="mb-0" style="color: #428677;">Your Affiliate Code</h5>
                        </div>
                        <div class="card-body">
                            @if($affiliateCode)
                                <div class="alert alert-success border-0" style="background-color: rgba(66, 134, 119, 0.1);">
                                    <h5 class="alert-heading" style="color: #428677;">Your code: <strong>{{ $affiliateCode->code }}</strong></h5>
                                    <p class="mb-0">Share this code with friends and earn {{ $affiliateCode->commission_rate }}% commission on their purchases!</p>
                                </div>

                                <div class="mb-3">
                                    <label for="affiliate-link" class="form-label">Your Affiliate Link</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="affiliate-link" value="{{ route('home') }}?ref={{ $affiliateCode->code }}" readonly>
                                        <button class="btn btn-outline-secondary" type="button" onclick="copyToClipboard('affiliate-link')">
                                            <i class="bi bi-clipboard"></i>
                                        </button>
                                    </div>
                                    <small class="text-muted">Share this link with friends to earn commissions.</small>
                                </div>
                            @else
                                <div class="text-center py-4">
                                    <div class="mb-3" style="width: 80px; height: 80px; background-color: rgba(66, 134, 119, 0.1); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto;">
                                        <i class="bi bi-tag" style="font-size: 2.5rem; color: #428677;"></i>
                                    </div>
                                    <h4 class="mt-3" style="color: #428677;">No Affiliate Code Yet</h4>
                                    <p class="text-muted">Generate your affiliate code to start earning commissions.</p>
                                    <form action="{{ route('affiliate.generate-code') }}" method="POST">
                                        @csrf
                                        <button type="submit" class="btn btn-primary mt-3" style="background-color: #428677; border-color: #428677;">
                                            Generate Affiliate Code
                                        </button>
                                    </form>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                <div class="col-lg-8 mb-4">
                    <div class="row g-4">
                        <div class="col-md-6">
                            <div class="card border-0 shadow-sm h-100">
                                <div class="card-body text-center">
                                    <div class="mb-3" style="width: 60px; height: 60px; background-color: rgba(66, 134, 119, 0.1); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto;">
                                        <i class="bi bi-wallet2" style="font-size: 1.8rem; color: #428677;"></i>
                                    </div>
                                    <h6 class="text-muted mb-2">Total Earnings</h6>
                                    <h2 class="mb-0" style="color: #428677;">${{ number_format($totalEarnings, 2) }}</h2>
                                    <a href="{{ route('affiliate.earnings') }}" class="btn btn-sm btn-outline-primary mt-3" style="color: #428677; border-color: #428677;">View Details</a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-0 shadow-sm h-100">
                                <div class="card-body text-center">
                                    <div class="mb-3" style="width: 60px; height: 60px; background-color: rgba(66, 134, 119, 0.1); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto;">
                                        <i class="bi bi-hourglass-split" style="font-size: 1.8rem; color: #428677;"></i>
                                    </div>
                                    <h6 class="text-muted mb-2">Pending Earnings</h6>
                                    <h2 class="mb-0" style="color: #428677;">${{ number_format($pendingEarnings, 2) }}</h2>
                                    <a href="{{ route('affiliate.earnings') }}" class="btn btn-sm btn-outline-primary mt-3" style="color: #428677; border-color: #428677;">View Details</a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-0 shadow-sm h-100">
                                <div class="card-body text-center">
                                    <div class="mb-3" style="width: 60px; height: 60px; background-color: rgba(66, 134, 119, 0.1); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto;">
                                        <i class="bi bi-people" style="font-size: 1.8rem; color: #428677;"></i>
                                    </div>
                                    <h6 class="text-muted mb-2">Referrals</h6>
                                    <h2 class="mb-0" style="color: #428677;">{{ count($referrals) }}</h2>
                                    <a href="{{ route('affiliate.referrals') }}" class="btn btn-sm btn-outline-primary mt-3" style="color: #428677; border-color: #428677;">View Details</a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-0 shadow-sm h-100">
                                <div class="card-body text-center">
                                    <div class="mb-3" style="width: 60px; height: 60px; background-color: rgba(66, 134, 119, 0.1); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto;">
                                        <i class="bi bi-percent" style="font-size: 1.8rem; color: #428677;"></i>
                                    </div>
                                    <h6 class="text-muted mb-2">Commission Rate</h6>
                                    <h2 class="mb-0" style="color: #428677;">{{ $affiliateCode ? $affiliateCode->commission_rate : 0 }}%</h2>
                                    <span class="text-muted mt-3 d-inline-block">Standard commission rate</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-12">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-white py-3">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="mb-0" style="color: #428677;">Recent Earnings</h5>
                                <a href="{{ route('affiliate.earnings') }}" class="btn btn-sm btn-primary" style="background-color: #428677; border-color: #428677;">View All</a>
                            </div>
                        </div>
                        <div class="card-body">
                            @if(count($earnings) > 0)
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>Order #</th>
                                                <th>Date</th>
                                                <th>Order Amount</th>
                                                <th>Commission</th>
                                                <th>Status</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($earnings as $earning)
                                                <tr>
                                                    <td>{{ $earning->order->order_number }}</td>
                                                    <td>{{ $earning->created_at->format('M d, Y') }}</td>
                                                    <td>${{ number_format($earning->order_amount, 2) }}</td>
                                                    <td>${{ number_format($earning->commission_amount, 2) }}</td>
                                                    <td>
                                                        @if($earning->status == 'pending')
                                                            <span class="badge bg-warning">Pending</span>
                                                        @elseif($earning->status == 'approved')
                                                            <span class="badge bg-success">Approved</span>
                                                        @elseif($earning->status == 'paid')
                                                            <span class="badge" style="background-color: #428677;">Paid</span>
                                                        @elseif($earning->status == 'rejected')
                                                            <span class="badge bg-danger">Rejected</span>
                                                        @endif
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            @else
                                <div class="text-center py-5">
                                    <div class="mb-3" style="width: 80px; height: 80px; background-color: rgba(66, 134, 119, 0.1); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto;">
                                        <i class="bi bi-cash" style="font-size: 2.5rem; color: #428677;"></i>
                                    </div>
                                    <h4 class="mt-3" style="color: #428677;">No earnings yet</h4>
                                    <p class="text-muted">Share your affiliate code to start earning commissions.</p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection

@push('scripts')
<script>
    function copyToClipboard(elementId) {
        const element = document.getElementById(elementId);
        element.select();
        document.execCommand('copy');

        // Show a temporary tooltip
        const button = element.nextElementSibling;
        const originalHTML = button.innerHTML;
        button.innerHTML = '<i class="bi bi-check"></i>';
        setTimeout(() => {
            button.innerHTML = originalHTML;
        }, 2000);
    }
</script>
@endpush
