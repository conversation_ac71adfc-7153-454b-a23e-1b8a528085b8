@extends('layouts.app')

@section('content')
<div class="container py-5" style="min-height: 80vh;">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="text-center mb-5">
                <h1 class="display-5 fw-bold text-primary">{{ __('Register') }}</h1>
                <p class="lead text-muted">{{ __('Join the NATRYON community') }}</p>
            </div>

            <div class="card border-0 shadow-sm rounded-4">
                <div class="card-body p-4 p-md-5">
                    <form method="POST" action="{{ route('register') }}">
                        @csrf

                        <div class="mb-4">
                            <label for="name" class="form-label fw-medium">{{ __('Full Name') }}</label>
                            <div class="input-group">
                                <span class="input-group-text bg-light border-end-0"><i class="bi bi-person"></i></span>
                                <input id="name" type="text" class="form-control border-start-0 @error('name') is-invalid @enderror" name="name" value="{{ old('name') }}" required autocomplete="name" autofocus placeholder="{{ __('Enter your full name') }}">
                            </div>
                            @error('name')
                                <span class="text-danger small mt-1">
                                    <strong>{{ $message }}</strong>
                                </span>
                            @enderror
                        </div>

                        <div class="mb-4">
                            <label for="email" class="form-label fw-medium">{{ __('Email Address') }}</label>
                            <div class="input-group">
                                <span class="input-group-text bg-light border-end-0"><i class="bi bi-envelope"></i></span>
                                <input id="email" type="email" class="form-control border-start-0 @error('email') is-invalid @enderror" name="email" value="{{ old('email') }}" required autocomplete="email" placeholder="{{ __('Enter your email address') }}">
                            </div>
                            @error('email')
                                <span class="text-danger small mt-1">
                                    <strong>{{ $message }}</strong>
                                </span>
                            @enderror
                        </div>

                        <div class="mb-4">
                            <label for="password" class="form-label fw-medium">{{ __('Password') }}</label>
                            <div class="input-group">
                                <span class="input-group-text bg-light border-end-0"><i class="bi bi-lock"></i></span>
                                <input id="password" type="password" class="form-control border-start-0 @error('password') is-invalid @enderror" name="password" required autocomplete="new-password" placeholder="{{ __('Create a password') }}">
                            </div>
                            @error('password')
                                <span class="text-danger small mt-1">
                                    <strong>{{ $message }}</strong>
                                </span>
                            @enderror
                        </div>

                        <div class="mb-4">
                            <label for="password-confirm" class="form-label fw-medium">{{ __('Confirm Password') }}</label>
                            <div class="input-group">
                                <span class="input-group-text bg-light border-end-0"><i class="bi bi-shield-lock"></i></span>
                                <input id="password-confirm" type="password" class="form-control border-start-0" name="password_confirmation" required autocomplete="new-password" placeholder="{{ __('Confirm your password') }}">
                            </div>
                        </div>

                        <div class="mb-4">
                            <label for="affiliate_code" class="form-label fw-medium">{{ __('Affiliate Code') }}</label>
                            <div class="input-group">
                                <span class="input-group-text bg-light border-end-0"><i class="bi bi-tag"></i></span>
                                <input id="affiliate_code" type="text" class="form-control border-start-0 @error('affiliate_code') is-invalid @enderror" name="affiliate_code" value="{{ old('affiliate_code', request('ref')) }}" required placeholder="{{ __('Enter affiliate code') }}">
                            </div>
                            <small class="form-text text-muted mt-1">{{ __('An affiliate code is required to register.') }}</small>
                            @error('affiliate_code')
                                <span class="text-danger small mt-1">
                                    <strong>{{ $message }}</strong>
                                </span>
                            @enderror
                        </div>

                        <div class="d-grid mb-4">
                            <button type="submit" class="btn btn-primary btn-lg py-3 rounded-3">
                                <i class="bi bi-person-plus me-2"></i>{{ __('Create Account') }}
                            </button>
                        </div>

                        <div class="text-center">
                            <p class="mb-0">{{ __('Already have an account?') }} <a href="{{ route('login') }}" class="text-decoration-none fw-medium">{{ __('Login here') }}</a></p>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
