@extends('layouts.app')

@section('title', 'Checkout Test')

@section('content')
<div class="container py-5">
    <div class="row">
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Order Summary</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex mb-3">
                        <div class="flex-shrink-0">
                            <img src="{{ asset('images/box-natryon.png') }}" alt="NatRyon Product" class="img-thumbnail" style="width: 100px;">
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h5 class="mb-1">NatRyon - 30 Day Supply</h5>
                            <p class="text-muted mb-1">Quantity: 1</p>
                            <p class="fw-bold">$49.99</p>
                        </div>
                    </div>
                    <hr>
                    <div class="d-flex justify-content-between mb-2">
                        <span>Subtotal:</span>
                        <span>$49.99</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>Shipping:</span>
                        <span>$5.00</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>Tax:</span>
                        <span>$5.00</span>
                    </div>
                    <hr>
                    <div class="d-flex justify-content-between mb-0">
                        <span class="fw-bold">Total:</span>
                        <span class="fw-bold">$59.99</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Payment Information</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <p class="mb-0">This is a test checkout page. You can use the following test card details:</p>
                        <ul class="mb-0">
                            <li>Card Number: 4242 4242 4242 4242</li>
                            <li>Expiry Date: Any future date (e.g., 12/25)</li>
                            <li>CVC: Any 3 digits (e.g., 123)</li>
                            <li>ZIP: Any 5 digits (e.g., 12345)</li>
                        </ul>
                    </div>

                    <form id="payment-form" class="mt-4">
                        <div class="mb-3">
                            <label for="name" class="form-label">Name</label>
                            <input type="text" class="form-control" id="name" required>
                        </div>
                        <div class="mb-3">
                            <label for="email" class="form-label">Email</label>
                            <input type="email" class="form-control" id="email" required>
                        </div>
                        <div class="mb-3">
                            <label for="address" class="form-label">Address</label>
                            <input type="text" class="form-control" id="address" required>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="city" class="form-label">City</label>
                                <input type="text" class="form-control" id="city" required>
                            </div>
                            <div class="col-md-6">
                                <label for="zip" class="form-label">ZIP Code</label>
                                <input type="text" class="form-control" id="zip" required>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="card-element" class="form-label">Credit or debit card</label>
                            <div id="card-element" class="form-control" style="height: 2.4em; padding-top: .7em;"></div>
                            <div id="card-errors" class="invalid-feedback d-block" role="alert"></div>
                        </div>

                        <button type="submit" class="btn btn-primary w-100" id="submit-button">
                            <span class="spinner-border spinner-border-sm d-none" id="spinner" role="status" aria-hidden="true"></span>
                            Pay $59.99
                        </button>
                    </form>

                    <div class="mt-4">
                        <div id="payment-result"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@endsection

@push('scripts')
<script src="https://js.stripe.com/v3/"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Create a Stripe client
        const stripe = Stripe('{{ env('STRIPE_KEY') }}');
        const elements = stripe.elements();

        // Create an instance of the card Element
        const cardElement = elements.create('card');

        // Add an instance of the card Element into the `card-element` div
        cardElement.mount('#card-element');

        // Handle real-time validation errors from the card Element
        cardElement.addEventListener('change', function(event) {
            const displayError = document.getElementById('card-errors');
            if (event.error) {
                displayError.textContent = event.error.message;
            } else {
                displayError.textContent = '';
            }
        });

        // Handle form submission
        const form = document.getElementById('payment-form');
        const submitButton = document.getElementById('submit-button');
        const spinner = document.getElementById('spinner');
        const paymentResult = document.getElementById('payment-result');

        form.addEventListener('submit', async function(event) {
            event.preventDefault();

            // Disable the submit button and show spinner
            submitButton.disabled = true;
            spinner.classList.remove('d-none');
            paymentResult.innerHTML = '';

            const name = document.getElementById('name').value;
            const email = document.getElementById('email').value;
            const address = document.getElementById('address').value;
            const city = document.getElementById('city').value;
            const zip = document.getElementById('zip').value;

            try {
                // Create a PaymentIntent on the server
                const response = await fetch('/api/create-checkout-session', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    body: JSON.stringify({ 
                        amount: 5999, // $59.99 in cents
                        name: name,
                        email: email,
                        address: address,
                        city: city,
                        zip: zip
                    })
                });

                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }

                const data = await response.json();

                // Confirm the PaymentIntent with the card Element
                const result = await stripe.confirmCardPayment(data.clientSecret, {
                    payment_method: {
                        card: cardElement,
                        billing_details: {
                            name: name,
                            email: email,
                            address: {
                                line1: address,
                                city: city,
                                postal_code: zip
                            }
                        }
                    }
                });

                if (result.error) {
                    // Show error to your customer
                    paymentResult.innerHTML = `<div class="alert alert-danger">${result.error.message}</div>`;
                } else {
                    // The payment succeeded!
                    paymentResult.innerHTML = `
                        <div class="alert alert-success">
                            <h5>Payment successful!</h5>
                            <p>Thank you for your order, ${name}!</p>
                            <p>Payment ID: ${result.paymentIntent.id}</p>
                            <p>Amount: $${(result.paymentIntent.amount / 100).toFixed(2)}</p>
                            <p>We've sent a confirmation email to ${email}.</p>
                        </div>
                    `;
                    form.reset();
                    cardElement.clear();
                }
            } catch (error) {
                paymentResult.innerHTML = `<div class="alert alert-danger">Error: ${error.message}</div>`;
            } finally {
                // Re-enable the submit button and hide spinner
                submitButton.disabled = false;
                spinner.classList.add('d-none');
            }
        });
    });
</script>
@endpush
