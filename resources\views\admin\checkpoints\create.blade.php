@extends('layouts.admin')

@section('title', 'Create Checkpoint')

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Create New Checkpoint</h1>
        <a href="{{ url()->previous() }}" class="btn btn-secondary">
            <i class="bi bi-arrow-left"></i> Back
        </a>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold">Checkpoint Information</h6>
        </div>
        <div class="card-body">
            <form action="{{ route('admin.checkpoints.store') }}" method="POST">
                @csrf
                <div class="mb-3">
                    <label for="release_id" class="form-label">Release <span class="text-danger">*</span></label>
                    <select class="form-select @error('release_id') is-invalid @enderror" id="release_id" name="release_id" required>
                        <option value="">Select a release</option>
                        @foreach($releases as $release)
                            <option value="{{ $release->id }}" {{ (old('release_id') == $release->id || request('release_id') == $release->id) ? 'selected' : '' }}>
                                {{ $release->version }} - {{ $release->name }}
                            </option>
                        @endforeach
                    </select>
                    @error('release_id')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="mb-3">
                    <label for="name" class="form-label">Name <span class="text-danger">*</span></label>
                    <input type="text" class="form-control @error('name') is-invalid @enderror" id="name" name="name" value="{{ old('name') }}" required>
                    @error('name')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                    <small class="form-text text-muted">A descriptive name for this checkpoint.</small>
                </div>

                <div class="mb-3">
                    <label for="type" class="form-label">Type <span class="text-danger">*</span></label>
                    <select class="form-select @error('type') is-invalid @enderror" id="type" name="type" required>
                        <option value="">Select a type</option>
                        @foreach($checkpointTypes as $value => $label)
                            <option value="{{ $value }}" {{ old('type') == $value ? 'selected' : '' }}>
                                {{ $label }}
                            </option>
                        @endforeach
                    </select>
                    @error('type')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="mb-3">
                    <label for="description" class="form-label">Description</label>
                    <textarea class="form-control @error('description') is-invalid @enderror" id="description" name="description" rows="5">{{ old('description') }}</textarea>
                    @error('description')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                    <small class="form-text text-muted">Provide a detailed description of what this checkpoint verifies.</small>
                </div>

                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-save"></i> Create Checkpoint
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection
