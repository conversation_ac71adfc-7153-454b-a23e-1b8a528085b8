<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ShippingOption extends Model
{
    use \Illuminate\Database\Eloquent\Factories\HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'description',
        'cost',
        'location_type',
        'locations',
        'delivery_time_min',
        'delivery_time_max',
        'is_active',
        'display_order',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'cost' => 'decimal:2',
        'locations' => 'array',
        'is_active' => 'boolean',
        'delivery_time_min' => 'integer',
        'delivery_time_max' => 'integer',
        'display_order' => 'integer',
    ];

    /**
     * Scope a query to only include active shipping options.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Check if this shipping option is available for the given location.
     */
    public function isAvailableForLocation($countryCode)
    {
        // If location type is 'all', it's available everywhere
        if ($this->location_type === 'all') {
            return true;
        }

        // If locations is null or empty, it's not available
        if (empty($this->locations)) {
            return false;
        }

        // Check if the country code is in the locations array
        return in_array($countryCode, $this->locations);
    }

    /**
     * Get the formatted delivery time.
     */
    public function getDeliveryTimeAttribute()
    {
        if ($this->delivery_time_min === null && $this->delivery_time_max === null) {
            return null;
        }

        if ($this->delivery_time_min === $this->delivery_time_max) {
            return "{$this->delivery_time_min} days";
        }

        if ($this->delivery_time_min === null) {
            return "Up to {$this->delivery_time_max} days";
        }

        if ($this->delivery_time_max === null) {
            return "{$this->delivery_time_min}+ days";
        }

        return "{$this->delivery_time_min}-{$this->delivery_time_max} days";
    }
}
