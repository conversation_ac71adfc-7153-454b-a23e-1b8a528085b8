<?php
    $currentCurrency = $currentCurrency ?? \App\Services\CurrencyService::getUserCurrency();
    $currentCurrencySymbol = $currentCurrencySymbol ?? \App\Services\CurrencyService::getSymbol();
    $activeCurrencies = $activeCurrencies ?? \App\Services\CurrencyService::getActiveCurrencies();

    // Debug: Check if we have currencies
    if ($activeCurrencies->isEmpty()) {
        $activeCurrencies = \App\Models\Currency::active()->get();
    }
?>

<div class="currency-selector">
    <div class="dropdown">
        <button class="btn btn-outline-secondary dropdown-toggle currency-btn" type="button" id="currencyDropdown" data-bs-toggle="dropdown" aria-expanded="false">
            <i class="bi bi-currency-exchange me-2"></i>
            <span class="current-currency"><?php echo e($currentCurrency); ?></span>
            <span class="current-symbol">(<?php echo e($currentCurrencySymbol); ?>)</span>
        </button>
        <ul class="dropdown-menu currency-menu" aria-labelledby="currencyDropdown">
            <?php if($activeCurrencies && $activeCurrencies->count() > 0): ?>
                <?php $__currentLoopData = $activeCurrencies; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $currency): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <li>
                        <a class="dropdown-item currency-option <?php echo e($currentCurrency === $currency->code ? 'active' : ''); ?>"
                           href="#"
                           data-currency="<?php echo e($currency->code); ?>"
                           data-symbol="<?php echo e($currency->symbol); ?>">
                            <div class="currency-item">
                                <div class="currency-main">
                                    <span class="currency-code"><?php echo e($currency->code); ?></span>
                                    <span class="currency-symbol"><?php echo e($currency->symbol); ?></span>
                                </div>
                                <div class="currency-name"><?php echo e($currency->name); ?></div>
                            </div>
                            <?php if($currency->is_default): ?>
                                <span class="badge bg-primary ms-2">Default</span>
                            <?php endif; ?>
                        </a>
                    </li>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <?php else: ?>
                <!-- Fallback currencies if none loaded -->
                <li>
                    <a class="dropdown-item currency-option <?php echo e($currentCurrency === 'EUR' ? 'active' : ''); ?>"
                       href="#" data-currency="EUR" data-symbol="€">
                        <div class="currency-item">
                            <div class="currency-main">
                                <span class="currency-code">EUR</span>
                                <span class="currency-symbol">€</span>
                            </div>
                            <div class="currency-name">Euro</div>
                        </div>
                        <span class="badge bg-primary ms-2">Default</span>
                    </a>
                </li>
                <li>
                    <a class="dropdown-item currency-option <?php echo e($currentCurrency === 'USD' ? 'active' : ''); ?>"
                       href="#" data-currency="USD" data-symbol="$">
                        <div class="currency-item">
                            <div class="currency-main">
                                <span class="currency-code">USD</span>
                                <span class="currency-symbol">$</span>
                            </div>
                            <div class="currency-name">US Dollar</div>
                        </div>
                    </a>
                </li>
                <li>
                    <a class="dropdown-item currency-option <?php echo e($currentCurrency === 'XOF' ? 'active' : ''); ?>"
                       href="#" data-currency="XOF" data-symbol="CFA">
                        <div class="currency-item">
                            <div class="currency-main">
                                <span class="currency-code">XOF</span>
                                <span class="currency-symbol">CFA</span>
                            </div>
                            <div class="currency-name">CFA Franc BCEAO</div>
                        </div>
                    </a>
                </li>
                <li>
                    <a class="dropdown-item currency-option <?php echo e($currentCurrency === 'CNY' ? 'active' : ''); ?>"
                       href="#" data-currency="CNY" data-symbol="¥">
                        <div class="currency-item">
                            <div class="currency-main">
                                <span class="currency-code">CNY</span>
                                <span class="currency-symbol">¥</span>
                            </div>
                            <div class="currency-name">Chinese Yuan Renminbi</div>
                        </div>
                    </a>
                </li>
            <?php endif; ?>
        </ul>
    </div>
</div>

<style>
.currency-selector {
    position: relative;
}

.currency-btn {
    min-width: 120px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.3);
    color: white;
    font-weight: 500;
    font-size: 0.9rem;
}

.currency-btn:hover, .currency-btn:focus {
    background-color: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
    color: white;
}

.current-currency {
    font-weight: 600;
}

.current-symbol {
    font-size: 0.9rem;
    opacity: 0.8;
}

.currency-menu {
    min-width: 200px;
    border: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-radius: 0.5rem;
    padding: 0.5rem 0;
}

.currency-option {
    padding: 0.75rem 1rem;
    transition: all 0.2s ease;
    border: none;
}

.currency-option:hover {
    background-color: #F6E8D2;
    color: #024C3D;
}

.currency-option.active {
    background-color: #024C3D;
    color: white;
}

.currency-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.currency-main {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.currency-code {
    font-weight: 600;
    font-size: 0.95rem;
}

.currency-symbol {
    font-weight: 500;
    color: #6c757d;
    font-size: 0.9rem;
}

.currency-name {
    font-size: 0.8rem;
    color: #6c757d;
}

.currency-option.active .currency-symbol,
.currency-option.active .currency-name {
    color: rgba(255, 255, 255, 0.8);
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const currencyOptions = document.querySelectorAll('.currency-option');

    currencyOptions.forEach(option => {
        option.addEventListener('click', function(e) {
            e.preventDefault();

            const currency = this.dataset.currency;
            const symbol = this.dataset.symbol;

            // Update UI immediately
            document.querySelector('.current-currency').textContent = currency;
            document.querySelector('.current-symbol').textContent = `(${symbol})`;

            // Remove active class from all options
            currencyOptions.forEach(opt => opt.classList.remove('active'));
            // Add active class to clicked option
            this.classList.add('active');

            // Send AJAX request to change currency
            fetch('/currency/change', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({ currency: currency })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Reload page to update all prices
                    window.location.reload();
                } else {
                    console.error('Failed to change currency:', data.message);
                    // Still reload to try to update the page
                    window.location.reload();
                }
            })
            .catch(error => {
                console.error('Error changing currency:', error);
                // Fallback: reload page anyway
                window.location.reload();
            });
        });
    });
});
</script>
<?php /**PATH D:\projects\natryon\resources\views/components/currency-selector.blade.php ENDPATH**/ ?>