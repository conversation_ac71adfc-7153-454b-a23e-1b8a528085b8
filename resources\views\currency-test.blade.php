@extends('layouts.app')

@section('title', 'Currency Test')

@section('content')
<div class="container py-5">
    <div class="row">
        <div class="col-12">
            <h1>Currency Selector Test</h1>
            
            <div class="card mb-4">
                <div class="card-header">
                    <h5>Currency Selector Component</h5>
                </div>
                <div class="card-body">
                    @include('components.currency-selector')
                </div>
            </div>
            
            <div class="card mb-4">
                <div class="card-header">
                    <h5>Current Currency Information</h5>
                </div>
                <div class="card-body">
                    <p><strong>Current Currency:</strong> {{ \App\Services\CurrencyService::getUserCurrency() }}</p>
                    <p><strong>Current Symbol:</strong> {{ \App\Services\CurrencyService::getSymbol() }}</p>
                    <p><strong>Current Name:</strong> {{ \App\Services\CurrencyService::getName() }}</p>
                </div>
            </div>
            
            <div class="card mb-4">
                <div class="card-header">
                    <h5>Available Currencies</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Code</th>
                                    <th>Name</th>
                                    <th>Symbol</th>
                                    <th>Exchange Rate</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach(\App\Services\CurrencyService::getActiveCurrencies() as $currency)
                                <tr>
                                    <td><strong>{{ $currency->code }}</strong></td>
                                    <td>{{ $currency->name }}</td>
                                    <td>{{ $currency->symbol }}</td>
                                    <td>{{ $currency->exchange_rate }}</td>
                                    <td>
                                        @if($currency->is_active)
                                            <span class="badge bg-success">Active</span>
                                        @endif
                                        @if($currency->is_default)
                                            <span class="badge bg-primary">Default</span>
                                        @endif
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h5>Sample Price Conversions</h5>
                </div>
                <div class="card-body">
                    @php
                        $samplePrice = 87.00; // €87.00
                        $userCurrency = \App\Services\CurrencyService::getUserCurrency();
                    @endphp
                    <p><strong>Sample Product Price: €87.00</strong></p>
                    <ul>
                        @foreach(\App\Services\CurrencyService::getActiveCurrencies() as $currency)
                            @php
                                $convertedPrice = \App\Services\CurrencyService::convert($samplePrice, 'EUR', $currency->code);
                            @endphp
                            <li>
                                <strong>{{ $currency->code }}:</strong> 
                                {{ $currency->symbol }}{{ number_format($convertedPrice, 2) }}
                                @if($currency->code === $userCurrency)
                                    <span class="badge bg-info">Current</span>
                                @endif
                            </li>
                        @endforeach
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
