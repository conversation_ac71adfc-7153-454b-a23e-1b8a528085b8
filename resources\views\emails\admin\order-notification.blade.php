<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>New Order Notification</title>
    <style>
        body {
            font-family: 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: #f9f9f9;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #ffffff;
        }
        .header {
            text-align: center;
            padding: 20px 0;
            border-bottom: 1px solid #eaeaea;
        }
        .logo {
            max-width: 150px;
            height: auto;
        }
        .content {
            padding: 20px 0;
        }
        .order-details {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .order-items {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .order-items th, .order-items td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #eaeaea;
        }
        .order-items th {
            background-color: #f5f5f5;
        }
        .footer {
            text-align: center;
            padding: 20px 0;
            font-size: 12px;
            color: #777;
            border-top: 1px solid #eaeaea;
        }
        .button {
            display: inline-block;
            padding: 10px 20px;
            background-color: #428677;
            color: #ffffff;
            text-decoration: none;
            border-radius: 4px;
            margin-top: 20px;
        }
        .alert {
            background-color: #428677;
            color: white;
            padding: 10px 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .total-row {
            font-weight: bold;
            background-color: #f9f9f9;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <img src="{{ asset('images/logos/natryon-logo.png') }}" alt="NATRYON" class="logo">
            <h1>New Order Notification</h1>
            <div class="alert">
                A new order has been placed on your NATRYON store.
            </div>
        </div>
        
        <div class="content">
            <h2>Order #{{ $order->order_number }}</h2>
            <p>A new order has been placed on {{ $order->created_at->format('F d, Y \a\t h:i A') }}.</p>
            
            <div class="order-details">
                <h3>Customer Information</h3>
                <p><strong>Name:</strong> {{ $order->billing_name }}</p>
                <p><strong>Email:</strong> {{ $order->billing_email }}</p>
                <p><strong>Phone:</strong> {{ $order->billing_phone }}</p>
                
                <h3>Billing Address</h3>
                <p>{{ $order->billing_address }}<br>
                {{ $order->billing_city }}, {{ $order->billing_state }} {{ $order->billing_zipcode }}<br>
                {{ $order->billing_country }}</p>
                
                <h3>Shipping Address</h3>
                <p>{{ $order->shipping_name }}<br>
                {{ $order->shipping_address }}<br>
                {{ $order->shipping_city }}, {{ $order->shipping_state }} {{ $order->shipping_zipcode }}<br>
                {{ $order->shipping_country }}</p>
                
                <h3>Payment Information</h3>
                <p><strong>Method:</strong> {{ ucfirst($order->payment_method) }}</p>
                <p><strong>Status:</strong> {{ $order->is_paid ? 'Paid' : 'Unpaid' }}</p>
                @if($order->payment_id)
                <p><strong>Payment ID:</strong> {{ $order->payment_id }}</p>
                @endif
                
                @if($order->affiliate_code)
                <p><strong>Affiliate Code:</strong> {{ $order->affiliate_code }}</p>
                @endif
            </div>
            
            <h3>Order Items</h3>
            <table class="order-items">
                <thead>
                    <tr>
                        <th>Product</th>
                        <th>Quantity</th>
                        <th>Price</th>
                        <th>Total</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($order->items as $item)
                    <tr>
                        <td>
                            {{ $item->product_name }}
                            @if($item->is_subscription)
                            <br><small>(Subscription)</small>
                            @endif
                        </td>
                        <td>{{ $item->quantity }}</td>
                        <td>${{ number_format($item->price, 2) }}</td>
                        <td>${{ number_format($item->price * $item->quantity, 2) }}</td>
                    </tr>
                    @endforeach
                    <tr class="total-row">
                        <td colspan="3" align="right">Subtotal:</td>
                        <td>${{ number_format($order->subtotal, 2) }}</td>
                    </tr>
                    @if($order->tax > 0)
                    <tr>
                        <td colspan="3" align="right">Tax:</td>
                        <td>${{ number_format($order->tax, 2) }}</td>
                    </tr>
                    @endif
                    @if($order->shipping > 0)
                    <tr>
                        <td colspan="3" align="right">Shipping:</td>
                        <td>${{ number_format($order->shipping, 2) }}</td>
                    </tr>
                    @endif
                    <tr class="total-row">
                        <td colspan="3" align="right">Total:</td>
                        <td>${{ number_format($order->total, 2) }}</td>
                    </tr>
                </tbody>
            </table>
            
            @if($order->notes)
            <h3>Order Notes</h3>
            <p>{{ $order->notes }}</p>
            @endif
            
            <div style="text-align: center;">
                <a href="{{ route('admin.orders.show', $order->id) }}" class="button">View Order in Admin Panel</a>
            </div>
        </div>
        
        <div class="footer">
            <p>This is an automated message from your NATRYON store. Please do not reply to this email.</p>
            <p>&copy; {{ date('Y') }} NATRYON. All rights reserved.</p>
        </div>
    </div>
</body>
</html>
