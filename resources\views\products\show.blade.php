@extends('layouts.app')

@section('title', $product->name . ' - NATRYON')

@push('styles')
    <!-- Swiper CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@8/swiper-bundle.min.css" />

    <!-- Fancybox CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fancyapps/ui@4.0/dist/fancybox.css" />

    <style>
        /* Product Gallery Styles */
        .product-gallery {
            position: relative;
            margin-bottom: 30px;
        }

        .swiper {
            width: 100%;
            border-radius: 8px;
            overflow: hidden;
        }

        .swiper-slide {
            background-size: cover;
            background-position: center;
        }

        .swiper-slide img {
            display: block;
            width: 100%;
            height: 400px;
            object-fit: cover;
            cursor: zoom-in;
        }

        .gallery-thumbs {
            margin-top: 15px;
        }

        .gallery-thumbs .swiper-slide {
            opacity: 0.5;
            transition: all 0.3s ease;
            height: 80px;
        }

        .gallery-thumbs .swiper-slide img {
            height: 80px;
            object-fit: cover;
            border-radius: 4px;
        }

        .gallery-thumbs .swiper-slide-thumb-active {
            opacity: 1;
            border: 2px solid #428677;
            border-radius: 4px;
        }

        /* Subscription notice styles */
        .subscription-notice {
            background-color: rgba(66, 134, 119, 0.1);
            border-left: 4px solid #428677;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 4px;
        }

        /* Price per day calculation */
        .price-per-day {
            font-size: 0.9rem;
            color: #6c757d;
            margin-top: 5px;
        }
    </style>
@endpush

@section('content')
    <!-- Currency Selector Section -->
    <section class="py-3 bg-light border-bottom">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item"><a href="{{ route('home') }}">Home</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('products.index') }}">Products</a></li>
                            <li class="breadcrumb-item active" aria-current="page">{{ $product->name }}</li>
                        </ol>
                    </nav>
                </div>
                <div class="col-md-6 text-md-end">
                    @include('components.currency-selector')
                </div>
            </div>
        </div>
    </section>

    <!-- Product Details Section -->
    <section class="py-5">
        <div class="container">
            <div class="row">
                <!-- Product Image Gallery -->
                <div class="col-lg-6 mb-4 mb-lg-0">
                    <div class="product-gallery">
                        <!-- Main Swiper -->
                        <div class="swiper gallery-main shadow-lg">
                            <div class="swiper-wrapper">
                                @if($product->image)
                                    <div class="swiper-slide">
                                        <a href="{{ asset('storage/' . $product->image) }}" data-fancybox="gallery" data-caption="{{ $product->name }}">
                                            <img src="{{ asset('storage/' . $product->image) }}" alt="{{ $product->name }}">
                                        </a>
                                    </div>
                                @else
                                    <div class="swiper-slide">
                                        <img src="https://via.placeholder.com/600x400?text=NATRYON+{{ $product->name }}" alt="{{ $product->name }}">
                                    </div>
                                @endif

                                @if($product->gallery && count($product->gallery) > 0)
                                    @foreach($product->gallery as $image)
                                        <div class="swiper-slide">
                                            <a href="{{ asset('storage/' . $image) }}" data-fancybox="gallery" data-caption="{{ $product->name }}">
                                                <img src="{{ asset('storage/' . $image) }}" alt="{{ $product->name }}">
                                            </a>
                                        </div>
                                    @endforeach
                                @endif
                            </div>
                            <div class="swiper-button-next" style="color: #428677;"></div>
                            <div class="swiper-button-prev" style="color: #428677;"></div>
                        </div>

                        <!-- Thumbs Swiper -->
                        @if($product->gallery && count($product->gallery) > 0 || $product->image)
                            <div class="swiper gallery-thumbs mt-3">
                                <div class="swiper-wrapper">
                                    @if($product->image)
                                        <div class="swiper-slide">
                                            <img src="{{ asset('storage/' . $product->image) }}" alt="{{ $product->name }}">
                                        </div>
                                    @endif

                                    @if($product->gallery && count($product->gallery) > 0)
                                        @foreach($product->gallery as $image)
                                            <div class="swiper-slide">
                                                <img src="{{ asset('storage/' . $image) }}" alt="{{ $product->name }}">
                                            </div>
                                        @endforeach
                                    @endif
                                </div>
                            </div>
                        @endif
                    </div>
                </div>

            <!-- Product Info -->
            <div class="col-lg-6">
                <h1 class="mb-3">{{ $product->name }}</h1>
                <!-- Product Pricing -->
                <div class="mb-3">
                    @php
                        $userCurrency = \App\Services\CurrencyService::getUserCurrency();
                        $currentPrice = $product->getPriceInCurrency($userCurrency);
                        $currentSubscriptionPrice = $product->subscription_price ? \App\Services\CurrencyService::convert($product->subscription_price, $product->currency, $userCurrency) : null;
                        $currencySymbol = \App\Services\CurrencyService::getSymbol($userCurrency);
                    @endphp

                    @if(!$product->subscription_only)
                        <!-- Regular product with optional subscription -->
                        <div class="d-flex align-items-center gap-3 mb-2">
                            <span class="h3 fw-bold mb-0" style="color: #024C3D;">{{ $currencySymbol }}{{ number_format($currentPrice, 2) }}</span>
                            @if($product->currency !== $userCurrency)
                                <span class="badge bg-light text-dark border">
                                    <small>Original: {{ $product->formatted_price }}</small>
                                </span>
                            @endif
                        </div>
                        <div class="price-per-day">Only {{ $currencySymbol }}{{ number_format($currentPrice / 30, 2) }} per day</div>

                        @if($product->allow_subscription && $product->subscription_price)
                            <div class="mt-2">
                                <span class="text-muted">or subscribe for {{ $currencySymbol }}{{ number_format($currentSubscriptionPrice, 2) }}/month</span>
                                <div class="price-per-day">Only {{ $currencySymbol }}{{ number_format($currentSubscriptionPrice / 30, 2) }} per day with subscription</div>
                            </div>
                        @endif
                    @else
                        <!-- Subscription-only product -->
                        <div class="subscription-notice">
                            <div class="d-flex align-items-center mb-2">
                                <i class="bi bi-calendar-check me-2" style="color: #024C3D; font-size: 1.5rem;"></i>
                                <span class="h5 mb-0">Subscription Only Product</span>
                            </div>
                            <p class="mb-0">This premium product is available exclusively with a subscription plan.</p>
                        </div>

                        <div class="d-flex align-items-center gap-3 mb-2">
                            <span class="h3 fw-bold mb-0" style="color: #024C3D;">{{ $currencySymbol }}{{ number_format($currentSubscriptionPrice, 2) }}/month</span>
                            @if($product->currency !== $userCurrency)
                                <span class="badge bg-light text-dark border">
                                    <small>Original: {{ $product->formatted_subscription_price }}/month</small>
                                </span>
                            @endif
                        </div>
                        <div class="price-per-day">Only {{ $currencySymbol }}{{ number_format($currentSubscriptionPrice / 30, 2) }} per day</div>
                    @endif
                </div>

                <div class="mb-4">
                    <p>{!! nl2br(e($product->description)) !!}</p>
                </div>

                <form action="{{ route('cart.add') }}" method="POST">
                    @csrf
                    <input type="hidden" name="product_id" value="{{ $product->id }}">
                    <div class="row g-3 align-items-center mb-4">
                        <div class="col-auto">
                            <label for="quantity" class="col-form-label">Quantity:</label>
                        </div>
                        <div class="col-auto">
                            <select name="quantity" id="quantity" class="form-select">
                                @for($i = 1; $i <= 10; $i++)
                                    <option value="{{ $i }}">{{ $i }}</option>
                                @endfor
                            </select>
                        </div>

                        @if($product->allow_subscription && $product->subscription_price && !$product->subscription_only)
                            <div class="col-auto">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="is_subscription" id="is_subscription" value="1">
                                    <label class="form-check-label" for="is_subscription">
                                        Subscribe & Save
                                    </label>
                                </div>
                            </div>
                        @elseif($product->subscription_only)
                            <!-- Hidden input for subscription-only products -->
                            <input type="hidden" name="is_subscription" value="1">
                        @endif
                    </div>
                    <div class="d-grid gap-2 d-md-flex">
                        <button type="submit" class="btn btn-primary btn-lg">Add to Cart</button>
                        <a href="{{ route('products.index') }}" class="btn btn-outline-secondary btn-lg">Back to Products</a>
                    </div>
                </form>

                <div class="mt-4">
                    <div class="d-flex align-items-center">
                        <i class="bi bi-truck text-primary me-2"></i>
                        <span>Free shipping on orders over $50</span>
                    </div>
                    <div class="d-flex align-items-center mt-2">
                        <i class="bi bi-arrow-repeat text-primary me-2"></i>
                        <span>30-day money-back guarantee</span>
                    </div>
                    <div class="d-flex align-items-center mt-2">
                        <i class="bi bi-shield-check text-primary me-2"></i>
                        <span>Secure checkout</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Product Details Tabs -->
    <section class="py-5 bg-light">
        <div class="container">
            <div class="row">
                <div class="col-lg-10 mx-auto">
                    <ul class="nav nav-tabs" id="productTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="description-tab" data-bs-toggle="tab" data-bs-target="#description" type="button" role="tab" aria-controls="description" aria-selected="true">Description</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="ingredients-tab" data-bs-toggle="tab" data-bs-target="#ingredients" type="button" role="tab" aria-controls="ingredients" aria-selected="false">Ingredients</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="benefits-tab" data-bs-toggle="tab" data-bs-target="#benefits" type="button" role="tab" aria-controls="benefits" aria-selected="false">Benefits</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="how-to-use-tab" data-bs-toggle="tab" data-bs-target="#how-to-use" type="button" role="tab" aria-controls="how-to-use" aria-selected="false">How to Use</button>
                    </li>
                </ul>
                <div class="tab-content p-4 border border-top-0 rounded-bottom" id="productTabsContent">
                    <div class="tab-pane fade show active" id="description" role="tabpanel" aria-labelledby="description-tab">
                        {!! nl2br(e($product->description)) !!}
                    </div>
                    <div class="tab-pane fade" id="ingredients" role="tabpanel" aria-labelledby="ingredients-tab">
                        @if($product->ingredients)
                            {!! nl2br(e($product->ingredients)) !!}
                        @else
                            <p>Ingredient information not available.</p>
                        @endif
                    </div>
                    <div class="tab-pane fade" id="benefits" role="tabpanel" aria-labelledby="benefits-tab">
                        @if($product->benefits)
                            {!! nl2br(e($product->benefits)) !!}
                        @else
                            <p>Benefits information not available.</p>
                        @endif
                    </div>
                    <div class="tab-pane fade" id="how-to-use" role="tabpanel" aria-labelledby="how-to-use-tab">
                        @if($product->how_to_use)
                            {!! nl2br(e($product->how_to_use)) !!}
                        @else
                            <p>Usage information not available.</p>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Related Products Section -->
    @if(count($relatedProducts) > 0)
    <section class="py-5">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="fw-bold" style="color: #428677;">You May Also Like</h2>
                <p class="lead">Explore more products from our collection</p>
            </div>
            <div class="row g-4">
            @foreach($relatedProducts as $relatedProduct)
                <div class="col-md-6 col-lg-3">
                    <div class="card h-100 border-0 shadow-sm">
                        @if($relatedProduct->image)
                            <img src="{{ asset('storage/' . $relatedProduct->image) }}" class="card-img-top" alt="{{ $relatedProduct->name }}" style="height: 200px; object-fit: cover;">
                        @else
                            <img src="https://via.placeholder.com/300x200?text=NATRYON" class="card-img-top" alt="{{ $relatedProduct->name }}">
                        @endif
                        <div class="card-body">
                            <h5 class="card-title">{{ $relatedProduct->name }}</h5>
                            <p class="card-text">{{ Str::limit($relatedProduct->description, 100) }}</p>
                            <div class="d-flex justify-content-between align-items-center">
                                @php
                                    $relatedPrice = $relatedProduct->getPriceInCurrency($userCurrency);
                                    $relatedSubPrice = $relatedProduct->subscription_price ? \App\Services\CurrencyService::convert($relatedProduct->subscription_price, $relatedProduct->currency, $userCurrency) : null;
                                @endphp
                                @if(!$relatedProduct->subscription_only)
                                    <span class="fw-bold" style="color: #024C3D;">{{ $currencySymbol }}{{ number_format($relatedPrice, 2) }}</span>
                                @else
                                    <span class="fw-bold" style="color: #024C3D;">{{ $currencySymbol }}{{ number_format($relatedSubPrice, 2) }}/mo</span>
                                @endif
                                <a href="{{ route('products.show', $relatedProduct->slug) }}" class="btn btn-sm" style="background-color: #024C3D; color: white;">View Details</a>
                            </div>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
    </section>
    @endif
@endsection

@push('scripts')
    <!-- Swiper JS -->
    <script src="https://cdn.jsdelivr.net/npm/swiper@8/swiper-bundle.min.js"></script>

    <!-- Fancybox JS -->
    <script src="https://cdn.jsdelivr.net/npm/@fancyapps/ui@4.0/dist/fancybox.umd.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize Fancybox
            Fancybox.bind("[data-fancybox]", {
                // Options
                Thumbs: {
                    autoStart: true,
                },
                Toolbar: {
                    display: [
                        { id: "prev", position: "center" },
                        { id: "counter", position: "center" },
                        { id: "next", position: "center" },
                        "zoom",
                        "slideshow",
                        "fullscreen",
                        "download",
                        "close",
                    ],
                },
            });

            // Initialize thumbnail slider
            const galleryThumbs = new Swiper('.gallery-thumbs', {
                spaceBetween: 10,
                slidesPerView: 4,
                freeMode: true,
                watchSlidesProgress: true,
                breakpoints: {
                    // when window width is >= 320px
                    320: {
                        slidesPerView: 3,
                    },
                    // when window width is >= 480px
                    480: {
                        slidesPerView: 4,
                    },
                    // when window width is >= 640px
                    640: {
                        slidesPerView: 5,
                    }
                }
            });

            // Initialize main slider
            const galleryMain = new Swiper('.gallery-main', {
                spaceBetween: 10,
                navigation: {
                    nextEl: '.swiper-button-next',
                    prevEl: '.swiper-button-prev',
                },
                thumbs: {
                    swiper: galleryThumbs
                }
            });

            // Handle subscription checkbox for products with optional subscription
            const isSubscriptionCheckbox = document.getElementById('is_subscription');
            if (isSubscriptionCheckbox) {
                isSubscriptionCheckbox.addEventListener('change', function() {
                    // You can add additional logic here if needed
                    console.log('Subscription option changed:', this.checked);
                });
            }
        });
    </script>
@endpush
