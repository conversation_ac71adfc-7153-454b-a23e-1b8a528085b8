<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ShippingOptionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $shippingOptions = [
            [
                'name' => 'Free Shipping',
                'description' => 'Free shipping for all orders',
                'cost' => 0,
                'location_type' => 'all',
                'delivery_time_min' => 5,
                'delivery_time_max' => 7,
                'is_active' => true,
                'display_order' => 1,
            ],
            [
                'name' => 'Standard Shipping',
                'description' => 'Standard shipping with tracking',
                'cost' => 5.99,
                'location_type' => 'all',
                'delivery_time_min' => 3,
                'delivery_time_max' => 5,
                'is_active' => true,
                'display_order' => 2,
            ],
            [
                'name' => 'Express Shipping',
                'description' => 'Express shipping with tracking and delivery confirmation',
                'cost' => 12.99,
                'location_type' => 'all',
                'delivery_time_min' => 1,
                'delivery_time_max' => 2,
                'is_active' => true,
                'display_order' => 3,
            ],
            [
                'name' => 'International Shipping',
                'description' => 'International shipping with tracking',
                'cost' => 19.99,
                'location_type' => 'all',
                'delivery_time_min' => 7,
                'delivery_time_max' => 14,
                'is_active' => true,
                'display_order' => 4,
            ],
        ];

        foreach ($shippingOptions as $option) {
            \App\Models\ShippingOption::create($option);
        }
    }
}
