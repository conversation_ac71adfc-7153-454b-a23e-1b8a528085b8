@extends('layouts.admin')

@section('title', 'Releases')

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Releases</h1>
        <a href="{{ route('admin.releases.create') }}" class="btn btn-primary">
            <i class="bi bi-plus-circle"></i> New Release
        </a>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold">All Releases</h6>
        </div>
        <div class="card-body">
            @if($releases->isEmpty())
                <div class="alert alert-info">
                    No releases found. Click the "New Release" button to create one.
                </div>
            @else
                <div class="table-responsive">
                    <table class="table table-bordered table-hover" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>Version</th>
                                <th>Name</th>
                                <th>Status</th>
                                <th>Created By</th>
                                <th>Created At</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($releases as $release)
                                <tr>
                                    <td>{{ $release->version }}</td>
                                    <td>{{ $release->name }}</td>
                                    <td>
                                        @switch($release->status)
                                            @case('draft')
                                                <span class="badge bg-secondary">Draft</span>
                                                @break
                                            @case('testing')
                                                <span class="badge bg-info">Testing</span>
                                                @break
                                            @case('approved')
                                                <span class="badge bg-success">Approved</span>
                                                @break
                                            @case('deployed')
                                                <span class="badge bg-primary">Deployed</span>
                                                @break
                                            @case('failed')
                                                <span class="badge bg-danger">Failed</span>
                                                @break
                                            @default
                                                <span class="badge bg-secondary">{{ $release->status }}</span>
                                        @endswitch
                                    </td>
                                    <td>{{ $release->creator->name }}</td>
                                    <td>{{ $release->created_at->format('Y-m-d H:i') }}</td>
                                    <td>
                                        <a href="{{ route('admin.releases.show', $release->id) }}" class="btn btn-sm btn-info">
                                            <i class="bi bi-eye"></i> View
                                        </a>
                                        @if($release->status === 'draft')
                                            <a href="{{ route('admin.releases.edit', $release->id) }}" class="btn btn-sm btn-primary">
                                                <i class="bi bi-pencil"></i> Edit
                                            </a>
                                            <form action="{{ route('admin.releases.destroy', $release->id) }}" method="POST" class="d-inline">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to delete this release?')">
                                                    <i class="bi bi-trash"></i> Delete
                                                </button>
                                            </form>
                                        @endif
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                <div class="mt-4">
                    {{ $releases->links() }}
                </div>
            @endif
        </div>
    </div>
</div>
@endsection
