@extends('layouts.app')

@section('title', 'Affiliate Referrals - NatRyon')

@section('content')
    <!-- Hero Section -->
    <section class="py-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">Affiliate Referrals</h1>
            <a href="{{ route('affiliate.index') }}" class="btn btn-outline-primary">
                <i class="bi bi-arrow-left me-1"></i> Back to Dashboard
            </a>
        </div>
    </section>

    <!-- Referrals Section -->
    <section class="py-4">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white py-3">
                <h5 class="mb-0">Your Referrals</h5>
            </div>
            <div class="card-body">
                @if($affiliateCode)
                    <div class="alert alert-info mb-4">
                        <div class="d-flex">
                            <div class="flex-shrink-0">
                                <i class="bi bi-info-circle-fill"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h5 class="alert-heading">Your affiliate code: <strong>{{ $affiliateCode->code }}</strong></h5>
                                <p class="mb-0">Share this code with friends and earn {{ $affiliateCode->commission_rate }}% commission on their purchases!</p>
                            </div>
                        </div>
                    </div>
                    
                    @if(count($referrals) > 0)
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>User</th>
                                        <th>Joined Date</th>
                                        <th>Orders</th>
                                        <th>Total Spent</th>
                                        <th>Your Earnings</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($referrals as $referral)
                                        <tr>
                                            <td>{{ $referral->name }}</td>
                                            <td>{{ $referral->created_at->format('M d, Y') }}</td>
                                            <td>{{ $referral->orders->count() }}</td>
                                            <td>${{ number_format($referral->orders->sum('total'), 2) }}</td>
                                            <td>${{ number_format($referral->orders->sum('total') * $affiliateCode->commission_rate / 100, 2) }}</td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                        <div class="mt-4">
                            {{ $referrals->links() }}
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="bi bi-people" style="font-size: 3rem;"></i>
                            <h4 class="mt-3">No referrals yet</h4>
                            <p class="text-muted">Share your affiliate code to start referring friends.</p>
                        </div>
                    @endif
                @else
                    <div class="text-center py-4">
                        <i class="bi bi-tag" style="font-size: 3rem;"></i>
                        <h4 class="mt-3">No Affiliate Code Yet</h4>
                        <p class="text-muted">Generate your affiliate code to start referring friends.</p>
                        <form action="{{ route('affiliate.generate-code') }}" method="POST">
                            @csrf
                            <button type="submit" class="btn btn-primary mt-3">
                                Generate Affiliate Code
                            </button>
                        </form>
                    </div>
                @endif
            </div>
        </div>
    </section>
    
    <!-- Referral Tools Section -->
    <section class="py-4">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white py-3">
                <h5 class="mb-0">Referral Tools</h5>
            </div>
            <div class="card-body">
                @if($affiliateCode)
                    <div class="row">
                        <div class="col-md-6 mb-4">
                            <h6 class="fw-bold mb-3">Your Affiliate Link</h6>
                            <div class="input-group mb-2">
                                <input type="text" class="form-control" id="affiliate-link" value="{{ route('home') }}?ref={{ $affiliateCode->code }}" readonly>
                                <button class="btn btn-outline-secondary" type="button" onclick="copyToClipboard('affiliate-link')">
                                    <i class="bi bi-clipboard"></i>
                                </button>
                            </div>
                            <small class="text-muted">Share this link with friends to earn commissions.</small>
                        </div>
                        
                        <div class="col-md-6 mb-4">
                            <h6 class="fw-bold mb-3">Referral Code</h6>
                            <div class="input-group mb-2">
                                <input type="text" class="form-control" id="affiliate-code" value="{{ $affiliateCode->code }}" readonly>
                                <button class="btn btn-outline-secondary" type="button" onclick="copyToClipboard('affiliate-code')">
                                    <i class="bi bi-clipboard"></i>
                                </button>
                            </div>
                            <small class="text-muted">Share this code with friends to earn commissions.</small>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-12">
                            <h6 class="fw-bold mb-3">Social Media Sharing</h6>
                            <div class="d-flex gap-2">
                                <a href="https://www.facebook.com/sharer/sharer.php?u={{ urlencode(route('home') . '?ref=' . $affiliateCode->code) }}" target="_blank" class="btn btn-outline-primary">
                                    <i class="bi bi-facebook me-1"></i> Facebook
                                </a>
                                <a href="https://twitter.com/intent/tweet?text={{ urlencode('Check out NatRyon\'s premium greens powder supplements! Use my code ' . $affiliateCode->code . ' for a discount.') }}&url={{ urlencode(route('home') . '?ref=' . $affiliateCode->code) }}" target="_blank" class="btn btn-outline-primary">
                                    <i class="bi bi-twitter me-1"></i> Twitter
                                </a>
                                <a href="mailto:?subject={{ urlencode('Check out NatRyon\'s premium greens powder supplements!') }}&body={{ urlencode('I thought you might be interested in NatRyon\'s premium greens powder supplements. Use my referral code ' . $affiliateCode->code . ' when you checkout to get a discount! ' . route('home') . '?ref=' . $affiliateCode->code) }}" class="btn btn-outline-primary">
                                    <i class="bi bi-envelope me-1"></i> Email
                                </a>
                            </div>
                        </div>
                    </div>
                @else
                    <div class="text-center py-4">
                        <p class="text-muted">Generate your affiliate code to access referral tools.</p>
                    </div>
                @endif
            </div>
        </div>
    </section>
@endsection

@push('scripts')
<script>
    function copyToClipboard(elementId) {
        const element = document.getElementById(elementId);
        element.select();
        document.execCommand('copy');
        
        // Show a temporary tooltip
        const button = element.nextElementSibling;
        const originalHTML = button.innerHTML;
        button.innerHTML = '<i class="bi bi-check"></i>';
        setTimeout(() => {
            button.innerHTML = originalHTML;
        }, 2000);
    }
</script>
@endpush
