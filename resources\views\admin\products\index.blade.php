@extends('layouts.admin')

@section('title', 'Manage Products')

@section('content')
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div class="d-flex align-items-center">
            <div class="premium-icon-container me-3">
                <i class="bi bi-box-seam"></i>
            </div>
            <h1 class="h3 mb-0 fw-bold text-gradient">Products</h1>
        </div>
        <a href="{{ route('admin.products.create') }}" class="btn btn-primary rounded-pill px-4 shadow-sm">
            <i class="bi bi-plus-circle me-2"></i> Add New Product
        </a>
    </div>

    <div class="card premium-card shadow-lg">
        <div class="card-header bg-transparent border-0 pt-4 pb-0 px-4">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <div class="premium-icon-container me-3">
                        <i class="bi bi-grid-3x3"></i>
                    </div>
                    <h5 class="card-title fw-bold mb-0">Product Catalog</h5>
                </div>
                <div class="checkpoint-info">
                    <span class="badge checkpoint-badge">
                        <i class="bi bi-check2-all me-1"></i> Checkpoint
                    </span>
                    <span class="text-muted ms-2 small">Last updated {{ now()->subHours(rand(1, 12))->diffForHumans() }}</span>
                </div>
            </div>
        </div>
        <div class="card-body p-4">
            <div class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center mb-4 gap-3">
                <form action="{{ route('admin.products.index') }}" method="GET" class="search-form">
                    <div class="input-group">
                        <span class="input-group-text bg-transparent border-end-0">
                            <i class="bi bi-search text-muted"></i>
                        </span>
                        <input type="text" name="search" class="form-control border-start-0 ps-0" placeholder="Search products..." value="{{ request('search') }}">
                        <button type="submit" class="btn btn-primary rounded-end">
                            Search
                        </button>
                    </div>
                </form>
                <div class="d-flex gap-2">
                    <div class="dropdown">
                        <button class="btn btn-outline-secondary rounded-pill px-3 dropdown-toggle" type="button" id="filterDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi bi-funnel me-2"></i> Filter
                            @if(request('category'))
                                <span class="badge bg-primary ms-1">1</span>
                            @endif
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end shadow-sm border-0" aria-labelledby="filterDropdown">
                            <li><h6 class="dropdown-header">Categories</h6></li>
                            @foreach($categories ?? [] as $category)
                                <li>
                                    <a class="dropdown-item {{ request('category') == $category->id ? 'active' : '' }}" href="{{ route('admin.products.index', ['category' => $category->id]) }}">
                                        <i class="bi bi-tag me-2"></i>{{ $category->name }}
                                    </a>
                                </li>
                            @endforeach
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ route('admin.products.index') }}">
                                <i class="bi bi-x-circle me-2"></i>Clear Filters
                            </a></li>
                        </ul>
                    </div>
                    <div class="dropdown">
                        <button class="btn btn-outline-secondary rounded-pill px-3 dropdown-toggle" type="button" id="sortDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi bi-sort-down me-2"></i> Sort
                            @if(request('sort'))
                                <span class="badge bg-primary ms-1">1</span>
                            @endif
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end shadow-sm border-0" aria-labelledby="sortDropdown">
                            <li><a class="dropdown-item {{ request('sort') == 'name_asc' ? 'active' : '' }}" href="{{ route('admin.products.index', ['sort' => 'name_asc']) }}">
                                <i class="bi bi-sort-alpha-down me-2"></i>Name (A-Z)
                            </a></li>
                            <li><a class="dropdown-item {{ request('sort') == 'name_desc' ? 'active' : '' }}" href="{{ route('admin.products.index', ['sort' => 'name_desc']) }}">
                                <i class="bi bi-sort-alpha-up me-2"></i>Name (Z-A)
                            </a></li>
                            <li><a class="dropdown-item {{ request('sort') == 'price_asc' ? 'active' : '' }}" href="{{ route('admin.products.index', ['sort' => 'price_asc']) }}">
                                <i class="bi bi-sort-numeric-down me-2"></i>Price (Low to High)
                            </a></li>
                            <li><a class="dropdown-item {{ request('sort') == 'price_desc' ? 'active' : '' }}" href="{{ route('admin.products.index', ['sort' => 'price_desc']) }}">
                                <i class="bi bi-sort-numeric-up me-2"></i>Price (High to Low)
                            </a></li>
                            <li><a class="dropdown-item {{ request('sort') == 'newest' ? 'active' : '' }}" href="{{ route('admin.products.index', ['sort' => 'newest']) }}">
                                <i class="bi bi-calendar-check me-2"></i>Newest
                            </a></li>
                            <li><a class="dropdown-item {{ request('sort') == 'oldest' ? 'active' : '' }}" href="{{ route('admin.products.index', ['sort' => 'oldest']) }}">
                                <i class="bi bi-calendar me-2"></i>Oldest
                            </a></li>
                        </ul>
                    </div>
                </div>
            </div>

            @if(isset($products) && count($products) > 0)
                <div class="table-responsive">
                    <table class="table premium-table table-hover align-middle">
                        <thead>
                            <tr>
                                <th style="width: 60px;">ID</th>
                                <th style="width: 80px;">Image</th>
                                <th>Name</th>
                                <th>Category</th>
                                <th>Price</th>
                                <th>Stock</th>
                                <th>Status</th>
                                <th style="width: 150px;" class="text-end">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($products as $product)
                                <tr class="product-row">
                                    <td>
                                        <span class="product-id-badge">{{ $product->id }}</span>
                                    </td>
                                    <td>
                                        <div class="product-image">
                                            @if($product->image)
                                                <img src="{{ asset('storage/' . $product->image) }}" alt="{{ $product->name }}">
                                            @else
                                                <div class="product-image-placeholder">
                                                    <i class="bi bi-image"></i>
                                                </div>
                                            @endif
                                            @if($product->is_featured)
                                                <div class="featured-badge" title="Featured Product">
                                                    <i class="bi bi-star-fill"></i>
                                                </div>
                                            @endif
                                        </div>
                                    </td>
                                    <td>
                                        <div class="product-name">
                                            <span class="fw-medium">{{ $product->name }}</span>
                                            @if($product->allow_subscription)
                                                <span class="subscription-badge ms-2" title="{{ $product->subscription_only ? 'Subscription Only' : 'Subscription Available' }}">
                                                    <i class="bi bi-arrow-repeat"></i>
                                                </span>
                                            @endif
                                        </div>
                                        <div class="product-slug text-muted small">
                                            {{ $product->slug }}
                                        </div>
                                    </td>
                                    <td>
                                        <span class="category-badge">
                                            <i class="bi bi-tag-fill me-1"></i>
                                            {{ $product->category->name ?? 'Uncategorized' }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="product-price">
                                            <span class="fw-bold">${{ number_format($product->price, 2) }}</span>
                                            @if($product->sale_price)
                                                <span class="sale-price">${{ number_format($product->sale_price, 2) }}</span>
                                            @endif
                                        </div>
                                    </td>
                                    <td>
                                        @if($product->stock > 10)
                                            <span class="stock-badge in-stock">
                                                <i class="bi bi-check-circle-fill me-1"></i>{{ $product->stock }}
                                            </span>
                                        @elseif($product->stock > 0)
                                            <span class="stock-badge low-stock">
                                                <i class="bi bi-exclamation-circle-fill me-1"></i>{{ $product->stock }}
                                            </span>
                                        @else
                                            <span class="stock-badge out-of-stock">
                                                <i class="bi bi-x-circle-fill me-1"></i>Out of Stock
                                            </span>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input status-toggle" type="checkbox" id="status{{ $product->id }}"
                                                {{ $product->is_active ? 'checked' : '' }}
                                                data-product-id="{{ $product->id }}"
                                                data-product-name="{{ $product->name }}"
                                                data-current-status="{{ $product->is_active ? 1 : 0 }}">
                                            <form id="toggleForm{{ $product->id }}" action="{{ route('admin.products.update', $product->id) }}" method="POST" class="d-none">
                                                @csrf
                                                @method('PUT')
                                                <input type="hidden" name="is_active" value="{{ $product->is_active ? 0 : 1 }}">
                                                <input type="hidden" name="status_toggle_only" value="1">
                                            </form>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex gap-2 justify-content-end">
                                            <a href="{{ route('admin.products.edit', $product->id) }}" class="btn btn-sm btn-action edit-btn" title="Edit Product">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                            <a href="{{ route('admin.products.show', $product->id) }}" class="btn btn-sm btn-action view-btn" title="View Product">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-action delete-btn delete-product-btn"
                                                data-product-id="{{ $product->id }}"
                                                data-product-name="{{ $product->name }}"
                                                title="Delete Product">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                            <form id="deleteForm{{ $product->id }}" action="{{ route('admin.products.destroy', $product->id) }}" method="POST" class="d-none">
                                                @csrf
                                                @method('DELETE')
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <div class="d-flex flex-column flex-md-row justify-content-between align-items-center mt-4 gap-3">
                    <div class="pagination-info">
                        <span class="badge bg-light text-dark px-3 py-2">
                            <i class="bi bi-info-circle me-1"></i> Showing {{ $products->firstItem() ?? 0 }} to {{ $products->lastItem() ?? 0 }} of {{ $products->total() ?? 0 }} products
                        </span>
                    </div>
                    <div class="premium-pagination">
                        {{ $products->withQueryString()->links() }}
                    </div>
                </div>
            @else
                <div class="empty-state py-5">
                    <div class="empty-state-icon">
                        <i class="bi bi-box-seam"></i>
                    </div>
                    <h4 class="mt-4 mb-2">No products found</h4>
                    <p class="text-muted mb-4">
                        @if(request('search'))
                            No products match your search criteria. <a href="{{ route('admin.products.index') }}" class="text-primary">Clear search</a>
                        @elseif(request('category'))
                            No products in this category. <a href="{{ route('admin.products.index') }}" class="text-primary">View all products</a>
                        @else
                            Start by adding your first product to your catalog.
                        @endif
                    </p>
                    <a href="{{ route('admin.products.create') }}" class="btn btn-primary rounded-pill px-4 mt-2">
                        <i class="bi bi-plus-circle me-2"></i> Add New Product
                    </a>
                </div>
            @endif
        </div>
    </div>
@endsection

@push('styles')
<style>
    /* Premium Product Table Styles */
    .premium-table {
        border-collapse: separate;
        border-spacing: 0;
        margin-bottom: 0;
    }

    .premium-table thead th {
        background-color: #f8f9fa;
        border-bottom: none;
        padding: 15px;
        font-weight: 600;
        color: #495057;
        text-transform: uppercase;
        font-size: 0.8rem;
        letter-spacing: 0.5px;
    }

    .premium-table tbody tr {
        transition: all 0.3s ease;
        border-bottom: 1px solid #f1f1f1;
    }

    .premium-table tbody tr:hover {
        background-color: rgba(66, 134, 119, 0.05);
    }

    .product-row td {
        padding: 15px;
        vertical-align: middle;
    }

    /* Product ID Badge */
    .product-id-badge {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        min-width: 32px;
        height: 32px;
        background-color: #f8f9fa;
        color: #6c757d;
        border-radius: 6px;
        font-size: 12px;
        font-weight: 600;
        padding: 0 8px;
    }

    /* Product Image */
    .product-image {
        width: 60px;
        height: 60px;
        border-radius: 12px;
        overflow: hidden;
        position: relative;
        border: 2px solid #f1f1f1;
        background-color: #f8f9fa;
    }

    .product-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .product-image-placeholder {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #adb5bd;
        font-size: 1.5rem;
    }

    .featured-badge {
        position: absolute;
        top: -2px;
        right: -2px;
        width: 20px;
        height: 20px;
        background-color: #ffc107;
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 10px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    /* Product Name */
    .product-name {
        font-weight: 600;
        color: var(--primary-color);
        display: flex;
        align-items: center;
    }

    .product-slug {
        font-size: 12px;
        color: #6c757d;
        margin-top: 3px;
    }

    .subscription-badge {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 20px;
        height: 20px;
        background-color: rgba(66, 134, 119, 0.1);
        color: var(--primary-color);
        border-radius: 50%;
        font-size: 10px;
    }

    /* Category Badge */
    .category-badge {
        display: inline-flex;
        align-items: center;
        padding: 6px 12px;
        background-color: rgba(36, 68, 90, 0.1);
        color: var(--darker-color);
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
    }

    /* Product Price */
    .product-price {
        display: flex;
        flex-direction: column;
    }

    .sale-price {
        font-size: 12px;
        color: #dc3545;
        text-decoration: line-through;
        margin-top: 3px;
    }

    /* Stock Badge */
    .stock-badge {
        display: inline-flex;
        align-items: center;
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
    }

    .stock-badge.in-stock {
        background-color: rgba(46, 125, 50, 0.1);
        color: #2e7d32;
    }

    .stock-badge.low-stock {
        background-color: rgba(237, 108, 2, 0.1);
        color: #ed6c02;
    }

    .stock-badge.out-of-stock {
        background-color: rgba(211, 47, 47, 0.1);
        color: #d32f2f;
    }

    /* Status Toggle */
    .form-check-input.status-toggle {
        width: 40px;
        height: 20px;
        cursor: pointer;
    }

    .form-check-input.status-toggle:checked {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
    }

    /* Action Buttons */
    .btn-action {
        width: 32px;
        height: 32px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        background-color: #f8f9fa;
        color: #6c757d;
        border: none;
    }

    .btn-action:hover {
        transform: translateY(-3px);
    }

    .btn-action.edit-btn:hover {
        background-color: var(--primary-color);
        color: white;
    }

    .btn-action.view-btn:hover {
        background-color: var(--secondary-color);
        color: white;
    }

    .btn-action.delete-btn:hover {
        background-color: #dc3545;
        color: white;
    }

    /* Pagination */
    .pagination-info {
        font-size: 0.9rem;
        color: #6c757d;
    }

    .premium-pagination .pagination {
        margin-bottom: 0;
    }

    .premium-pagination .page-item .page-link {
        border-radius: 8px;
        margin: 0 3px;
        color: var(--primary-color);
        border: none;
        background-color: #f8f9fa;
        font-weight: 500;
        padding: 8px 16px;
    }

    .premium-pagination .page-item.active .page-link {
        background-color: var(--primary-color);
        color: white;
    }

    .premium-pagination .page-item .page-link:hover {
        background-color: rgba(66, 134, 119, 0.1);
    }

    /* Empty State */
    .empty-state {
        text-align: center;
        padding: 40px 20px;
    }

    .empty-state-icon {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background-color: #f8f9fa;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto;
        color: #adb5bd;
        font-size: 2rem;
    }

    /* Search Form */
    .search-form {
        width: 100%;
        max-width: 400px;
    }

    .search-form .input-group {
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        border-radius: 8px;
        overflow: hidden;
    }

    /* Text Gradient */
    .text-gradient {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--darker-color) 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-fill-color: transparent;
    }
</style>
@endpush

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Handle delete product buttons
        const deleteButtons = document.querySelectorAll('.delete-product-btn');
        deleteButtons.forEach(button => {
            button.addEventListener('click', function() {
                const productId = this.dataset.productId;
                const productName = this.dataset.productName;

                Swal.fire({
                    title: 'Are you sure?',
                    html: `You are about to delete <strong>${productName}</strong>.<br><span class="text-danger">This action cannot be undone!</span>`,
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#d33',
                    cancelButtonColor: '#3085d6',
                    confirmButtonText: 'Yes, delete it!',
                    cancelButtonText: 'Cancel',
                    customClass: {
                        confirmButton: 'btn btn-danger',
                        cancelButton: 'btn btn-secondary'
                    },
                    buttonsStyling: false
                }).then((result) => {
                    if (result.isConfirmed) {
                        document.getElementById(`deleteForm${productId}`).submit();
                    }
                });
            });
        });

        // Handle status toggle switches
        const statusToggles = document.querySelectorAll('.status-toggle');
        statusToggles.forEach(toggle => {
            toggle.addEventListener('change', function() {
                const productId = this.dataset.productId;
                const productName = this.dataset.productName;
                const newStatus = this.checked ? 'active' : 'inactive';

                Swal.fire({
                    title: 'Confirm Status Change',
                    html: `Are you sure you want to mark <strong>${productName}</strong> as <strong>${newStatus}</strong>?`,
                    icon: 'question',
                    showCancelButton: true,
                    confirmButtonColor: '#428677',
                    cancelButtonColor: '#6c757d',
                    confirmButtonText: 'Yes, update it!',
                    cancelButtonText: 'Cancel',
                    customClass: {
                        confirmButton: 'btn btn-primary',
                        cancelButton: 'btn btn-secondary'
                    },
                    buttonsStyling: false
                }).then((result) => {
                    if (result.isConfirmed) {
                        document.getElementById(`toggleForm${productId}`).submit();
                    } else {
                        // Revert the toggle if canceled
                        this.checked = !this.checked;
                    }
                });
            });
        });
    });
</script>
@endpush
