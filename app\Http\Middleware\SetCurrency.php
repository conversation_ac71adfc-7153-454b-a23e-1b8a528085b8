<?php

namespace App\Http\Middleware;

use App\Services\CurrencyService;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class SetCurrency
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if currency is being changed via request
        if ($request->has('currency')) {
            $currencyCode = strtoupper($request->get('currency'));
            CurrencyService::setUserCurrency($currencyCode);
        }

        // Set current currency in view
        view()->share('currentCurrency', CurrencyService::getUserCurrency());
        view()->share('currentCurrencySymbol', CurrencyService::getSymbol());
        view()->share('activeCurrencies', CurrencyService::getActiveCurrencies());

        return $next($request);
    }
}
