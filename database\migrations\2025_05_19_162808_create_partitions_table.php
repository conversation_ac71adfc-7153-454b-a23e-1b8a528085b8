<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('partitions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('release_id')->constrained()->onDelete('cascade');
            $table->string('name');
            $table->text('description')->nullable();
            $table->text('file_paths')->nullable(); // JSON array of file paths included in this partition
            $table->enum('type', ['frontend', 'backend', 'database', 'config', 'assets', 'other'])->default('other');
            $table->integer('deployment_order')->default(0);
            $table->enum('status', ['pending', 'deployed', 'failed', 'rolled_back'])->default('pending');
            $table->text('deployment_notes')->nullable();
            $table->timestamp('deployed_at')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('partitions');
    }
};
