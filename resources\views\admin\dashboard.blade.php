@extends('layouts.admin')

@section('title', 'Dashboard')

@section('content')
    <!-- Welcome Banner -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 premium-gradient text-white shadow-xl rounded-4 overflow-hidden">
                <div class="card-body p-4 position-relative">
                    <!-- Decorative elements -->
                    <div class="position-absolute top-0 end-0 mt-3 me-3">
                        <div class="premium-badge">
                            <span>ELITE</span>
                        </div>
                    </div>
                    <div class="position-absolute top-0 start-0 w-100 h-100 premium-overlay"></div>

                    <div class="row align-items-center position-relative">
                        <div class="col-md-8">
                            <h2 class="fw-bold mb-3 display-5 text-gradient">Welcome to NATRYON Dashboard</h2>
                            <p class="mb-0 opacity-90 lead">Track your business performance and manage your products, orders, and customers all in one place.</p>
                            <div class="mt-4 d-none d-md-block">
                                <a href="{{ route('admin.checkpoints.index') }}" class="btn btn-light btn-lg rounded-pill px-4 me-2 shadow-sm">
                                    <i class="bi bi-check2-circle me-2"></i>Checkpoints
                                </a>
                                <a href="{{ route('admin.orders.index') }}" class="btn btn-outline-light btn-lg rounded-pill px-4 shadow-sm">
                                    <i class="bi bi-cart me-2"></i>Orders
                                </a>
                            </div>
                        </div>
                        <div class="col-md-4 text-md-end mt-4 mt-md-0">
                            <div class="dashboard-icon-container">
                                <div class="dashboard-icon-bg"></div>
                                <div class="dashboard-icon">
                                    <i class="bi bi-speedometer2"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-footer premium-gradient-footer py-3 position-relative">
                    <div class="row text-center">
                        <div class="col-md-3 col-6 border-end border-white border-opacity-25">
                            <div class="stat-container">
                                <h5 class="mb-0 fw-bold stat-value">${{ number_format($totalRevenue, 2) }}</h5>
                                <small class="stat-label">Total Revenue</small>
                                <div class="stat-icon"><i class="bi bi-currency-euro"></i></div>
                            </div>
                        </div>
                        <div class="col-md-3 col-6 border-end border-white border-opacity-25">
                            <div class="stat-container">
                                <h5 class="mb-0 fw-bold stat-value">{{ $totalOrders }}</h5>
                                <small class="stat-label">Total Orders</small>
                                <div class="stat-icon"><i class="bi bi-bag-check"></i></div>
                            </div>
                        </div>
                        <div class="col-md-3 col-6 mt-3 mt-md-0">
                            <div class="stat-container">
                                <h5 class="mb-0 fw-bold stat-value">{{ $totalProducts }}</h5>
                                <small class="stat-label">Products</small>
                                <div class="stat-icon"><i class="bi bi-box-seam"></i></div>
                            </div>
                        </div>
                        <div class="col-md-3 col-6 mt-3 mt-md-0">
                            <div class="stat-container">
                                <h5 class="mb-0 fw-bold stat-value">{{ $totalUsers }}</h5>
                                <small class="stat-label">Customers</small>
                                <div class="stat-icon"><i class="bi bi-people"></i></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Revenue Stats -->
    <div class="row g-4 mb-4">
        <div class="col-md-6">
            <div class="card border-0 shadow-lg rounded-4 h-100 premium-card">
                <div class="card-header bg-transparent border-0 pt-4 pb-0 px-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <div class="premium-icon-container me-3">
                                <i class="bi bi-currency-euro"></i>
                            </div>
                            <h5 class="card-title fw-bold mb-0">Revenue Overview</h5>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-dark rounded-pill px-3" type="button" id="revenueDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="bi bi-three-dots"></i>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end shadow-sm border-0" aria-labelledby="revenueDropdown">
                                <li><a class="dropdown-item" href="{{ route('admin.orders.index', ['status' => 'completed']) }}"><i class="bi bi-check-circle me-2 text-success"></i>View Completed Orders</a></li>
                                <li><a class="dropdown-item" href="#"><i class="bi bi-file-earmark-arrow-down me-2 text-primary"></i>Export Revenue Report</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#"><i class="bi bi-gear me-2"></i>Revenue Settings</a></li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="card-body p-4">
                    <div class="row text-center mb-4">
                        <div class="col-6">
                            <div class="revenue-card this-month">
                                <div class="revenue-card-inner">
                                    <div class="revenue-icon">
                                        <i class="bi bi-calendar-check"></i>
                                    </div>
                                    <h3 class="revenue-amount">${{ number_format($thisMonthRevenue, 2) }}</h3>
                                    <p class="revenue-label">This Month</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="revenue-card last-month">
                                <div class="revenue-card-inner">
                                    <div class="revenue-icon">
                                        <i class="bi bi-calendar-minus"></i>
                                    </div>
                                    <h3 class="revenue-amount">${{ number_format($lastMonthRevenue, 2) }}</h3>
                                    <p class="revenue-label">Last Month</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="checkpoint-container">
                        <div class="checkpoint-label d-flex justify-content-between mb-2">
                            <span class="text-muted">Monthly Target</span>
                            <span class="fw-medium">$10,000.00</span>
                        </div>
                        <div class="progress premium-progress" style="height: 10px;">
                            <div class="progress-bar progress-gradient" role="progressbar"
                                style="width: {{ min(100, max(5, ($thisMonthRevenue/10000)*100)) }}%"
                                aria-valuenow="{{ min(100, max(5, ($thisMonthRevenue/10000)*100)) }}"
                                aria-valuemin="0"
                                aria-valuemax="100">
                            </div>
                        </div>
                        <div class="d-flex justify-content-between mt-2">
                            <div class="checkpoint-status">
                                <span class="badge {{ $revenueGrowth >= 0 ? 'growth-positive' : 'growth-negative' }} rounded-pill px-3 py-2">
                                    <i class="bi {{ $revenueGrowth >= 0 ? 'bi-graph-up-arrow' : 'bi-graph-down-arrow' }} me-1"></i>
                                    {{ number_format(abs($revenueGrowth), 1) }}%
                                </span>
                                <span class="ms-2 text-muted small">vs last month</span>
                            </div>
                            <div class="checkpoint-remaining">
                                <span class="small text-muted">{{ round(($thisMonthRevenue/10000)*100) }}% of target</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card border-0 shadow-lg rounded-4 h-100 premium-card">
                <div class="card-header bg-transparent border-0 pt-4 pb-0 px-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <div class="premium-icon-container me-3">
                                <i class="bi bi-cart-check"></i>
                            </div>
                            <h5 class="card-title fw-bold mb-0">Order Status</h5>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-dark rounded-pill px-3" type="button" id="orderDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="bi bi-three-dots"></i>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end shadow-sm border-0" aria-labelledby="orderDropdown">
                                <li><a class="dropdown-item" href="{{ route('admin.orders.index') }}"><i class="bi bi-list-ul me-2 text-primary"></i>View All Orders</a></li>
                                <li><a class="dropdown-item" href="{{ route('admin.orders.index', ['status' => 'pending']) }}"><i class="bi bi-hourglass me-2 text-warning"></i>View Pending Orders</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#"><i class="bi bi-gear me-2"></i>Order Settings</a></li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="card-body p-4">
                    <div class="row g-3">
                        <div class="col-4">
                            <div class="order-status-card pending">
                                <div class="order-status-icon">
                                    <i class="bi bi-hourglass-split"></i>
                                </div>
                                <div class="order-status-content">
                                    <h3 class="order-status-count">{{ $pendingOrders }}</h3>
                                    <p class="order-status-label">Pending</p>
                                </div>
                                <div class="order-status-badge">
                                    <span class="badge rounded-pill">{{ round(($pendingOrders/max(1, $totalOrders))*100) }}%</span>
                                </div>
                                <a href="{{ route('admin.orders.index', ['status' => 'pending']) }}" class="order-status-link">
                                    <i class="bi bi-arrow-right-circle"></i>
                                </a>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="order-status-card processing">
                                <div class="order-status-icon">
                                    <i class="bi bi-gear-wide-connected"></i>
                                </div>
                                <div class="order-status-content">
                                    <h3 class="order-status-count">{{ $processingOrders }}</h3>
                                    <p class="order-status-label">Processing</p>
                                </div>
                                <div class="order-status-badge">
                                    <span class="badge rounded-pill">{{ round(($processingOrders/max(1, $totalOrders))*100) }}%</span>
                                </div>
                                <a href="{{ route('admin.orders.index', ['status' => 'processing']) }}" class="order-status-link">
                                    <i class="bi bi-arrow-right-circle"></i>
                                </a>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="order-status-card completed">
                                <div class="order-status-icon">
                                    <i class="bi bi-check-circle"></i>
                                </div>
                                <div class="order-status-content">
                                    <h3 class="order-status-count">{{ $completedOrders }}</h3>
                                    <p class="order-status-label">Completed</p>
                                </div>
                                <div class="order-status-badge">
                                    <span class="badge rounded-pill">{{ round(($completedOrders/max(1, $totalOrders))*100) }}%</span>
                                </div>
                                <a href="{{ route('admin.orders.index', ['status' => 'completed']) }}" class="order-status-link">
                                    <i class="bi bi-arrow-right-circle"></i>
                                </a>
                            </div>
                        </div>
                    </div>

                    <div class="checkpoint-container mt-4">
                        <div class="checkpoint-label d-flex justify-content-between mb-2">
                            <span class="text-muted">Order Completion Rate</span>
                            <span class="fw-medium">{{ round(($completedOrders/max(1, $totalOrders))*100) }}%</span>
                        </div>
                        <div class="progress premium-progress" style="height: 10px;">
                            <div class="progress-bar progress-gradient-blue" role="progressbar"
                                style="width: {{ round(($completedOrders/max(1, $totalOrders))*100) }}%"
                                aria-valuenow="{{ round(($completedOrders/max(1, $totalOrders))*100) }}"
                                aria-valuemin="0"
                                aria-valuemax="100">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row g-4">
        <!-- Recent Orders -->
        <div class="col-lg-8">
            <div class="card border-0 shadow-lg rounded-4 premium-card">
                <div class="card-header bg-transparent border-0 pt-4 pb-0 px-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <div class="premium-icon-container me-3">
                                <i class="bi bi-receipt"></i>
                            </div>
                            <h5 class="card-title fw-bold mb-0">Recent Orders</h5>
                        </div>
                        <a href="{{ route('admin.orders.index') }}" class="btn btn-primary btn-sm rounded-pill px-4 shadow-sm">
                            <i class="bi bi-eye me-1"></i> View All
                        </a>
                    </div>
                </div>
                <div class="card-body p-0 mt-3">
                    <div class="table-responsive">
                        <table class="table table-hover align-middle mb-0 premium-table">
                            <thead>
                                <tr>
                                    <th class="ps-4">Order #</th>
                                    <th>Customer</th>
                                    <th>Date</th>
                                    <th>Status</th>
                                    <th>Total</th>
                                    <th class="text-end pe-4">Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($recentOrders as $order)
                                    <tr class="order-row">
                                        <td class="ps-4 fw-medium">
                                            <div class="d-flex align-items-center">
                                                <span class="order-number-badge me-2">#</span>
                                                {{ $order->order_number }}
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-circle me-2">
                                                    {{ strtoupper(substr($order->user->name, 0, 1)) }}
                                                </div>
                                                <span class="fw-medium">{{ $order->user->name }}</span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <i class="bi bi-calendar3 me-2 text-muted"></i>
                                                {{ $order->created_at->format('M d, Y') }}
                                            </div>
                                        </td>
                                        <td>
                                            @if($order->status == 'pending')
                                                <span class="status-badge pending">
                                                    <i class="bi bi-hourglass-split me-1"></i>Pending
                                                </span>
                                            @elseif($order->status == 'processing')
                                                <span class="status-badge processing">
                                                    <i class="bi bi-gear-wide-connected me-1"></i>Processing
                                                </span>
                                            @elseif($order->status == 'completed')
                                                <span class="status-badge completed">
                                                    <i class="bi bi-check-circle me-1"></i>Completed
                                                </span>
                                            @elseif($order->status == 'declined')
                                                <span class="status-badge declined">
                                                    <i class="bi bi-x-circle me-1"></i>Declined
                                                </span>
                                            @endif
                                        </td>
                                        <td class="fw-medium">${{ number_format($order->total, 2) }}</td>
                                        <td class="text-end pe-4">
                                            <a href="{{ route('admin.orders.show', $order->id) }}" class="btn btn-sm btn-outline-primary rounded-pill px-3 action-btn">
                                                <i class="bi bi-eye me-1"></i>View
                                            </a>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="6" class="text-center py-5">
                                            <div class="empty-state">
                                                <div class="empty-state-icon">
                                                    <i class="bi bi-inbox"></i>
                                                </div>
                                                <h6 class="mt-3">No Orders Found</h6>
                                                <p class="text-muted small">New orders will appear here when customers make purchases.</p>
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="card-footer bg-transparent border-0 p-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="checkpoint-info">
                            <span class="badge checkpoint-badge">
                                <i class="bi bi-check2-all me-1"></i> Checkpoint
                            </span>
                            <span class="text-muted ms-2 small">Last order processed {{ now()->subHours(rand(1, 24))->diffForHumans() }}</span>
                        </div>
                        <div>
                            <a href="{{ route('admin.orders.create') }}" class="btn btn-sm btn-outline-primary rounded-pill">
                                <i class="bi bi-plus-circle me-1"></i> New Order
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Top Products -->
        <div class="col-lg-4">
            <div class="card border-0 shadow-lg rounded-4 premium-card">
                <div class="card-header bg-transparent border-0 pt-4 pb-0 px-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <div class="premium-icon-container me-3">
                                <i class="bi bi-trophy"></i>
                            </div>
                            <h5 class="card-title fw-bold mb-0">Top Products</h5>
                        </div>
                        <a href="{{ route('admin.products.index') }}" class="btn btn-primary btn-sm rounded-pill px-4 shadow-sm">
                            <i class="bi bi-eye me-1"></i> View All
                        </a>
                    </div>
                </div>
                <div class="card-body p-0 mt-3">
                    <ul class="list-group list-group-flush premium-list">
                        @forelse($topProducts as $index => $product)
                            <li class="list-group-item border-0 px-4 py-3 product-item">
                                <div class="d-flex align-items-center">
                                    <div class="product-rank">
                                        <span>{{ $index + 1 }}</span>
                                    </div>
                                    <div class="product-image">
                                        @if($product->image)
                                            <img src="{{ asset('storage/' . $product->image) }}" alt="{{ $product->name }}">
                                        @else
                                            <div class="product-image-placeholder">
                                                <i class="bi bi-box-seam"></i>
                                            </div>
                                        @endif
                                    </div>
                                    <div class="product-info">
                                        <h6 class="product-name">{{ $product->name }}</h6>
                                        <div class="product-meta">
                                            <span class="product-orders">
                                                <i class="bi bi-bag-check me-1"></i>{{ $product->order_items_count }} orders
                                            </span>
                                            <span class="product-price">
                                                ${{ number_format($product->price, 2) }}
                                            </span>
                                        </div>
                                    </div>
                                    <div class="product-actions">
                                        <a href="{{ route('admin.products.edit', $product->id) }}" class="btn btn-sm btn-icon">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                    </div>
                                </div>
                            </li>
                        @empty
                            <li class="list-group-item border-0 px-4 py-5 text-center">
                                <div class="empty-state">
                                    <div class="empty-state-icon">
                                        <i class="bi bi-box"></i>
                                    </div>
                                    <h6 class="mt-3">No Products Found</h6>
                                    <p class="text-muted small">Add products to see them ranked here.</p>
                                </div>
                            </li>
                        @endforelse
                    </ul>
                </div>
                <div class="card-footer bg-transparent border-0 p-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="checkpoint-info">
                            <span class="badge checkpoint-badge">
                                <i class="bi bi-check2-all me-1"></i> Checkpoint
                            </span>
                            <span class="text-muted ms-2 small">Updated {{ now()->subHours(rand(1, 12))->diffForHumans() }}</span>
                        </div>
                        <div>
                            <a href="{{ route('admin.products.create') }}" class="btn btn-sm btn-outline-primary rounded-pill">
                                <i class="bi bi-plus-circle me-1"></i> New Product
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('styles')
<style>
    /* NATRYON Premium Admin Dashboard Styles */
    :root {
        --primary-color: #428677;
        --primary-dark: #2d504b;
        --primary-light: #8ab0a6;
        --secondary-color: #24445a;
        --secondary-light: #7a868d;
        --light-gray: #b1b7b8;
        --success-color: #2e7d32;
        --warning-color: #ed6c02;
        --info-color: #0288d1;
        --danger-color: #d32f2f;
        --card-border-radius: 0.75rem;
        --transition-speed: 0.3s;
    }

    /* General Card Styles */
    .rounded-4 {
        border-radius: var(--card-border-radius) !important;
    }

    .premium-card {
        transition: transform var(--transition-speed) ease-in-out, box-shadow var(--transition-speed) ease-in-out;
        background-color: #ffffff;
        border: none;
        overflow: hidden;
    }

    .premium-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1) !important;
    }

    .shadow-xl {
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15) !important;
    }

    /* Premium Gradient Styles */
    .premium-gradient {
        background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);
        position: relative;
        overflow: hidden;
    }

    .premium-gradient-footer {
        background: linear-gradient(135deg, rgba(45, 80, 75, 0.9) 0%, rgba(66, 134, 119, 0.9) 100%);
        backdrop-filter: blur(5px);
    }

    .premium-overlay {
        background: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiPjxkZWZzPjxwYXR0ZXJuIGlkPSJwYXR0ZXJuIiB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgcGF0dGVyblVuaXRzPSJ1c2VyU3BhY2VPblVzZSIgcGF0dGVyblRyYW5zZm9ybT0icm90YXRlKDQ1KSI+PHJlY3QgaWQ9InBhdHRlcm4tYmFja2dyb3VuZCIgd2lkdGg9IjQwMCUiIGhlaWdodD0iNDAwJSIgZmlsbD0icmdiYSgyNTUsMjU1LDI1NSwwLjAyKSI+PC9yZWN0PjxjaXJjbGUgZmlsbD0icmdiYSgyNTUsMjU1LDI1NSwwLjA1KSIgY3g9IjIwIiBjeT0iMjAiIHI9IjEiPjwvY2lyY2xlPjwvcGF0dGVybj48L2RlZnM+PHJlY3QgZmlsbD0idXJsKCNwYXR0ZXJuKSIgaGVpZ2h0PSIxMDAlIiB3aWR0aD0iMTAwJSI+PC9yZWN0Pjwvc3ZnPg==');
        opacity: 0.5;
    }

    .text-gradient {
        background: linear-gradient(to right, #ffffff, rgba(255, 255, 255, 0.8));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-fill-color: transparent;
    }

    /* Premium Badge */
    .premium-badge {
        background: linear-gradient(135deg, #f6d365 0%, #fda085 100%);
        color: #fff;
        padding: 5px 15px;
        border-radius: 20px;
        font-weight: 700;
        font-size: 12px;
        letter-spacing: 1px;
        box-shadow: 0 5px 15px rgba(253, 160, 133, 0.3);
    }

    /* Dashboard Icon */
    .dashboard-icon-container {
        position: relative;
        display: inline-block;
    }

    .dashboard-icon-bg {
        position: absolute;
        width: 120px;
        height: 120px;
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }

    .dashboard-icon {
        position: relative;
        width: 80px;
        height: 80px;
        background-color: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto;
    }

    .dashboard-icon i {
        font-size: 3rem;
        color: #fff;
    }

    /* Stat Containers */
    .stat-container {
        position: relative;
        padding: 10px 0;
    }

    .stat-value {
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 5px;
    }

    .stat-label {
        font-size: 0.85rem;
        opacity: 0.8;
    }

    .stat-icon {
        position: absolute;
        top: 50%;
        right: 10px;
        transform: translateY(-50%);
        opacity: 0.3;
        font-size: 1.5rem;
    }

    /* Premium Icon Container */
    .premium-icon-container {
        width: 40px;
        height: 40px;
        border-radius: 12px;
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 5px 15px rgba(66, 134, 119, 0.2);
    }

    .premium-icon-container i {
        color: white;
        font-size: 1.2rem;
    }

    /* Revenue Cards */
    .revenue-card {
        border-radius: 15px;
        padding: 20px;
        height: 100%;
        position: relative;
        overflow: hidden;
        transition: all var(--transition-speed) ease;
    }

    .revenue-card.this-month {
        background: linear-gradient(135deg, rgba(66, 134, 119, 0.1) 0%, rgba(66, 134, 119, 0.2) 100%);
        border: 1px solid rgba(66, 134, 119, 0.1);
    }

    .revenue-card.last-month {
        background: linear-gradient(135deg, rgba(36, 68, 90, 0.1) 0%, rgba(36, 68, 90, 0.2) 100%);
        border: 1px solid rgba(36, 68, 90, 0.1);
    }

    .revenue-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
    }

    .revenue-card-inner {
        position: relative;
        z-index: 2;
    }

    .revenue-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 15px;
    }

    .this-month .revenue-icon {
        background-color: rgba(66, 134, 119, 0.2);
        color: var(--primary-color);
    }

    .last-month .revenue-icon {
        background-color: rgba(36, 68, 90, 0.2);
        color: var(--secondary-color);
    }

    .revenue-icon i {
        font-size: 1.5rem;
    }

    .revenue-amount {
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 5px;
    }

    .this-month .revenue-amount {
        color: var(--primary-color);
    }

    .last-month .revenue-amount {
        color: var(--secondary-color);
    }

    .revenue-label {
        font-size: 0.85rem;
        color: #6c757d;
        margin-bottom: 0;
    }

    /* Checkpoint Container */
    .checkpoint-container {
        background-color: #f8f9fa;
        border-radius: 12px;
        padding: 15px;
        margin-top: 20px;
    }

    .premium-progress {
        border-radius: 10px;
        overflow: hidden;
        background-color: #e9ecef;
    }

    .progress-gradient {
        background: linear-gradient(to right, var(--primary-light), var(--primary-color));
    }

    .progress-gradient-blue {
        background: linear-gradient(to right, #90caf9, #1976d2);
    }

    .growth-positive {
        background-color: rgba(46, 125, 50, 0.1);
        color: var(--success-color);
        font-weight: 600;
    }

    .growth-negative {
        background-color: rgba(211, 47, 47, 0.1);
        color: var(--danger-color);
        font-weight: 600;
    }

    /* Order Status Cards */
    .order-status-card {
        border-radius: 15px;
        padding: 20px;
        position: relative;
        overflow: hidden;
        transition: all var(--transition-speed) ease;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        text-align: center;
    }

    .order-status-card.pending {
        background: linear-gradient(135deg, rgba(237, 108, 2, 0.1) 0%, rgba(237, 108, 2, 0.2) 100%);
        border: 1px solid rgba(237, 108, 2, 0.1);
    }

    .order-status-card.processing {
        background: linear-gradient(135deg, rgba(2, 136, 209, 0.1) 0%, rgba(2, 136, 209, 0.2) 100%);
        border: 1px solid rgba(2, 136, 209, 0.1);
    }

    .order-status-card.completed {
        background: linear-gradient(135deg, rgba(46, 125, 50, 0.1) 0%, rgba(46, 125, 50, 0.2) 100%);
        border: 1px solid rgba(46, 125, 50, 0.1);
    }

    .order-status-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
    }

    .order-status-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 10px;
    }

    .pending .order-status-icon {
        background-color: rgba(237, 108, 2, 0.2);
        color: var(--warning-color);
    }

    .processing .order-status-icon {
        background-color: rgba(2, 136, 209, 0.2);
        color: var(--info-color);
    }

    .completed .order-status-icon {
        background-color: rgba(46, 125, 50, 0.2);
        color: var(--success-color);
    }

    .order-status-icon i {
        font-size: 1.5rem;
    }

    .order-status-count {
        font-size: 1.8rem;
        font-weight: 700;
        margin-bottom: 0;
    }

    .pending .order-status-count {
        color: var(--warning-color);
    }

    .processing .order-status-count {
        color: var(--info-color);
    }

    .completed .order-status-count {
        color: var(--success-color);
    }

    .order-status-label {
        font-size: 0.85rem;
        color: #6c757d;
        margin-bottom: 5px;
    }

    .order-status-badge {
        position: absolute;
        top: 10px;
        right: 10px;
    }

    .pending .order-status-badge .badge {
        background-color: rgba(237, 108, 2, 0.2);
        color: var(--warning-color);
    }

    .processing .order-status-badge .badge {
        background-color: rgba(2, 136, 209, 0.2);
        color: var(--info-color);
    }

    .completed .order-status-badge .badge {
        background-color: rgba(46, 125, 50, 0.2);
        color: var(--success-color);
    }

    .order-status-link {
        position: absolute;
        bottom: 10px;
        right: 10px;
        opacity: 0;
        transition: opacity var(--transition-speed) ease;
    }

    .order-status-card:hover .order-status-link {
        opacity: 1;
    }

    .pending .order-status-link {
        color: var(--warning-color);
    }

    .processing .order-status-link {
        color: var(--info-color);
    }

    .completed .order-status-link {
        color: var(--success-color);
    }

    /* Premium Table Styles */
    .premium-table {
        border-collapse: separate;
        border-spacing: 0;
    }

    .premium-table thead th {
        background-color: #f8f9fa;
        border-bottom: none;
        padding: 15px;
        font-weight: 600;
        color: #495057;
    }

    .premium-table tbody tr {
        transition: all var(--transition-speed) ease;
    }

    .premium-table tbody tr:hover {
        background-color: rgba(66, 134, 119, 0.05);
    }

    .order-row {
        border-bottom: 1px solid #f1f1f1;
    }

    .order-number-badge {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        height: 24px;
        background-color: var(--primary-color);
        color: white;
        border-radius: 6px;
        font-size: 12px;
        font-weight: 700;
    }

    .avatar-circle {
        width: 36px;
        height: 36px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        font-size: 14px;
        background-color: rgba(66, 134, 119, 0.1);
        color: var(--primary-color);
    }

    .status-badge {
        display: inline-flex;
        align-items: center;
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
    }

    .status-badge.pending {
        background-color: rgba(237, 108, 2, 0.1);
        color: var(--warning-color);
    }

    .status-badge.processing {
        background-color: rgba(2, 136, 209, 0.1);
        color: var(--info-color);
    }

    .status-badge.completed {
        background-color: rgba(46, 125, 50, 0.1);
        color: var(--success-color);
    }

    .status-badge.declined {
        background-color: rgba(211, 47, 47, 0.1);
        color: var(--danger-color);
    }

    .action-btn {
        transition: all var(--transition-speed) ease;
    }

    .action-btn:hover {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
        color: white;
    }

    /* Premium List Styles */
    .premium-list {
        border-radius: var(--card-border-radius);
        overflow: hidden;
    }

    .product-item {
        transition: all var(--transition-speed) ease;
        position: relative;
    }

    .product-item:hover {
        background-color: rgba(66, 134, 119, 0.05);
    }

    .product-rank {
        width: 28px;
        height: 28px;
        border-radius: 50%;
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 700;
        font-size: 14px;
        margin-right: 15px;
        flex-shrink: 0;
    }

    .product-image {
        width: 60px;
        height: 60px;
        border-radius: 12px;
        overflow: hidden;
        margin-right: 15px;
        flex-shrink: 0;
    }

    .product-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .product-image-placeholder {
        width: 100%;
        height: 100%;
        background-color: #f8f9fa;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #adb5bd;
        font-size: 1.5rem;
    }

    .product-info {
        flex-grow: 1;
    }

    .product-name {
        font-weight: 600;
        margin-bottom: 5px;
        color: #343a40;
    }

    .product-meta {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 10px;
    }

    .product-orders {
        font-size: 12px;
        color: #6c757d;
    }

    .product-price {
        font-size: 12px;
        font-weight: 600;
        color: var(--primary-color);
        background-color: rgba(66, 134, 119, 0.1);
        padding: 3px 8px;
        border-radius: 12px;
    }

    .product-actions {
        margin-left: 10px;
        opacity: 0;
        transition: opacity var(--transition-speed) ease;
    }

    .product-item:hover .product-actions {
        opacity: 1;
    }

    .btn-icon {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: rgba(66, 134, 119, 0.1);
        color: var(--primary-color);
        border: none;
        transition: all var(--transition-speed) ease;
    }

    .btn-icon:hover {
        background-color: var(--primary-color);
        color: white;
    }

    /* Empty State */
    .empty-state {
        padding: 20px;
        text-align: center;
    }

    .empty-state-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background-color: #f8f9fa;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto;
        color: #adb5bd;
        font-size: 1.5rem;
    }

    /* Checkpoint Badge */
    .checkpoint-badge {
        background-color: rgba(66, 134, 119, 0.1);
        color: var(--primary-color);
        font-weight: 600;
        padding: 6px 12px;
        border-radius: 20px;
    }

    /* Button Styles */
    .btn-primary {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
    }

    .btn-primary:hover, .btn-primary:focus {
        background-color: var(--primary-dark);
        border-color: var(--primary-dark);
    }

    .btn-outline-primary {
        color: var(--primary-color);
        border-color: var(--primary-color);
    }

    .btn-outline-primary:hover, .btn-outline-primary:focus {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
        color: white;
    }
</style>
@endpush
