@extends('layouts.admin')

@section('title', 'Partition Details')

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Partition: {{ $partition->name }}</h1>
        <div>
            <a href="{{ route('admin.releases.show', $partition->release_id) }}" class="btn btn-secondary">
                <i class="bi bi-arrow-left"></i> Back to Release
            </a>
            @if($partition->release->status === 'draft')
                <a href="{{ route('admin.partitions.edit', $partition->id) }}" class="btn btn-primary">
                    <i class="bi bi-pencil"></i> Edit
                </a>
            @endif
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold">Partition Information</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>Name:</strong>
                        <p>{{ $partition->name }}</p>
                    </div>
                    <div class="mb-3">
                        <strong>Release:</strong>
                        <p>
                            <a href="{{ route('admin.releases.show', $partition->release_id) }}">
                                {{ $partition->release->version }} - {{ $partition->release->name }}
                            </a>
                        </p>
                    </div>
                    <div class="mb-3">
                        <strong>Type:</strong>
                        <p>{{ ucfirst($partition->type) }}</p>
                    </div>
                    <div class="mb-3">
                        <strong>Deployment Order:</strong>
                        <p>{{ $partition->deployment_order }}</p>
                    </div>
                    <div class="mb-3">
                        <strong>Status:</strong>
                        <p>
                            @switch($partition->status)
                                @case('pending')
                                    <span class="badge bg-secondary">Pending</span>
                                    @break
                                @case('deployed')
                                    <span class="badge bg-success">Deployed</span>
                                    @break
                                @case('failed')
                                    <span class="badge bg-danger">Failed</span>
                                    @break
                                @case('rolled_back')
                                    <span class="badge bg-warning">Rolled Back</span>
                                    @break
                                @default
                                    <span class="badge bg-secondary">{{ $partition->status }}</span>
                            @endswitch
                        </p>
                    </div>
                    <div class="mb-3">
                        <strong>Description:</strong>
                        <p>{{ $partition->description ?? 'No description provided.' }}</p>
                    </div>
                    @if($partition->deployed_at)
                        <div class="mb-3">
                            <strong>Deployed At:</strong>
                            <p>{{ $partition->deployed_at->format('Y-m-d H:i') }}</p>
                        </div>
                    @endif
                    @if($partition->deployment_notes)
                        <div class="mb-3">
                            <strong>Deployment Notes:</strong>
                            <p>{{ $partition->deployment_notes }}</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold">Actions</h6>
                </div>
                <div class="card-body">
                    @if($partition->release->status === 'draft')
                        <form action="{{ route('admin.partitions.destroy', $partition->id) }}" method="POST" class="mb-2">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-danger btn-block" onclick="return confirm('Are you sure you want to delete this partition?')">
                                <i class="bi bi-trash"></i> Delete Partition
                            </button>
                        </form>
                    @endif

                    @if($partition->release->status === 'approved' && $partition->status === 'pending')
                        <button type="button" class="btn btn-success btn-block mb-2" data-bs-toggle="modal" data-bs-target="#deployPartitionModal">
                            <i class="bi bi-rocket"></i> Deploy Partition
                        </button>
                        <button type="button" class="btn btn-danger btn-block" data-bs-toggle="modal" data-bs-target="#failPartitionModal">
                            <i class="bi bi-x"></i> Mark as Failed
                        </button>
                    @endif

                    @if($partition->status === 'deployed')
                        <button type="button" class="btn btn-warning btn-block" data-bs-toggle="modal" data-bs-target="#rollbackPartitionModal">
                            <i class="bi bi-arrow-counterclockwise"></i> Rollback Partition
                        </button>
                    @endif
                </div>
            </div>

            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold">File Paths</h6>
                </div>
                <div class="card-body">
                    @if(empty($partition->file_paths))
                        <div class="alert alert-info">
                            No file paths defined for this partition.
                        </div>
                    @else
                        <ul class="list-group">
                            @foreach($partition->file_paths as $path)
                                <li class="list-group-item">{{ $path }}</li>
                            @endforeach
                        </ul>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Deploy Partition Modal -->
<div class="modal fade" id="deployPartitionModal" tabindex="-1" aria-labelledby="deployPartitionModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form action="{{ route('admin.partitions.deploy', $partition->id) }}" method="POST">
                @csrf
                <div class="modal-header">
                    <h5 class="modal-title" id="deployPartitionModalLabel">Deploy Partition: {{ $partition->name }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="deployment_notes" class="form-label">Deployment Notes</label>
                        <textarea class="form-control" id="deployment_notes" name="deployment_notes" rows="3" placeholder="Enter any notes about the deployment process"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">Deploy</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Fail Partition Modal -->
<div class="modal fade" id="failPartitionModal" tabindex="-1" aria-labelledby="failPartitionModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form action="{{ route('admin.partitions.fail', $partition->id) }}" method="POST">
                @csrf
                <div class="modal-header">
                    <h5 class="modal-title" id="failPartitionModalLabel">Fail Partition: {{ $partition->name }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="deployment_notes" class="form-label">Failure Reason</label>
                        <textarea class="form-control" id="deployment_notes" name="deployment_notes" rows="3" placeholder="Enter the reason for failure" required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">Mark as Failed</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Rollback Partition Modal -->
<div class="modal fade" id="rollbackPartitionModal" tabindex="-1" aria-labelledby="rollbackPartitionModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form action="{{ route('admin.partitions.rollback', $partition->id) }}" method="POST">
                @csrf
                <div class="modal-header">
                    <h5 class="modal-title" id="rollbackPartitionModalLabel">Rollback Partition: {{ $partition->name }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="deployment_notes" class="form-label">Rollback Reason</label>
                        <textarea class="form-control" id="deployment_notes" name="deployment_notes" rows="3" placeholder="Enter the reason for rollback" required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-warning">Rollback</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection
