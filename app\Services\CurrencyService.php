<?php

namespace App\Services;

use App\Models\Currency;
use App\Models\UserCurrencyPreference;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;

class CurrencyService
{
    /**
     * Get the current user's preferred currency.
     */
    public static function getUserCurrency()
    {
        // Check if user is authenticated and has a preference
        if (Auth::check()) {
            $preference = UserCurrencyPreference::where('user_id', Auth::id())->first();
            if ($preference) {
                return $preference->currency_code;
            }
        }

        // Check session for guest users
        if (Session::has('currency')) {
            return Session::get('currency');
        }

        // Return default currency
        $defaultCurrency = Currency::getDefault();
        return $defaultCurrency ? $defaultCurrency->code : 'EUR';
    }

    /**
     * Set the current user's currency preference.
     */
    public static function setUserCurrency($currencyCode)
    {
        // Validate currency exists and is active
        $currency = Currency::where('code', $currencyCode)->where('is_active', true)->first();
        if (!$currency) {
            return false;
        }

        // Save to user preference if authenticated
        if (Auth::check()) {
            UserCurrencyPreference::updateOrCreate(
                ['user_id' => Auth::id()],
                ['currency_code' => $currencyCode]
            );
        }

        // Always save to session for immediate use
        Session::put('currency', $currencyCode);
        return true;
    }

    /**
     * Get all active currencies.
     */
    public static function getActiveCurrencies()
    {
        return Currency::active()->orderBy('is_default', 'desc')->orderBy('name')->get();
    }

    /**
     * Format amount with currency symbol.
     */
    public static function format($amount, $currencyCode = null)
    {
        $currencyCode = $currencyCode ?? self::getUserCurrency();
        $currency = Currency::where('code', $currencyCode)->first();
        
        if (!$currency) {
            return $currencyCode . ' ' . number_format($amount, 2);
        }

        return $currency->formatAmount($amount);
    }

    /**
     * Convert amount between currencies.
     */
    public static function convert($amount, $fromCurrency, $toCurrency = null)
    {
        $toCurrency = $toCurrency ?? self::getUserCurrency();
        return Currency::convert($amount, $fromCurrency, $toCurrency);
    }

    /**
     * Get currency symbol.
     */
    public static function getSymbol($currencyCode = null)
    {
        $currencyCode = $currencyCode ?? self::getUserCurrency();
        return Currency::getSymbol($currencyCode);
    }

    /**
     * Get currency name.
     */
    public static function getName($currencyCode = null)
    {
        $currencyCode = $currencyCode ?? self::getUserCurrency();
        $currency = Currency::where('code', $currencyCode)->first();
        return $currency ? $currency->name : $currencyCode;
    }

    /**
     * Check if currency is supported.
     */
    public static function isSupported($currencyCode)
    {
        return Currency::where('code', $currencyCode)->where('is_active', true)->exists();
    }

    /**
     * Get exchange rate for currency.
     */
    public static function getExchangeRate($currencyCode)
    {
        $currency = Currency::where('code', $currencyCode)->first();
        return $currency ? $currency->exchange_rate : 1;
    }

    /**
     * Update exchange rates (for admin use).
     */
    public static function updateExchangeRates($rates)
    {
        foreach ($rates as $code => $rate) {
            Currency::where('code', $code)->update(['exchange_rate' => $rate]);
        }
    }
}
