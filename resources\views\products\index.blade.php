@extends('layouts.app')

@section('title', 'Products - NATRYON')

@push('styles')
<style>
    .hover-lift {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .hover-lift:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1) !important;
    }
</style>
@endpush

@section('content')
    <!-- Hero Section -->
    <section class="py-5 bg-light">
        <div class="container">
            <div class="text-center mb-4">
                <h1 class="display-5 fw-bold" style="color: #428677;">Our Products</h1>
                <p class="lead">Discover our premium greens powder supplements for optimal health</p>
            </div>
        </div>
    </section>

    <!-- Products Section -->
    <section class="py-5">
        <div class="container">
            <div class="row">
                <!-- Filters Sidebar -->
                <div class="col-lg-3 mb-4">
                    <div class="card border-0 shadow-sm rounded-3">
                        <div class="card-header bg-white py-3 rounded-top">
                            <h5 class="mb-0" style="color: #428677;">Filters</h5>
                        </div>
                        <div class="card-body">
                        <form action="{{ route('products.index') }}" method="GET">
                            <div class="mb-4">
                                <h6 class="fw-bold mb-3">Categories</h6>
                                @foreach($categories as $category)
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="radio" name="category" id="category-{{ $category->id }}" value="{{ $category->id }}" {{ request('category') == $category->id ? 'checked' : '' }}>
                                        <label class="form-check-label" for="category-{{ $category->id }}">
                                            {{ $category->name }}
                                        </label>
                                    </div>
                                @endforeach
                            </div>
                            <div class="mb-4">
                                <h6 class="fw-bold mb-3">Sort By</h6>
                                <select name="sort" class="form-select">
                                    <option value="newest" {{ $sort == 'newest' ? 'selected' : '' }}>Newest</option>
                                    <option value="price_low" {{ $sort == 'price_low' ? 'selected' : '' }}>Price: Low to High</option>
                                    <option value="price_high" {{ $sort == 'price_high' ? 'selected' : '' }}>Price: High to Low</option>
                                    <option value="name" {{ $sort == 'name' ? 'selected' : '' }}>Name: A to Z</option>
                                </select>
                            </div>
                            <div class="d-grid">
                                <button type="submit" class="btn text-white" style="background-color: #428677;">
                                    <i class="bi bi-funnel me-2"></i>Apply Filters
                                </button>
                                @if(request('category') || request('sort'))
                                    <a href="{{ route('products.index') }}" class="btn btn-outline-secondary mt-2">
                                        <i class="bi bi-x-circle me-2"></i>Clear Filters
                                    </a>
                                @endif
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Products Grid -->
            <div class="col-lg-9">
                <div class="card border-0 shadow-sm rounded-3 mb-4">
                    <div class="card-body py-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <p class="mb-0"><i class="bi bi-grid me-2"></i>Showing {{ $products->firstItem() ?? 0 }} - {{ $products->lastItem() ?? 0 }} of {{ $products->total() }} products</p>
                        </div>
                    </div>
                </div>

                <div class="row g-4">
                    @forelse($products as $product)
                        <div class="col-md-6 col-lg-4">
                            <div class="card h-100 border-0 shadow-sm rounded-3 hover-lift">
                                @if($product->image)
                                    <img src="{{ asset('storage/' . $product->image) }}" class="card-img-top rounded-top" alt="{{ $product->name }}" style="height: 200px; object-fit: cover;">
                                @else
                                    <img src="https://via.placeholder.com/300x200?text=NATRYON" class="card-img-top rounded-top" alt="{{ $product->name }}">
                                @endif
                                <div class="card-body p-4">
                                    <h5 class="card-title">{{ $product->name }}</h5>
                                    <p class="card-text text-muted">{{ Str::limit($product->description, 100) }}</p>

                                    @if($product->subscription_only)
                                        <div class="badge bg-info mb-2">Subscription Only</div>
                                    @endif

                                    <div class="d-flex justify-content-between align-items-center mt-3">
                                        @if(!$product->subscription_only)
                                            <span class="fw-bold" style="color: #428677;">${{ number_format($product->price, 2) }}</span>
                                        @else
                                            <span class="fw-bold" style="color: #428677;">${{ number_format($product->subscription_price, 2) }}/mo</span>
                                        @endif
                                        <a href="{{ route('products.show', $product->slug) }}" class="btn btn-sm text-white" style="background-color: #428677;">
                                            <i class="bi bi-eye me-1"></i>View Details
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @empty
                        <div class="col-12">
                            <div class="alert alert-info rounded-3 shadow-sm">
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-exclamation-circle fs-4 me-3"></i>
                                    <div>
                                        <h5 class="mb-1">No products found</h5>
                                        <p class="mb-0">Please try different filters or check back later for new products.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforelse
                </div>

                <div class="mt-5 d-flex justify-content-center">
                    {{ $products->withQueryString()->links() }}
                </div>
            </div>
        </div>
    </section>
@endsection
