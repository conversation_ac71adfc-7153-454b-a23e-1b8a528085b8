<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="author" content="Mohamed EL KHOULI">
    <meta name="creator" content="Mohamed ELKHOULI">
    <meta name="publisher" content="NATRYON">
    <meta name="geo.placename" content="Casablanca, Morocco">

    <title>@yield('title', 'NATRYON - Premium Greens Powder Supplement')</title>

    <!-- SEO -->
    {!! SEO::generate() !!}

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Styles -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        :root {
            --primary-color: #2d504b;
            --secondary-color: #428677;
            --accent-color: #8ab0a6;
            --light-color: #b1b7b8;
            --dark-color: #7a868d;
            --darker-color: #24445a;
        }

        body {
            font-family: 'Poppins', sans-serif;
            color: #333;
            background-color: #f8f9fa;
        }

        .navbar {
            background-color: var(--primary-color);
        }

        .navbar-brand, .navbar-nav .nav-link {
            color: white;
        }

        .navbar-nav .nav-link:hover {
            color: var(--light-color);
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-primary:hover {
            background-color: var(--darker-color);
            border-color: var(--darker-color);
        }

        .btn-secondary {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
        }

        .btn-secondary:hover {
            background-color: var(--dark-color);
            border-color: var(--dark-color);
        }

        .footer {
            background-color: var(--primary-color);
            color: white;
            padding: 2rem 0;
        }

        .footer a {
            color: var(--light-color);
        }

        .footer a:hover {
            color: white;
        }
    </style>
    @stack('styles')
</head>
<body>
    <header>
        <nav class="navbar navbar-expand-lg navbar-dark">
            <div class="container">
                <a class="navbar-brand" href="{{ route('home') }}">
                    <img src="{{ asset('images/logos/logo natryon blanc copie.png') }}" alt="NATRYON" height="40">
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('home') ? 'active' : '' }}" href="{{ route('home') }}">{{ __('Home') }}</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('products.index') ? 'active' : '' }}" href="{{ route('products.index') }}">{{ __('Products') }}</a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('about') ? 'active' : '' }}" href="{{ route('about') }}">{{ __('About') }}</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('contact') ? 'active' : '' }}" href="{{ route('contact') }}">{{ __('Contact') }}</a>
                        </li>
                    </ul>
                    <ul class="navbar-nav">
                        <li>
                            @include('components.language-selector')
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ route('cart.index') }}">
                                <i class="bi bi-cart"></i> {{ __('Cart') }}
                                @if(session()->has('cart') && count(session('cart')) > 0)
                                    <span class="badge bg-danger">{{ count(session('cart')) }}</span>
                                @endif
                            </a>
                        </li>
                        @guest
                            <li class="nav-item">
                                <a class="nav-link" href="{{ route('login') }}">{{ __('Login') }}</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="{{ route('register') }}">{{ __('Register') }}</a>
                            </li>
                        @else
                            <li class="nav-item dropdown">
                                <button class="nav-link dropdown-toggle btn btn-link" type="button" id="navbarDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                    {{ Auth::user()->name }}
                                </button>
                                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown">
                                    @if(Auth::user()->isAdmin())
                                        <li><a class="dropdown-item" href="{{ route('admin.dashboard') }}">{{ __('Admin Dashboard') }}</a></li>
                                        <li><hr class="dropdown-divider"></li>
                                    @endif
                                    <li><a class="dropdown-item" href="{{ route('profile.edit') }}"><i class="bi bi-person me-2"></i>{{ __('My Profile') }}</a></li>
                                    <li><a class="dropdown-item" href="{{ route('orders.index') }}"><i class="bi bi-box me-2"></i>{{ __('My Orders') }}</a></li>
                                    <li><a class="dropdown-item" href="{{ route('affiliate.index') }}"><i class="bi bi-graph-up me-2"></i>{{ __('Affiliate Program') }}</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <a class="dropdown-item" href="{{ route('logout') }}" onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
                                            {{ __('Logout') }}
                                        </a>
                                        <form id="logout-form" action="{{ route('logout') }}" method="POST" class="d-none">
                                            @csrf
                                        </form>
                                    </li>
                                </ul>
                            </li>
                        @endguest
                    </ul>
                </div>
            </div>
        </nav>
    </header>

    <main class="py-0">
        @if(session('success') || session('error') || session('info'))
            <div class="container py-3">
                @if(session('success'))
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        {{ session('success') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                @endif

                @if(session('error'))
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        {{ session('error') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                @endif

                @if(session('info'))
                    <div class="alert alert-info alert-dismissible fade show" role="alert">
                        {{ session('info') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                @endif
            </div>
        @endif

        @yield('content')
    </main>

    <footer class="footer mt-auto">
        <div class="container">
            <div class="row">
                <div class="col-md-4 mb-4 mb-md-0">
                    <img src="{{ asset('images/logos/logo natryon blanc copie.png') }}" alt="NATRYON" height="40" class="mb-3">
                    <p>{{ __('Premium Greens Powder Supplement for optimal health and nutrition.') }}</p>
                </div>
                <div class="col-md-2 mb-4 mb-md-0">
                    <h5>{{ __('Links') }}</h5>
                    <ul class="list-unstyled">
                        <li><a href="{{ route('home') }}">{{ __('Home') }}</a></li>
                        <li><a href="{{ route('products.index') }}">{{ __('Products') }}</a></li>
                        <li><a href="{{ route('about') }}">{{ __('About') }}</a></li>
                        <li><a href="{{ route('contact') }}">{{ __('Contact') }}</a></li>
                    </ul>
                </div>
                <div class="col-md-3 mb-4 mb-md-0">
                    <h5>{{ __('Legal') }}</h5>
                    <ul class="list-unstyled">
                        <li><a href="#">{{ __('Terms of Service') }}</a></li>
                        <li><a href="#">{{ __('Privacy Policy') }}</a></li>
                        <li><a href="#">{{ __('Shipping Policy') }}</a></li>
                        <li><a href="#">{{ __('Refund Policy') }}</a></li>
                    </ul>
                </div>
                <div class="col-md-3">
                    <h5>{{ __('Contact') }}</h5>
                    <ul class="list-unstyled">
                        <li><i class="bi bi-envelope"></i> <a href="mailto:<EMAIL>"><EMAIL></a></li>
                        <li><i class="bi bi-telephone"></i> <a href="tel:+212600000000">+212 600-000-000</a></li>
                        <li><i class="bi bi-geo-alt"></i> Casablanca, Morocco</li>
                        <li class="mt-2"><i class="bi bi-person"></i> {{ __('Creator') }}: Mohamed EL KHOULI</li>
                    </ul>
                </div>
            </div>
            <hr class="my-4 bg-light">
            <div class="row">
                <div class="col-md-6 text-center text-md-start">
                    <p>&copy; {{ date('Y') }} NATRYON. {{ __('All rights reserved.') }}</p>
                    <p class="small text-muted" style="display: none;">{{ __('Developed by') }} <a href="#" class="text-decoration-none" style="color: var(--secondary-color);">Mohamed EL KHOULI</a> {{ __('from Casablanca, Morocco') }}</p>
                </div>
                <div class="col-md-6 text-center text-md-end">
                    <ul class="list-inline mb-0">
                        <li class="list-inline-item"><a href="#"><i class="bi bi-facebook"></i></a></li>
                        <li class="list-inline-item"><a href="#"><i class="bi bi-instagram"></i></a></li>
                        <li class="list-inline-item"><a href="#"><i class="bi bi-twitter"></i></a></li>
                        <li class="list-inline-item"><a href="#"><i class="bi bi-youtube"></i></a></li>
                        <li class="list-inline-item"><a href="#"><i class="bi bi-github"></i></a></li>
                        <li class="list-inline-item"><a href="#"><i class="bi bi-linkedin"></i></a></li>
                    </ul>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    @stack('scripts')

    <!-- Structured Data for Creator -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebSite",
        "name": "NATRYON",
        "url": "/",
        "description": "Premium Greens Powder Supplement for optimal health and nutrition",
        "potentialAction": {
            "@type": "SearchAction",
            "target": "/products?search={search_term}",
            "query-input": "required name=search_term"
        },
        "author": {
            "@type": "Person",
            "name": "Mohamed EL KHOULI",
            "jobTitle": "Full Stack Developer",
            "address": {
                "@type": "PostalAddress",
                "addressLocality": "Casablanca",
                "addressCountry": "Morocco"
            }
        }
    }
    </script>
</body>
</html>
