@extends('layouts.app')

@section('title', 'Order #' . $order->order_number . ' - NATRYON')

@section('content')
    <!-- Hero Section -->
    <section class="py-4">
        <div class="container">
            <div class="d-flex flex-column flex-md-row justify-content-between align-items-md-center mb-4 gap-3">
                <div>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-2">
                            <li class="breadcrumb-item"><a href="{{ route('orders.index') }}" class="text-decoration-none">My Orders</a></li>
                            <li class="breadcrumb-item active" aria-current="page">Order Details</li>
                        </ol>
                    </nav>
                    <h1 class="h3 mb-0 text-primary fw-bold">Order #{{ $order->order_number }}</h1>
                </div>
                <div class="d-flex gap-2">
                    <a href="{{ route('orders.index') }}" class="btn btn-outline-primary rounded-pill">
                        <i class="bi bi-arrow-left me-1"></i> Back to Orders
                    </a>
                    <button class="btn btn-primary rounded-pill" onclick="window.print()">
                        <i class="bi bi-printer me-1"></i> Print
                    </button>
                </div>
            </div>

            <!-- Order Status Card -->
            <div class="card border-0 shadow-sm rounded-3 mb-4 overflow-hidden">
                <div class="card-body p-0">
                    <div class="d-flex flex-wrap">
                        <div class="p-4 border-end" style="min-width: 200px;">
                            <div class="d-flex align-items-center mb-2">
                                <div class="rounded-circle bg-light p-2 me-3">
                                    <i class="bi bi-calendar3 text-primary"></i>
                                </div>
                                <div>
                                    <small class="text-muted d-block">Order Date</small>
                                    <span class="fw-medium">{{ $order->created_at->format('M d, Y') }}</span>
                                </div>
                            </div>
                            <div class="d-flex align-items-center">
                                <div class="rounded-circle bg-light p-2 me-3">
                                    <i class="bi bi-tag text-primary"></i>
                                </div>
                                <div>
                                    <small class="text-muted d-block">Status</small>
                                    @if($order->status == 'pending')
                                        <button type="button" class="badge rounded-pill bg-warning text-dark status-badge border-0"
                                               onclick="showStatusInfo('pending')"
                                               title="Click for more information about pending status">
                                            <i class="bi bi-hourglass-split me-1"></i>Pending
                                        </button>
                                    @elseif($order->status == 'processing')
                                        <button type="button" class="badge rounded-pill bg-info text-dark status-badge border-0"
                                               onclick="showStatusInfo('processing')"
                                               title="Click for more information about processing status">
                                            <i class="bi bi-gear-wide-connected me-1"></i>Processing
                                        </button>
                                    @elseif($order->status == 'completed')
                                        <button type="button" class="badge rounded-pill bg-success status-badge border-0"
                                               onclick="showStatusInfo('completed')"
                                               title="Click for more information about completed status">
                                            <i class="bi bi-check-circle me-1"></i>Completed
                                        </button>
                                    @elseif($order->status == 'declined')
                                        <button type="button" class="badge rounded-pill bg-danger status-badge border-0"
                                               onclick="showStatusInfo('declined')"
                                               title="Click for more information about declined status">
                                            <i class="bi bi-x-circle me-1"></i>Declined
                                        </button>
                                    @endif
                                </div>
                            </div>
                        </div>
                        <div class="p-4 border-end" style="min-width: 200px;">
                            <div class="d-flex align-items-center">
                                <div class="rounded-circle bg-light p-2 me-3">
                                    <i class="bi bi-credit-card text-primary"></i>
                                </div>
                                <div>
                                    <small class="text-muted d-block">Payment</small>
                                    <div class="d-flex align-items-center">
                                        <span class="fw-medium me-2">{{ ucfirst($order->payment_method) }}</span>
                                        @if($order->is_paid)
                                            <button type="button" class="badge rounded-pill bg-success border-0 status-badge"
                                                   onclick="showPaymentInfo('paid')"
                                                   title="Payment completed successfully">Paid</button>
                                        @else
                                            <button type="button" class="badge rounded-pill bg-warning text-dark border-0 status-badge"
                                                   onclick="showPaymentInfo('unpaid')"
                                                   title="Payment pending">Unpaid</button>
                                        @endif
                                    </div>
                                    @if($order->is_paid && $order->paid_at)
                                        <small class="text-muted">{{ $order->paid_at->format('M d, Y H:i') }}</small>
                                    @endif
                                </div>
                            </div>
                        </div>
                        <div class="p-4 d-flex align-items-center">
                            <div class="rounded-circle bg-light p-2 me-3">
                                <i class="bi bi-cash-stack text-primary"></i>
                            </div>
                            <div>
                                <small class="text-muted d-block">Total Amount</small>
                                <span class="fw-bold fs-4 text-primary">${{ number_format($order->total, 2) }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row g-4">
                <!-- Customer Information -->
                <div class="col-lg-4">
                    <div class="card border-0 shadow-sm rounded-3 h-100">
                        <div class="card-header bg-white py-3 border-bottom border-light">
                            <h5 class="mb-0 text-primary">
                                <i class="bi bi-person-circle me-2"></i>Customer Information
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-4">
                                <h6 class="fw-bold text-uppercase small text-secondary mb-3">
                                    <i class="bi bi-receipt me-2"></i>Billing Information
                                </h6>
                                <div class="bg-light p-3 rounded-3">
                                    <p class="mb-1 fw-medium">{{ $order->billing_name }}</p>
                                    <p class="mb-1"><i class="bi bi-envelope-fill text-muted me-2 small"></i>{{ $order->billing_email }}</p>
                                    <p class="mb-1"><i class="bi bi-telephone-fill text-muted me-2 small"></i>{{ $order->billing_phone }}</p>
                                    <p class="mb-1"><i class="bi bi-geo-alt-fill text-muted me-2 small"></i>{{ $order->billing_address }}</p>
                                    <p class="mb-1">{{ $order->billing_city }}, {{ $order->billing_state }} {{ $order->billing_zipcode }}</p>
                                    <p class="mb-0">{{ $order->billing_country }}</p>
                                </div>
                            </div>

                            <div>
                                <h6 class="fw-bold text-uppercase small text-secondary mb-3">
                                    <i class="bi bi-truck me-2"></i>Shipping Information
                                </h6>
                                <div class="bg-light p-3 rounded-3">
                                    <p class="mb-1 fw-medium">{{ $order->shipping_name ?? $order->billing_name }}</p>
                                    <p class="mb-1"><i class="bi bi-geo-alt-fill text-muted me-2 small"></i>{{ $order->shipping_address ?? $order->billing_address }}</p>
                                    <p class="mb-1">{{ $order->shipping_city ?? $order->billing_city }}, {{ $order->shipping_state ?? $order->billing_state }} {{ $order->shipping_zipcode ?? $order->billing_zipcode }}</p>
                                    <p class="mb-0">{{ $order->shipping_country ?? $order->billing_country }}</p>
                                </div>
                            </div>

                            @if($order->notes || $order->affiliate_code)
                                <hr class="my-4">

                                @if($order->notes)
                                    <div class="mb-3">
                                        <h6 class="fw-bold text-uppercase small text-secondary mb-2">
                                            <i class="bi bi-pencil-square me-2"></i>Order Notes
                                        </h6>
                                        <p class="mb-0 bg-light p-3 rounded-3 fst-italic">{{ $order->notes }}</p>
                                    </div>
                                @endif

                                @if($order->affiliate_code)
                                    <div>
                                        <h6 class="fw-bold text-uppercase small text-secondary mb-2">
                                            <i class="bi bi-diagram-3 me-2"></i>Affiliate Code
                                        </h6>
                                        <div class="bg-light p-3 rounded-3">
                                            <span class="badge bg-primary">{{ $order->affiliate_code }}</span>
                                        </div>
                                    </div>
                                @endif
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Order Items -->
                <div class="col-lg-8">
                    <div class="card border-0 shadow-sm rounded-3">
                        <div class="card-header bg-white py-3 border-bottom border-light">
                            <h5 class="mb-0 text-primary">
                                <i class="bi bi-box2 me-2"></i>Order Items
                            </h5>
                        </div>
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th class="ps-4">Product</th>
                                            <th>Price</th>
                                            <th>Quantity</th>
                                            <th class="text-end pe-4">Total</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($order->items as $item)
                                            <tr>
                                                <td class="ps-4">
                                                    <div class="d-flex align-items-center">
                                                        @if($item->product && $item->product->image)
                                                            <img src="{{ asset('storage/' . $item->product->image) }}" alt="{{ $item->product_name }}" class="rounded-3 me-3" style="width: 60px; height: 60px; object-fit: cover;">
                                                        @else
                                                            <div class="bg-light rounded-3 d-flex align-items-center justify-content-center me-3" style="width: 60px; height: 60px;">
                                                                <i class="bi bi-box-seam text-primary"></i>
                                                            </div>
                                                        @endif
                                                        <div>
                                                            <h6 class="mb-0 fw-medium">{{ $item->product_name }}</h6>
                                                            @if($item->is_subscription)
                                                                <div class="d-flex align-items-center gap-2">
                                                                    <button type="button" class="badge bg-primary rounded-pill border-0 status-badge"
                                                                           onclick="showSubscriptionInfo('{{ $item->product_name }}')"
                                                                           title="Subscription information">
                                                                        <i class="bi bi-arrow-repeat me-1"></i>Subscription
                                                                    </button>
                                                                    <button type="button" class="badge bg-danger rounded-pill border-0 status-badge"
                                                                           onclick="confirmCancelSubscription('{{ $item->id }}', '{{ $item->product_name }}')"
                                                                           title="Cancel this subscription">
                                                                        <i class="bi bi-x-circle me-1"></i>Cancel
                                                                    </button>
                                                                </div>

                                                                <!-- Hidden form for subscription cancellation -->
                                                                <form id="cancel-subscription-form-{{ $item->id }}"
                                                                      action="{{ route('subscriptions.cancel-from-order', ['order' => $order->id, 'item' => $item->id]) }}"
                                                                      method="POST"
                                                                      style="display: none;">
                                                                    @csrf
                                                                </form>
                                                            @endif
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>${{ number_format($item->price, 2) }}</td>
                                                <td>{{ $item->quantity }}</td>
                                                <td class="text-end pe-4 fw-medium">${{ number_format($item->price * $item->quantity, 2) }}</td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>

                            <div class="p-4 border-top">
                                <div class="row justify-content-end">
                                    <div class="col-md-6">
                                        <div class="bg-light p-3 rounded-3">
                                            <div class="d-flex justify-content-between mb-2">
                                                <span class="text-muted">Subtotal:</span>
                                                <span>${{ number_format($order->subtotal, 2) }}</span>
                                            </div>
                                            @if($order->tax > 0)
                                                <div class="d-flex justify-content-between mb-2">
                                                    <span class="text-muted">Tax:</span>
                                                    <span>${{ number_format($order->tax, 2) }}</span>
                                                </div>
                                            @endif
                                            @if($order->shipping > 0)
                                                <div class="d-flex justify-content-between mb-2">
                                                    <span class="text-muted">Shipping:</span>
                                                    <span>${{ number_format($order->shipping, 2) }}</span>
                                                </div>
                                            @endif
                                            <hr>
                                            <div class="d-flex justify-content-between">
                                                <span class="fw-bold">Total:</span>
                                                <span class="fw-bold text-primary fs-5">${{ number_format($order->total, 2) }}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection

@push('styles')
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css">
<style>
    .badge {
        font-weight: 500;
        padding: 0.5em 0.8em;
    }

    .btn-primary {
        background-color: #428677;
        border-color: #428677;
    }

    .btn-primary:hover, .btn-primary:focus {
        background-color: #2d504b;
        border-color: #2d504b;
    }

    .btn-outline-primary {
        color: #428677;
        border-color: #428677;
    }

    .btn-outline-primary:hover, .btn-outline-primary:focus {
        background-color: #428677;
        border-color: #428677;
    }

    .text-primary {
        color: #428677 !important;
    }

    .bg-primary {
        background-color: #428677 !important;
    }

    .table > :not(caption) > * > * {
        padding: 1rem 0.75rem;
    }

    .breadcrumb-item a {
        color: #428677;
    }

    .breadcrumb-item.active {
        color: #7a868d;
    }

    .breadcrumb-item + .breadcrumb-item::before {
        color: #b1b7b8;
    }

    /* Toast Notification Styling */
    .toastify {
        padding: 16px 20px;
        border-radius: 8px;
        font-weight: 500;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    }

    .toast-success {
        background: linear-gradient(135deg, #428677, #2d504b);
    }

    .toast-error {
        background: linear-gradient(135deg, #e74c3c, #c0392b);
    }

    .toast-info {
        background: linear-gradient(135deg, #3498db, #2980b9);
    }

    .toast-warning {
        background: linear-gradient(135deg, #f39c12, #e67e22);
    }

    /* Order status badges with hover effect */
    .status-badge {
        transition: all 0.3s ease;
    }

    .status-badge:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    /* Print styles */
    @media print {
        .container {
            width: 100%;
            max-width: 100%;
        }

        .card {
            border: 1px solid #dee2e6 !important;
            box-shadow: none !important;
        }

        .no-print {
            display: none !important;
        }
    }
</style>
@endpush

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/toastify-js"></script>
<script>
    // Show notification when page loads
    document.addEventListener('DOMContentLoaded', function() {
        showNotification('Order details loaded successfully', 'success');
    });

    // Function to show notifications
    function showNotification(message, type = 'info') {
        const toastClass = type === 'success' ? 'toast-success' :
                          type === 'error' ? 'toast-error' :
                          type === 'warning' ? 'toast-warning' : 'toast-info';

        Toastify({
            text: message,
            duration: 5000,
            close: true,
            gravity: "top",
            position: "right",
            className: toastClass,
            stopOnFocus: true
        }).showToast();
    }

    // Function to show status information
    function showStatusInfo(status) {
        let message = '';
        let type = 'info';

        switch(status) {
            case 'pending':
                message = 'Your order is pending. We\'ll process it shortly.';
                type = 'warning';
                break;
            case 'processing':
                message = 'Your order is being processed. We\'re preparing your items for shipment.';
                type = 'info';
                break;
            case 'completed':
                message = 'Your order has been completed and shipped. Thank you for shopping with us!';
                type = 'success';
                break;
            case 'declined':
                message = 'Your order was declined. Please contact customer support for assistance.';
                type = 'error';
                break;
            default:
                message = 'Order status: ' + status;
        }

        showNotification(message, type);
    }

    // Function to show payment information
    function showPaymentInfo(status) {
        let message = '';
        let type = 'info';

        switch(status) {
            case 'paid':
                message = 'Payment received successfully. Thank you for your purchase!';
                type = 'success';
                break;
            case 'unpaid':
                message = 'Payment pending. Please complete your payment to process your order.';
                type = 'warning';
                break;
            default:
                message = 'Payment status: ' + status;
        }

        showNotification(message, type);
    }

    // Add animation to order items
    document.querySelectorAll('tbody tr').forEach((row, index) => {
        row.style.opacity = '0';
        row.style.transform = 'translateY(20px)';
        row.style.transition = 'all 0.3s ease';

        setTimeout(() => {
            row.style.opacity = '1';
            row.style.transform = 'translateY(0)';
        }, 100 + (index * 100));
    });

    // Function to show subscription information
    function showSubscriptionInfo(productName) {
        showNotification(`${productName} is a subscription product. You'll be billed regularly until you cancel.`, 'info');
    }

    // Function to confirm subscription cancellation
    function confirmCancelSubscription(itemId, productName) {
        // Create a modal dialog for confirmation
        const modalHtml = `
            <div class="modal fade" id="cancelSubscriptionModal" tabindex="-1" aria-labelledby="cancelSubscriptionModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header bg-light">
                            <h5 class="modal-title" id="cancelSubscriptionModalLabel">Cancel Subscription</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="text-center mb-4">
                                <i class="bi bi-exclamation-triangle text-warning" style="font-size: 3rem;"></i>
                            </div>
                            <p>Are you sure you want to cancel your subscription to <strong>${productName}</strong>?</p>
                            <p>This action cannot be undone. You will no longer receive regular shipments of this product.</p>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Keep Subscription</button>
                            <button type="button" class="btn btn-danger" id="confirmCancelBtn">
                                <i class="bi bi-x-circle me-1"></i>Yes, Cancel Subscription
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Add the modal to the document
        const modalContainer = document.createElement('div');
        modalContainer.innerHTML = modalHtml;
        document.body.appendChild(modalContainer);

        // Initialize the modal
        const modal = new bootstrap.Modal(document.getElementById('cancelSubscriptionModal'));
        modal.show();

        // Handle confirmation
        document.getElementById('confirmCancelBtn').addEventListener('click', function() {
            // Show processing notification
            showNotification('Processing your cancellation request...', 'info');

            // Hide the modal
            modal.hide();

            // Submit the cancellation form
            document.getElementById(`cancel-subscription-form-${itemId}`).submit();
        });

        // Clean up the modal when it's hidden
        document.getElementById('cancelSubscriptionModal').addEventListener('hidden.bs.modal', function() {
            document.body.removeChild(modalContainer);
        });
    }

    // Add click event to print button with notification
    document.querySelector('button[onclick="window.print()"]').addEventListener('click', function(e) {
        // Let the default print action happen, but also show a notification
        setTimeout(() => {
            showNotification('Preparing order for printing...', 'info');
        }, 100);
    });

    // Add hover effects to product images
    document.querySelectorAll('tbody img, .bg-light.rounded-3').forEach(img => {
        img.style.transition = 'transform 0.3s ease, box-shadow 0.3s ease';

        img.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.05)';
            this.style.boxShadow = '0 5px 15px rgba(0,0,0,0.1)';
        });

        img.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1)';
            this.style.boxShadow = 'none';
        });

        // Show product info on click
        img.addEventListener('click', function() {
            const productName = this.closest('tr').querySelector('h6').textContent;
            showNotification(`Product: ${productName}`, 'info');
        });
    });
</script>
@endpush
