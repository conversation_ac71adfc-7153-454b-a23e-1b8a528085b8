<?php

namespace App\Http\Controllers;

use App\Models\Subscription;
use App\Models\OrderItem;
use App\Models\Order;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Stripe\StripeClient;

class SubscriptionController extends Controller
{
    /**
     * Display a listing of the user's subscriptions.
     */
    public function index()
    {
        $user = Auth::user();
        $subscriptions = Subscription::where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->get();

        return view('subscriptions.index', compact('subscriptions'));
    }

    /**
     * Show the form for managing a specific subscription.
     */
    public function show(string $id)
    {
        $user = Auth::user();
        $subscription = Subscription::where('user_id', $user->id)
            ->where('id', $id)
            ->firstOrFail();

        return view('subscriptions.show', compact('subscription'));
    }

    /**
     * Cancel a subscription.
     */
    public function cancel(string $id)
    {
        $user = Auth::user();

        // Find the subscription
        $subscription = Subscription::where('user_id', $user->id)
            ->where('id', $id)
            ->firstOrFail();

        try {
            // If it's a Stripe subscription, cancel it through the API
            if ($subscription->stripe_id) {
                $stripe = new StripeClient(env('STRIPE_SECRET'));
                $stripe->subscriptions->cancel($subscription->stripe_id, [
                    'cancel_at_period_end' => true,
                ]);
            }

            // Update the local subscription record
            $subscription->stripe_status = 'canceled';
            $subscription->ends_at = now();
            $subscription->save();

            // Log the cancellation
            Log::info('Subscription canceled', [
                'user_id' => $user->id,
                'subscription_id' => $subscription->id,
                'stripe_id' => $subscription->stripe_id
            ]);

            return redirect()->back()->with('success', 'Your subscription has been canceled successfully.');
        } catch (\Exception $e) {
            Log::error('Error canceling subscription', [
                'user_id' => $user->id,
                'subscription_id' => $subscription->id,
                'error' => $e->getMessage()
            ]);

            return redirect()->back()->with('error', 'There was an error canceling your subscription: ' . $e->getMessage());
        }
    }

    /**
     * Cancel a subscription from an order item.
     */
    public function cancelFromOrder(string $orderId, string $itemId)
    {
        $user = Auth::user();

        // Find the order and verify it belongs to the user
        $order = Order::where('user_id', $user->id)
            ->where('id', $orderId)
            ->firstOrFail();

        // Find the order item and verify it's a subscription
        $orderItem = OrderItem::where('order_id', $order->id)
            ->where('id', $itemId)
            ->where('is_subscription', true)
            ->firstOrFail();

        // Find the associated subscription through subscription items
        // First, find subscription items that match the product ID
        $subscriptionItem = \App\Models\SubscriptionItem::where('stripe_product', $orderItem->product_id)
            ->first();

        // If we found a matching subscription item, get the subscription
        $subscription = null;
        if ($subscriptionItem) {
            $subscription = Subscription::where('user_id', $user->id)
                ->where('id', $subscriptionItem->subscription_id)
                ->first();
        }

        if (!$subscription) {
            return redirect()->back()->with('error', 'No active subscription found for this product.');
        }

        try {
            // If it's a Stripe subscription, cancel it through the API
            if ($subscription->stripe_id) {
                $stripe = new StripeClient(env('STRIPE_SECRET'));
                $stripe->subscriptions->cancel($subscription->stripe_id, [
                    'cancel_at_period_end' => true,
                ]);
            }

            // Update the local subscription record
            $subscription->stripe_status = 'canceled';
            $subscription->ends_at = now();
            $subscription->save();

            // Log the cancellation
            Log::info('Subscription canceled from order', [
                'user_id' => $user->id,
                'order_id' => $order->id,
                'item_id' => $orderItem->id,
                'subscription_id' => $subscription->id
            ]);

            return redirect()->back()->with('success', 'Your subscription has been canceled successfully.');
        } catch (\Exception $e) {
            Log::error('Error canceling subscription from order', [
                'user_id' => $user->id,
                'order_id' => $order->id,
                'item_id' => $orderItem->id,
                'error' => $e->getMessage()
            ]);

            return redirect()->back()->with('error', 'There was an error canceling your subscription: ' . $e->getMessage());
        }
    }
}
