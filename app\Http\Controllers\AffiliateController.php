<?php

namespace App\Http\Controllers;

use App\Models\AffiliateCode;
use App\Models\AffiliateEarning;
use App\Models\User;
use Artesaos\SEOTools\Facades\SEOMeta;
use Artesaos\SEOTools\Facades\OpenGraph;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class AffiliateController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        // Middleware is applied in the routes file
    }

    /**
     * Display the affiliate dashboard.
     */
    public function index()
    {
        // Set SEO metadata
        SEOMeta::setTitle('Affiliate Dashboard - NatRyon');
        SEOMeta::setDescription('Manage your affiliate program with NatRyon.');
        SEOMeta::setCanonical(url('/affiliate'));

        OpenGraph::setTitle('Affiliate Dashboard - NatRyon');
        OpenGraph::setDescription('Manage your affiliate program with NatRyon.');
        OpenGraph::setUrl(url('/affiliate'));
        OpenGraph::addProperty('type', 'website');

        // Get the user's affiliate code
        $affiliateCode = AffiliateCode::where('user_id', Auth::id())->first();

        // Get the user's affiliate earnings
        $earnings = AffiliateEarning::where('user_id', Auth::id())
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        // Calculate total earnings
        $totalEarnings = AffiliateEarning::where('user_id', Auth::id())
            ->where('status', 'approved')
            ->sum('commission_amount');

        // Calculate pending earnings
        $pendingEarnings = AffiliateEarning::where('user_id', Auth::id())
            ->where('status', 'pending')
            ->sum('commission_amount');

        // Get referrals (users who used this user's affiliate code)
        $referrals = [];
        if ($affiliateCode) {
            $referrals = User::whereHas('orders', function ($query) use ($affiliateCode) {
                $query->where('affiliate_code', $affiliateCode->code);
            })->get();
        }

        return view('affiliate.index', compact('affiliateCode', 'earnings', 'totalEarnings', 'pendingEarnings', 'referrals'));
    }

    /**
     * Generate an affiliate code for the user.
     */
    public function generateCode(Request $request)
    {
        // Check if user already has an affiliate code
        $existingCode = AffiliateCode::where('user_id', Auth::id())->first();

        if ($existingCode) {
            return redirect()->route('affiliate.index')->with('info', 'You already have an affiliate code: ' . $existingCode->code);
        }

        // Get the parent affiliate code if provided
        $parentUserId = null;
        if ($request->has('parent_code')) {
            $parentCode = AffiliateCode::where('code', $request->parent_code)
                ->where('is_active', true)
                ->first();

            if ($parentCode && $parentCode->user_id != Auth::id()) {
                $parentUserId = $parentCode->user_id;
            }
        }

        // Generate a new affiliate code
        $user = Auth::user();
        $affiliateCode = $user->generateAffiliateCode($parentUserId);

        return redirect()->route('affiliate.index')->with('success', 'Affiliate code generated successfully: ' . $affiliateCode->code);
    }

    /**
     * Show the affiliate earnings.
     */
    public function earnings()
    {
        // Set SEO metadata
        SEOMeta::setTitle('Affiliate Earnings - NatRyon');
        SEOMeta::setDescription('View your affiliate earnings with NatRyon.');
        SEOMeta::setCanonical(url('/affiliate/earnings'));

        OpenGraph::setTitle('Affiliate Earnings - NatRyon');
        OpenGraph::setDescription('View your affiliate earnings with NatRyon.');
        OpenGraph::setUrl(url('/affiliate/earnings'));
        OpenGraph::addProperty('type', 'website');

        // Get the user's affiliate earnings
        $earnings = AffiliateEarning::where('user_id', Auth::id())
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        // Calculate total earnings
        $totalEarnings = AffiliateEarning::where('user_id', Auth::id())
            ->where('status', 'approved')
            ->sum('commission_amount');

        // Calculate pending earnings
        $pendingEarnings = AffiliateEarning::where('user_id', Auth::id())
            ->where('status', 'pending')
            ->sum('commission_amount');

        return view('affiliate.earnings', compact('earnings', 'totalEarnings', 'pendingEarnings'));
    }

    /**
     * Show the affiliate referrals.
     */
    public function referrals()
    {
        // Set SEO metadata
        SEOMeta::setTitle('Affiliate Referrals - NatRyon');
        SEOMeta::setDescription('View your affiliate referrals with NatRyon.');
        SEOMeta::setCanonical(url('/affiliate/referrals'));

        OpenGraph::setTitle('Affiliate Referrals - NatRyon');
        OpenGraph::setDescription('View your affiliate referrals with NatRyon.');
        OpenGraph::setUrl(url('/affiliate/referrals'));
        OpenGraph::addProperty('type', 'website');

        // Get the user's affiliate code
        $affiliateCode = AffiliateCode::where('user_id', Auth::id())->first();

        // Get referrals (users who used this user's affiliate code)
        $referrals = [];
        if ($affiliateCode) {
            $referrals = User::whereHas('orders', function ($query) use ($affiliateCode) {
                $query->where('affiliate_code', $affiliateCode->code);
            })->paginate(20);
        }

        return view('affiliate.referrals', compact('affiliateCode', 'referrals'));
    }
}
