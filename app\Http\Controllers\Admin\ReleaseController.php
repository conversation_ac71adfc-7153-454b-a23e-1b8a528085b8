<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Release;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class ReleaseController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $releases = Release::with(['creator', 'approver'])
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('admin.releases.index', compact('releases'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.releases.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'version' => 'required|string|max:50|unique:releases,version',
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $release = new Release();
        $release->version = $request->version;
        $release->name = $request->name;
        $release->description = $request->description;
        $release->status = 'draft';
        $release->created_by = Auth::id();
        $release->save();

        return redirect()->route('admin.releases.show', $release->id)
            ->with('success', 'Release created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $release = Release::with(['creator', 'approver', 'checkpoints', 'partitions'])
            ->findOrFail($id);

        return view('admin.releases.show', compact('release'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $release = Release::findOrFail($id);

        // Only allow editing of releases in draft status
        if ($release->status !== 'draft') {
            return redirect()->route('admin.releases.show', $release->id)
                ->with('error', 'Only draft releases can be edited.');
        }

        return view('admin.releases.edit', compact('release'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $release = Release::findOrFail($id);

        // Only allow updating of releases in draft status
        if ($release->status !== 'draft') {
            return redirect()->route('admin.releases.show', $release->id)
                ->with('error', 'Only draft releases can be updated.');
        }

        $validator = Validator::make($request->all(), [
            'version' => 'required|string|max:50|unique:releases,version,' . $id,
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $release->version = $request->version;
        $release->name = $request->name;
        $release->description = $request->description;
        $release->save();

        return redirect()->route('admin.releases.show', $release->id)
            ->with('success', 'Release updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $release = Release::findOrFail($id);

        // Only allow deletion of releases in draft status
        if ($release->status !== 'draft') {
            return redirect()->route('admin.releases.show', $release->id)
                ->with('error', 'Only draft releases can be deleted.');
        }

        $release->delete();

        return redirect()->route('admin.releases.index')
            ->with('success', 'Release deleted successfully.');
    }

    /**
     * Submit the release for testing.
     */
    public function submitForTesting(string $id)
    {
        $release = Release::findOrFail($id);

        // Only allow submission of releases in draft status
        if ($release->status !== 'draft') {
            return redirect()->route('admin.releases.show', $release->id)
                ->with('error', 'Only draft releases can be submitted for testing.');
        }

        // Check if the release has at least one checkpoint and one partition
        if ($release->checkpoints()->count() === 0) {
            return redirect()->route('admin.releases.show', $release->id)
                ->with('error', 'Release must have at least one checkpoint before it can be submitted for testing.');
        }

        if ($release->partitions()->count() === 0) {
            return redirect()->route('admin.releases.show', $release->id)
                ->with('error', 'Release must have at least one partition before it can be submitted for testing.');
        }

        $release->status = 'testing';
        $release->save();

        return redirect()->route('admin.releases.show', $release->id)
            ->with('success', 'Release submitted for testing successfully.');
    }

    /**
     * Approve the release for deployment.
     */
    public function approve(string $id)
    {
        $release = Release::findOrFail($id);

        // Only allow approval of releases in testing status
        if ($release->status !== 'testing') {
            return redirect()->route('admin.releases.show', $release->id)
                ->with('error', 'Only releases in testing status can be approved.');
        }

        // Check if all checkpoints have passed
        if (!$release->allCheckpointsPassed()) {
            return redirect()->route('admin.releases.show', $release->id)
                ->with('error', 'All checkpoints must pass before the release can be approved.');
        }

        $release->status = 'approved';
        $release->approved_by = Auth::id();
        $release->save();

        return redirect()->route('admin.releases.show', $release->id)
            ->with('success', 'Release approved successfully.');
    }

    /**
     * Deploy the release.
     */
    public function deploy(string $id)
    {
        $release = Release::findOrFail($id);

        // Only allow deployment of releases in approved status
        if ($release->status !== 'approved') {
            return redirect()->route('admin.releases.show', $release->id)
                ->with('error', 'Only approved releases can be deployed.');
        }

        // In a real application, you would implement the actual deployment logic here
        // For now, we'll just mark the release as deployed
        $release->status = 'deployed';
        $release->deployed_at = now();
        $release->save();

        return redirect()->route('admin.releases.show', $release->id)
            ->with('success', 'Release deployed successfully.');
    }
}
