@extends('layouts.admin')

@section('title', 'Partitions')

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Partitions</h1>
        <a href="{{ route('admin.partitions.create') }}" class="btn btn-primary">
            <i class="bi bi-plus-circle"></i> New Partition
        </a>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold">All Partitions</h6>
        </div>
        <div class="card-body">
            @if($partitions->isEmpty())
                <div class="alert alert-info">
                    No partitions found. Click the "New Partition" button to create one.
                </div>
            @else
                <div class="table-responsive">
                    <table class="table table-bordered table-hover" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Release</th>
                                <th>Type</th>
                                <th>Order</th>
                                <th>Status</th>
                                <th>Deployed At</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($partitions as $partition)
                                <tr>
                                    <td>{{ $partition->name }}</td>
                                    <td>
                                        <a href="{{ route('admin.releases.show', $partition->release_id) }}">
                                            {{ $partition->release->version }} - {{ $partition->release->name }}
                                        </a>
                                    </td>
                                    <td>{{ ucfirst($partition->type) }}</td>
                                    <td>{{ $partition->deployment_order }}</td>
                                    <td>
                                        @switch($partition->status)
                                            @case('pending')
                                                <span class="badge bg-secondary">Pending</span>
                                                @break
                                            @case('deployed')
                                                <span class="badge bg-success">Deployed</span>
                                                @break
                                            @case('failed')
                                                <span class="badge bg-danger">Failed</span>
                                                @break
                                            @case('rolled_back')
                                                <span class="badge bg-warning">Rolled Back</span>
                                                @break
                                            @default
                                                <span class="badge bg-secondary">{{ $partition->status }}</span>
                                        @endswitch
                                    </td>
                                    <td>{{ $partition->deployed_at ? $partition->deployed_at->format('Y-m-d H:i') : 'Not deployed' }}</td>
                                    <td>
                                        <a href="{{ route('admin.partitions.show', $partition->id) }}" class="btn btn-sm btn-info">
                                            <i class="bi bi-eye"></i> View
                                        </a>
                                        @if($partition->release->status === 'draft')
                                            <a href="{{ route('admin.partitions.edit', $partition->id) }}" class="btn btn-sm btn-primary">
                                                <i class="bi bi-pencil"></i> Edit
                                            </a>
                                            <form action="{{ route('admin.partitions.destroy', $partition->id) }}" method="POST" class="d-inline">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to delete this partition?')">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </form>
                                        @endif
                                        @if($partition->release->status === 'approved' && $partition->status === 'pending')
                                            <button type="button" class="btn btn-sm btn-success" data-bs-toggle="modal" data-bs-target="#deployPartitionModal{{ $partition->id }}">
                                                <i class="bi bi-rocket"></i> Deploy
                                            </button>
                                        @endif
                                    </td>
                                </tr>

                                <!-- Deploy Partition Modal -->
                                <div class="modal fade" id="deployPartitionModal{{ $partition->id }}" tabindex="-1" aria-labelledby="deployPartitionModalLabel{{ $partition->id }}" aria-hidden="true">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <form action="{{ route('admin.partitions.deploy', $partition->id) }}" method="POST">
                                                @csrf
                                                <div class="modal-header">
                                                    <h5 class="modal-title" id="deployPartitionModalLabel{{ $partition->id }}">Deploy Partition: {{ $partition->name }}</h5>
                                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                </div>
                                                <div class="modal-body">
                                                    <div class="mb-3">
                                                        <label for="deployment_notes" class="form-label">Deployment Notes</label>
                                                        <textarea class="form-control" id="deployment_notes" name="deployment_notes" rows="3" placeholder="Enter any notes about the deployment process"></textarea>
                                                    </div>
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                    <button type="submit" class="btn btn-success">Deploy</button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                <div class="mt-4">
                    {{ $partitions->links() }}
                </div>
            @endif
        </div>
    </div>
</div>
@endsection
