<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class RoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create roles
        $adminRole = Role::create(['name' => 'admin']);
        $clientRole = Role::create(['name' => 'client']);

        // Create permissions
        $permissions = [
            // Product permissions
            'view products',
            'create products',
            'edit products',
            'delete products',

            // Order permissions
            'view orders',
            'create orders',
            'edit orders',
            'delete orders',

            // User permissions
            'view users',
            'create users',
            'edit users',
            'delete users',

            // Affiliate permissions
            'view affiliates',
            'create affiliates',
            'edit affiliates',
            'delete affiliates',
        ];

        foreach ($permissions as $permission) {
            Permission::create(['name' => $permission]);
        }

        // Assign all permissions to admin role
        $adminRole->givePermissionTo($permissions);

        // Assign limited permissions to client role
        $clientRole->givePermissionTo([
            'view products',
            'create orders',
            'view orders',
        ]);
    }
}
