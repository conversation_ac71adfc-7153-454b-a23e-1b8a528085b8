<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Release extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'version',
        'name',
        'description',
        'status',
        'created_by',
        'approved_by',
        'deployed_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'deployed_at' => 'datetime',
    ];

    /**
     * Get the user who created the release.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who approved the release.
     */
    public function approver(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Get the checkpoints for the release.
     */
    public function checkpoints(): <PERSON><PERSON><PERSON>
    {
        return $this->hasMany(Checkpoint::class);
    }

    /**
     * Get the partitions for the release.
     */
    public function partitions(): Has<PERSON><PERSON>
    {
        return $this->hasMany(Partition::class);
    }

    /**
     * Check if all checkpoints have passed.
     */
    public function allCheckpointsPassed(): bool
    {
        return $this->checkpoints()->count() > 0 &&
               $this->checkpoints()->where('status', '!=', 'passed')->count() === 0;
    }

    /**
     * Check if any checkpoint has failed.
     */
    public function hasFailedCheckpoints(): bool
    {
        return $this->checkpoints()->where('status', 'failed')->count() > 0;
    }

    /**
     * Get the percentage of checkpoints that have passed.
     */
    public function checkpointProgress(): int
    {
        $total = $this->checkpoints()->count();
        if ($total === 0) {
            return 0;
        }

        $passed = $this->checkpoints()->where('status', 'passed')->count();
        return (int) (($passed / $total) * 100);
    }

    /**
     * Get the percentage of partitions that have been deployed.
     */
    public function deploymentProgress(): int
    {
        $total = $this->partitions()->count();
        if ($total === 0) {
            return 0;
        }

        $deployed = $this->partitions()->where('status', 'deployed')->count();
        return (int) (($deployed / $total) * 100);
    }
}
