<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\AffiliateCode;
use App\Models\AffiliateEarning;
use App\Models\User;
use Illuminate\Support\Str;

class AdminAffiliateController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = AffiliateCode::with(['user', 'user.orders']);

        // Search
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('code', 'like', "%{$search}%")
                  ->orWhereHas('user', function ($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%")
                                ->orWhere('email', 'like', "%{$search}%");
                  });
            });
        }

        // Status filter
        if ($request->has('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        // Earnings filter
        if ($request->has('earnings')) {
            $query->whereHas('user.affiliateEarnings', function ($q) use ($request) {
                $q->where('status', $request->earnings);
            });
        }

        // Sort
        if ($request->has('sort')) {
            switch ($request->sort) {
                case 'name_asc':
                    $query->whereHas('user', function ($q) {
                        $q->orderBy('name', 'asc');
                    });
                    break;
                case 'name_desc':
                    $query->whereHas('user', function ($q) {
                        $q->orderBy('name', 'desc');
                    });
                    break;
                case 'earnings_high':
                    $query->withCount(['user.affiliateEarnings as earnings_sum' => function ($q) {
                        $q->select(\DB::raw('SUM(commission_amount)'));
                    }])->orderBy('earnings_sum', 'desc');
                    break;
                case 'earnings_low':
                    $query->withCount(['user.affiliateEarnings as earnings_sum' => function ($q) {
                        $q->select(\DB::raw('SUM(commission_amount)'));
                    }])->orderBy('earnings_sum', 'asc');
                    break;
                case 'referrals_high':
                    $query->withCount(['referrals'])->orderBy('referrals_count', 'desc');
                    break;
                case 'referrals_low':
                    $query->withCount(['referrals'])->orderBy('referrals_count', 'asc');
                    break;
                default:
                    $query->orderBy('created_at', 'desc');
            }
        } else {
            $query->orderBy('created_at', 'desc');
        }

        $affiliates = $query->paginate(10);
        
        // Get statistics
        $totalAffiliates = AffiliateCode::count();
        $totalEarnings = AffiliateEarning::sum('commission_amount');
        $pendingEarnings = AffiliateEarning::where('status', 'pending')->sum('commission_amount');
        $paidEarnings = AffiliateEarning::where('status', 'paid')->sum('commission_amount');
        
        // Get recent earnings
        $recentEarnings = AffiliateEarning::with(['user', 'order'])->orderBy('created_at', 'desc')->take(5)->get();

        return view('admin.affiliates.index', compact(
            'affiliates', 
            'totalAffiliates', 
            'totalEarnings', 
            'pendingEarnings', 
            'paidEarnings', 
            'recentEarnings'
        ));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $users = User::doesntHave('affiliateCodes')->get();
        return view('admin.affiliates.create', compact('users'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
            'code' => 'nullable|string|max:20|unique:affiliate_codes,code',
            'commission_rate' => 'required|numeric|min:0|max:100',
        ]);

        $code = $request->code;
        if (empty($code)) {
            $user = User::find($request->user_id);
            $code = Str::slug($user->name) . '-' . Str::random(6);
        }

        AffiliateCode::create([
            'user_id' => $request->user_id,
            'code' => $code,
            'commission_rate' => $request->commission_rate,
            'is_active' => true,
        ]);

        return redirect()->route('admin.affiliates.index')->with('success', 'Affiliate code created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $affiliate = AffiliateCode::with(['user', 'user.affiliateEarnings'])->findOrFail($id);
        
        // Get referrals
        $referrals = User::whereHas('orders', function ($query) use ($affiliate) {
            $query->where('affiliate_code', $affiliate->code);
        })->get();
        
        // Get earnings
        $earnings = $affiliate->user->affiliateEarnings()->with('order')->paginate(10);
        
        // Calculate statistics
        $totalEarnings = $affiliate->user->affiliateEarnings->sum('commission_amount');
        $pendingEarnings = $affiliate->user->affiliateEarnings->where('status', 'pending')->sum('commission_amount');
        $paidEarnings = $affiliate->user->affiliateEarnings->where('status', 'paid')->sum('commission_amount');
        
        return view('admin.affiliates.show', compact(
            'affiliate', 
            'referrals', 
            'earnings', 
            'totalEarnings', 
            'pendingEarnings', 
            'paidEarnings'
        ));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $affiliate = AffiliateCode::findOrFail($id);
        return view('admin.affiliates.edit', compact('affiliate'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $affiliate = AffiliateCode::findOrFail($id);
        
        if ($request->has('is_active') !== null) {
            $affiliate->is_active = $request->is_active;
            $affiliate->save();
            return redirect()->back()->with('success', 'Affiliate status updated successfully.');
        }
        
        $request->validate([
            'code' => 'required|string|max:20|unique:affiliate_codes,code,' . $affiliate->id,
            'commission_rate' => 'required|numeric|min:0|max:100',
        ]);
        
        $affiliate->code = $request->code;
        $affiliate->commission_rate = $request->commission_rate;
        $affiliate->save();
        
        return redirect()->route('admin.affiliates.index')->with('success', 'Affiliate updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $affiliate = AffiliateCode::findOrFail($id);
        $affiliate->delete();
        
        return redirect()->route('admin.affiliates.index')->with('success', 'Affiliate deleted successfully.');
    }
    
    /**
     * Update an affiliate earning status.
     */
    public function updateEarning(Request $request, string $id)
    {
        $earning = AffiliateEarning::findOrFail($id);
        
        $request->validate([
            'status' => 'required|in:pending,approved,paid,rejected',
        ]);
        
        $earning->status = $request->status;
        
        if ($request->status === 'paid' && !$earning->paid_at) {
            $earning->paid_at = now();
        }
        
        $earning->save();
        
        return redirect()->back()->with('success', 'Earning status updated successfully.');
    }
    
    /**
     * Display a listing of all affiliate earnings.
     */
    public function earnings(Request $request)
    {
        $query = AffiliateEarning::with(['user', 'order']);
        
        // Search
        if ($request->has('search')) {
            $search = $request->search;
            $query->whereHas('user', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }
        
        // Status filter
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }
        
        // User filter
        if ($request->has('user')) {
            $query->where('user_id', $request->user);
        }
        
        // Sort
        if ($request->has('sort')) {
            switch ($request->sort) {
                case 'amount_high':
                    $query->orderBy('commission_amount', 'desc');
                    break;
                case 'amount_low':
                    $query->orderBy('commission_amount', 'asc');
                    break;
                case 'newest':
                    $query->orderBy('created_at', 'desc');
                    break;
                case 'oldest':
                    $query->orderBy('created_at', 'asc');
                    break;
                default:
                    $query->orderBy('created_at', 'desc');
            }
        } else {
            $query->orderBy('created_at', 'desc');
        }
        
        $earnings = $query->paginate(10);
        
        return view('admin.affiliates.earnings', compact('earnings'));
    }
}
