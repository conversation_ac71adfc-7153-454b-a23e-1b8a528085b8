<?php

namespace App\Http\Controllers;

use App\Models\AffiliateCode;
use App\Models\AffiliateEarning;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Product;
use App\Models\User;
use Artesaos\SEOTools\Facades\SEOMeta;
use Artesaos\SEOTools\Facades\OpenGraph;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;
use Stripe\Exception\ApiErrorException;
use Stripe\PaymentIntent;
use Stripe\Stripe;
use Barryvdh\DomPDF\Facade\Pdf;
use App\Mail\OrderInvoice;
use App\Mail\AdminOrderNotification;
use Spatie\Permission\Models\Role;

class OrderController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        // Middleware is applied in the routes file
    }

    /**
     * Display a listing of the user's orders.
     */
    public function index()
    {
        // Set SEO metadata
        SEOMeta::setTitle('My Orders - NatRyon');
        SEOMeta::setDescription('View your order history with NatRyon.');
        SEOMeta::setCanonical(url('/orders'));

        OpenGraph::setTitle('My Orders - NatRyon');
        OpenGraph::setDescription('View your order history with NatRyon.');
        OpenGraph::setUrl(url('/orders'));
        OpenGraph::addProperty('type', 'website');

        // Get the user's orders
        $orders = Order::where('user_id', Auth::id())
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('orders.index', compact('orders'));
    }

    /**
     * Store a newly created order in storage.
     */
    public function store(Request $request)
    {
        // Log the request for debugging
        \Illuminate\Support\Facades\Log::info('Order submission received', [
            'request_data' => $request->except(['payment_token']),
            'user_id' => auth()->id(),
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'billing_zipcode' => $request->billing_zipcode,
            'billing_country' => $request->billing_country
        ]);

        // Basic validation rules
        $validationRules = [
            'billing_name' => 'required|string|max:255',
            'billing_email' => 'required|email|max:255',
            'billing_phone' => 'required|string|max:20',
            'billing_address' => 'required|string|max:255',
            'billing_city' => 'required|string|max:255',
            'billing_state' => 'nullable|string|max:255',
            'billing_zipcode' => 'required|string|max:20',
            'billing_country' => 'required|string|max:255',
            'shipping_name' => 'nullable|string|max:255',
            'shipping_address' => 'nullable|string|max:255',
            'shipping_city' => 'nullable|string|max:255',
            'shipping_state' => 'nullable|string|max:255',
            'shipping_zipcode' => 'nullable|string|max:20',
            'shipping_country' => 'nullable|string|max:255',
            'payment_method' => 'required|in:stripe,cod',
            'payment_token' => 'required_if:payment_method,stripe',
            'payment_amount' => 'required_if:payment_method,stripe|numeric',
            'notes' => 'nullable|string',
        ];

        // Validate the request
        $validated = $request->validate($validationRules);

        // Additional validation for zip code based on country
        $zipCode = $request->billing_zipcode;
        $country = $request->billing_country;

        // Log zip code validation
        \Illuminate\Support\Facades\Log::info('Validating zip code', [
            'zipcode' => $zipCode,
            'country' => $country
        ]);

        // Get the cart from the session
        $cart = session()->get('cart', []);

        // Check if cart is empty
        if (empty($cart)) {
            return redirect()->route('cart.index')->with('error', 'Your cart is empty.');
        }

        // Check if cart has subscription products and payment method is COD
        $hasSubscription = false;

        foreach ($cart as $item) {
            if (isset($item['is_subscription']) && $item['is_subscription']) {
                $hasSubscription = true;
                break;
            }
        }

        // If there are subscription products and payment method is COD, return an error
        if ($hasSubscription && $request->payment_method === 'cod') {
            return back()->with('error', 'Cash on Delivery is not available for subscription products. Please use a credit card.');
        }

        // Start a database transaction
        DB::beginTransaction();

        try {
            // Calculate cart totals
            $subtotal = 0;
            foreach ($cart as $item) {
                $subtotal += $item['price'] * $item['quantity'];
            }

            // Get affiliate code from session
            $affiliateCode = session()->get('affiliate_code');
            $affiliateCodeModel = null;

            if ($affiliateCode) {
                // Verify the affiliate code is valid
                $affiliateCodeModel = AffiliateCode::where('code', $affiliateCode)
                    ->where('is_active', true)
                    ->first();

                if (!$affiliateCodeModel) {
                    // Invalid code, remove from session
                    session()->forget('affiliate_code');
                    $affiliateCode = null;
                }
            }

            $total = $subtotal;

            // Create the order
            $order = Order::create([
                'user_id' => Auth::id(),
                'order_number' => 'ORD-' . strtoupper(Str::random(10)),
                'status' => 'pending',
                'subtotal' => $subtotal,
                'tax' => 0, // You can calculate tax here if needed
                'shipping' => 0, // You can calculate shipping here if needed
                'total' => $total,
                'payment_method' => $request->payment_method,
                'payment_id' => $request->payment_token,
                'is_paid' => false, // Will be updated after payment processing
                'billing_name' => $request->billing_name,
                'billing_email' => $request->billing_email,
                'billing_phone' => $request->billing_phone,
                'billing_address' => $request->billing_address,
                'billing_city' => $request->billing_city,
                'billing_state' => $request->billing_state,
                'billing_zipcode' => $request->billing_zipcode,
                'billing_country' => $request->billing_country,
                'shipping_name' => $request->shipping_name ?? $request->billing_name,
                'shipping_address' => $request->shipping_address ?? $request->billing_address,
                'shipping_city' => $request->shipping_city ?? $request->billing_city,
                'shipping_state' => $request->shipping_state ?? $request->billing_state,
                'shipping_zipcode' => $request->shipping_zipcode ?? $request->billing_zipcode,
                'shipping_country' => $request->shipping_country ?? $request->billing_country,
                'notes' => $request->notes,
                'affiliate_code' => $affiliateCode,
            ]);

            // Create order items
            foreach ($cart as $item) {
                $product = Product::findOrFail($item['id']);

                // Create order item
                OrderItem::create([
                    'order_id' => $order->id,
                    'product_id' => $product->id,
                    'product_name' => $product->name,
                    'quantity' => $item['quantity'],
                    'price' => $item['price'],
                    'is_subscription' => $item['is_subscription'],
                ]);

                // Update product stock
                $product->stock -= $item['quantity'];
                $product->save();

                // Create subscription if needed
                if ($item['is_subscription']) {
                    // Here you would typically create a subscription using Stripe
                    // For now, we'll just note that it's a subscription in the order item
                }
            }

            // Process affiliate earnings if applicable
            if ($affiliateCodeModel) {
                // Increment usage count
                $affiliateCodeModel->usage_count += 1;
                $affiliateCodeModel->save();

                // Calculate 10% commission for the affiliate
                $commissionAmount = $subtotal * 0.10; // 10% commission

                // Create affiliate earning for the direct referrer
                AffiliateEarning::create([
                    'user_id' => $affiliateCodeModel->user_id,
                    'order_id' => $order->id,
                    'affiliate_code_id' => $affiliateCodeModel->id,
                    'order_amount' => $subtotal,
                    'commission_rate' => 10.00, // Fixed 10% commission
                    'commission_amount' => $commissionAmount,
                    'level' => 1,
                    'status' => 'pending',
                ]);

                // Update user's referral count and earnings
                $referrer = User::find($affiliateCodeModel->user_id);
                $referrer->referral_count += 1;
                $referrer->affiliate_earnings += $commissionAmount;
                $referrer->save();

                // Process second-level affiliate earnings if applicable
                if ($affiliateCodeModel->parent_user_id) {
                    $secondLevelCommission = $commissionAmount * 0.1; // 10% of the first level commission

                    // Create affiliate earning for the second-level referrer
                    AffiliateEarning::create([
                        'user_id' => $affiliateCodeModel->parent_user_id,
                        'order_id' => $order->id,
                        'affiliate_code_id' => $affiliateCodeModel->id,
                        'order_amount' => $subtotal,
                        'commission_rate' => 1.00, // 1% of total (10% of 10%)
                        'commission_amount' => $secondLevelCommission,
                        'level' => 2,
                        'status' => 'pending',
                    ]);

                    // Update user's earnings
                    $secondLevelReferrer = User::find($affiliateCodeModel->parent_user_id);
                    $secondLevelReferrer->affiliate_earnings += $secondLevelCommission;
                    $secondLevelReferrer->save();
                }
            }

            // Process payment based on the selected method
            if ($request->payment_method === 'stripe') {
                try {
                    // Set your secret key
                    Stripe::setApiKey(env('STRIPE_SECRET'));

                    // Verify that the payment amount matches the order total
                    if (isset($request->payment_amount) && abs($request->payment_amount - $total) > 0.01) {
                        throw new \Exception('Payment amount mismatch. Expected: ' . $total . ', Received: ' . $request->payment_amount);
                    }

                    // Convert total to cents (Stripe requires amount in cents)
                    $amountInCents = (int)($total * 100);

                    // Create a PaymentIntent with amount and currency
                    $paymentIntent = PaymentIntent::create([
                        'amount' => $amountInCents,
                        'currency' => 'usd',
                        'payment_method' => $request->payment_token,
                        'confirm' => true,
                        'description' => 'Order #' . $order->order_number,
                        'receipt_email' => $order->billing_email,
                        'return_url' => route('orders.payment.callback', ['order_id' => $order->id]),
                        'metadata' => [
                            'order_id' => $order->id,
                            'order_number' => $order->order_number,
                            'customer_name' => $order->billing_name,
                            'customer_email' => $order->billing_email,
                            'total_amount' => $total,
                        ],
                        'automatic_payment_methods' => [
                            'enabled' => true,
                            'allow_redirects' => 'always',
                        ],
                    ]);

                    // Update order with payment details
                    $order->payment_id = $paymentIntent->id;

                    if ($paymentIntent->status === 'succeeded') {
                        // Payment succeeded
                        $order->is_paid = true;
                        $order->paid_at = now();
                        $order->status = 'processing';
                    } else {
                        // Payment is pending or requires action
                        $order->is_paid = false;
                        $order->status = 'pending';
                    }

                    $order->save();

                } catch (\Exception $stripeException) {
                    // Log the error
                    \Illuminate\Support\Facades\Log::error('Stripe payment failed: ' . $stripeException->getMessage());

                    // Mark order as declined (since payment_failed is not an allowed status)
                    $order->status = 'declined';
                    $order->save();

                    throw $stripeException;
                }
            } else if ($request->payment_method === 'cod') {
                // For Cash on Delivery, set the order status to 'processing'
                $order->payment_id = $request->payment_token; // This will be 'cod_timestamp'
                $order->is_paid = false; // Will be paid upon delivery
                $order->status = 'processing';
                $order->save();

                // Log the COD order
                \Illuminate\Support\Facades\Log::info('Cash on Delivery order created: ' . $order->order_number);
            }

            // Clear the cart and affiliate code from the session
            session()->forget(['cart', 'affiliate_code']);

            // Commit the transaction
            DB::commit();

            // Generate PDF invoice
            $pdf = PDF::loadView('admin.orders.pdf', compact('order'));

            // Send order confirmation email with invoice
            try {
                Mail::to($order->billing_email)->send(new OrderInvoice($order, $pdf));

                // Send notification to admin users
                $adminRole = Role::where('name', 'admin')->first();
                if ($adminRole) {
                    $adminUsers = User::role($adminRole)->get();
                    foreach ($adminUsers as $admin) {
                        Mail::to($admin->email)->send(new AdminOrderNotification($order, $pdf));
                    }

                    // Log admin notification
                    \Illuminate\Support\Facades\Log::info('Admin order notification sent to ' . $adminUsers->count() . ' admin users');
                }
            } catch (\Exception $e) {
                // Log the error but don't stop the order process
                \Illuminate\Support\Facades\Log::error('Failed to send order email: ' . $e->getMessage());
            }

            return redirect()->route('orders.show', $order->id)->with('success', 'Order placed successfully! An invoice has been sent to your email.');
        } catch (\Exception $e) {
            // Rollback the transaction if something goes wrong
            DB::rollBack();

            // Log the error
            \Illuminate\Support\Facades\Log::error('Order processing failed: ' . $e->getMessage());

            return back()->with('error', 'An error occurred while processing your order: ' . $e->getMessage());
        }
    }

    /**
     * Handle the payment callback after a redirect.
     */
    public function paymentCallback(Request $request, $order_id)
    {
        // Get the order
        $order = Order::findOrFail($order_id);

        // Verify that the user owns this order
        if (Auth::id() !== $order->user_id) {
            return redirect()->route('orders.index')->with('error', 'Unauthorized access.');
        }

        // Check the payment status
        try {
            // Set your secret key
            Stripe::setApiKey(env('STRIPE_SECRET'));

            // Retrieve the payment intent
            $paymentIntent = PaymentIntent::retrieve($order->payment_id);

            // Update order status based on payment status
            if ($paymentIntent->status === 'succeeded') {
                $order->is_paid = true;
                $order->paid_at = now();
                $order->status = 'processing';
                $order->save();

                return redirect()->route('orders.show', $order->id)->with('success', 'Payment completed successfully!');
            } else if ($paymentIntent->status === 'requires_action') {
                // Payment requires additional action
                return redirect()->route('orders.show', $order->id)->with('info', 'Your payment requires additional verification. Please check your email for instructions.');
            } else {
                // Payment failed or is still pending
                $order->status = 'pending';
                $order->save();

                return redirect()->route('orders.show', $order->id)->with('warning', 'Payment is still being processed. We will update your order status soon.');
            }
        } catch (\Exception $e) {
            // Log the error
            \Illuminate\Support\Facades\Log::error('Payment callback error: ' . $e->getMessage());

            return redirect()->route('orders.show', $order->id)->with('error', 'There was an error checking your payment status. Please contact customer support.');
        }
    }

    /**
     * Display the specified order.
     */
    public function show(string $id)
    {
        $order = Order::with('items.product')
            ->where('user_id', Auth::id())
            ->findOrFail($id);

        // Set SEO metadata
        SEOMeta::setTitle('Order #' . $order->order_number . ' - NatRyon');
        SEOMeta::setDescription('View details for your order with NatRyon.');
        SEOMeta::setCanonical(url('/orders/' . $order->id));

        OpenGraph::setTitle('Order #' . $order->order_number . ' - NatRyon');
        OpenGraph::setDescription('View details for your order with NatRyon.');
        OpenGraph::setUrl(url('/orders/' . $order->id));
        OpenGraph::addProperty('type', 'website');

        return view('orders.show', compact('order'));
    }

    /**
     * Generate an affiliate code for the user.
     */
    public function generateAffiliateCode(Request $request)
    {
        // Check if user already has an affiliate code
        $existingCode = AffiliateCode::where('user_id', Auth::id())->first();

        if ($existingCode) {
            return redirect()->route('profile.affiliate')->with('info', 'You already have an affiliate code: ' . $existingCode->code);
        }

        // Get the parent affiliate code if provided
        $parentUserId = null;
        if ($request->has('parent_code')) {
            $parentCode = AffiliateCode::where('code', $request->parent_code)
                ->where('is_active', true)
                ->first();

            if ($parentCode && $parentCode->user_id != Auth::id()) {
                $parentUserId = $parentCode->user_id;
            }
        }

        // Generate a new affiliate code
        $user = Auth::user();
        $affiliateCode = $user->generateAffiliateCode($parentUserId);

        return redirect()->route('profile.affiliate')->with('success', 'Affiliate code generated successfully: ' . $affiliateCode->code);
    }
}
