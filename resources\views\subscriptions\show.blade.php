@extends('layouts.app')

@section('title', 'Subscription Details - NATRYON')

@section('content')
    <!-- Hero Section -->
    <section class="py-4">
        <div class="container">
            <div class="d-flex flex-column flex-md-row justify-content-between align-items-md-center mb-4 gap-3">
                <div>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-2">
                            <li class="breadcrumb-item"><a href="{{ route('subscriptions.index') }}" class="text-decoration-none">My Subscriptions</a></li>
                            <li class="breadcrumb-item active" aria-current="page">Subscription Details</li>
                        </ol>
                    </nav>
                    <h1 class="h3 mb-0 text-primary fw-bold">Subscription Details</h1>
                </div>
                <div>
                    <a href="{{ route('subscriptions.index') }}" class="btn btn-outline-primary rounded-pill">
                        <i class="bi bi-arrow-left me-1"></i> Back to Subscriptions
                    </a>
                </div>
            </div>

            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    {{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            @endif

            @if(session('error'))
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    {{ session('error') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            @endif

            <!-- Subscription Status Card -->
            <div class="card border-0 shadow-sm rounded-3 mb-4 overflow-hidden">
                <div class="card-body p-0">
                    <div class="d-flex flex-wrap">
                        <div class="p-4 border-end" style="min-width: 200px;">
                            <div class="d-flex align-items-center mb-3">
                                <div class="rounded-circle bg-light p-2 me-3">
                                    <i class="bi bi-calendar3 text-primary"></i>
                                </div>
                                <div>
                                    <small class="text-muted d-block">Started On</small>
                                    <span class="fw-medium">{{ $subscription->created_at->format('M d, Y') }}</span>
                                </div>
                            </div>
                            @if($subscription->ends_at)
                                <div class="d-flex align-items-center">
                                    <div class="rounded-circle bg-light p-2 me-3">
                                        <i class="bi bi-calendar-x text-primary"></i>
                                    </div>
                                    <div>
                                        <small class="text-muted d-block">Ends On</small>
                                        <span class="fw-medium">{{ $subscription->ends_at->format('M d, Y') }}</span>
                                    </div>
                                </div>
                            @endif
                        </div>
                        <div class="p-4 border-end" style="min-width: 200px;">
                            <div class="d-flex align-items-center">
                                <div class="rounded-circle bg-light p-2 me-3">
                                    <i class="bi bi-tag text-primary"></i>
                                </div>
                                <div>
                                    <small class="text-muted d-block">Status</small>
                                    <button type="button" class="badge {{ $subscription->stripe_status === 'active' ? 'bg-success' : ($subscription->stripe_status === 'canceled' ? 'bg-danger' : 'bg-warning text-dark') }} rounded-pill border-0 status-badge"
                                           onclick="showStatusInfo('{{ $subscription->stripe_status }}')"
                                           title="Subscription status information">
                                        {{ ucfirst($subscription->stripe_status) }}
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="p-4 d-flex align-items-center">
                            <div class="rounded-circle bg-light p-2 me-3">
                                <i class="bi bi-credit-card text-primary"></i>
                            </div>
                            <div>
                                <small class="text-muted d-block">Subscription ID</small>
                                <span class="fw-medium">{{ $subscription->stripe_id }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row g-4">
                <!-- Subscription Details -->
                <div class="col-lg-8">
                    <div class="card border-0 shadow-sm rounded-3">
                        <div class="card-header bg-white py-3 border-bottom border-light">
                            <h5 class="mb-0 text-primary">
                                <i class="bi bi-info-circle me-2"></i>Subscription Details
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <h6 class="fw-bold text-uppercase small text-secondary mb-2">
                                        <i class="bi bi-box me-2"></i>Product Type
                                    </h6>
                                    <p class="bg-light p-3 rounded-3">{{ $subscription->type }}</p>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="fw-bold text-uppercase small text-secondary mb-2">
                                        <i class="bi bi-tag me-2"></i>Price ID
                                    </h6>
                                    <p class="bg-light p-3 rounded-3">{{ $subscription->stripe_price }}</p>
                                </div>
                            </div>

                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <h6 class="fw-bold text-uppercase small text-secondary mb-2">
                                        <i class="bi bi-calendar-check me-2"></i>Billing Cycle
                                    </h6>
                                    <p class="bg-light p-3 rounded-3">Monthly</p>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="fw-bold text-uppercase small text-secondary mb-2">
                                        <i class="bi bi-box-seam me-2"></i>Quantity
                                    </h6>
                                    <p class="bg-light p-3 rounded-3">{{ $subscription->quantity ?? 1 }}</p>
                                </div>
                            </div>

                            @if($subscription->stripe_status === 'active')
                                <div class="d-grid gap-2 mt-4">
                                    <button type="button" class="btn btn-danger"
                                           onclick="confirmCancelSubscription('{{ $subscription->id }}', '{{ $subscription->type }}')">
                                        <i class="bi bi-x-circle me-2"></i>Cancel Subscription
                                    </button>

                                    <form id="cancel-subscription-form-{{ $subscription->id }}"
                                          action="{{ route('subscriptions.cancel', $subscription->id) }}"
                                          method="POST"
                                          style="display: none;">
                                        @csrf
                                    </form>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Subscription Management -->
                <div class="col-lg-4">
                    <div class="card border-0 shadow-sm rounded-3">
                        <div class="card-header bg-white py-3 border-bottom border-light">
                            <h5 class="mb-0 text-primary">
                                <i class="bi bi-gear me-2"></i>Subscription Management
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <a href="#" class="btn btn-outline-primary">
                                    <i class="bi bi-clock-history me-2"></i>View Billing History
                                </a>

                                @if($subscription->stripe_status === 'active')
                                    <a href="#" class="btn btn-outline-primary">
                                        <i class="bi bi-pause-circle me-2"></i>Pause Subscription
                                    </a>

                                    <a href="#" class="btn btn-outline-primary">
                                        <i class="bi bi-credit-card me-2"></i>Update Payment Method
                                    </a>
                                @endif
                            </div>

                            <div class="alert alert-info mt-4">
                                <i class="bi bi-info-circle-fill me-2"></i>
                                <small>Need help with your subscription? Contact our customer support team at <a href="mailto:<EMAIL>"><EMAIL></a></small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection

@push('styles')
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css">
<style>
    .badge {
        font-weight: 500;
        padding: 0.5em 0.8em;
    }

    .btn-primary {
        background-color: #428677;
        border-color: #428677;
    }

    .btn-primary:hover, .btn-primary:focus {
        background-color: #2d504b;
        border-color: #2d504b;
    }

    .btn-outline-primary {
        color: #428677;
        border-color: #428677;
    }

    .btn-outline-primary:hover, .btn-outline-primary:focus {
        background-color: #428677;
        border-color: #428677;
    }

    .text-primary {
        color: #428677 !important;
    }

    .bg-primary {
        background-color: #428677 !important;
    }

    .breadcrumb-item a {
        color: #428677;
    }

    .breadcrumb-item.active {
        color: #7a868d;
    }

    .breadcrumb-item + .breadcrumb-item::before {
        color: #b1b7b8;
    }

    /* Toast Notification Styling */
    .toastify {
        padding: 16px 20px;
        border-radius: 8px;
        font-weight: 500;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    }

    .toast-success {
        background: linear-gradient(135deg, #428677, #2d504b);
    }

    .toast-error {
        background: linear-gradient(135deg, #e74c3c, #c0392b);
    }

    .toast-info {
        background: linear-gradient(135deg, #3498db, #2980b9);
    }

    .toast-warning {
        background: linear-gradient(135deg, #f39c12, #e67e22);
    }

    /* Status badge with hover effect */
    .status-badge {
        transition: all 0.3s ease;
    }

    .status-badge:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
</style>
@endpush

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/toastify-js"></script>
<script>
    // Show notification when page loads
    document.addEventListener('DOMContentLoaded', function() {
        showNotification('Subscription details loaded successfully', 'info');
    });

    // Function to show notifications
    function showNotification(message, type = 'info') {
        const toastClass = type === 'success' ? 'toast-success' :
                          type === 'error' ? 'toast-error' :
                          type === 'warning' ? 'toast-warning' : 'toast-info';

        Toastify({
            text: message,
            duration: 5000,
            close: true,
            gravity: "top",
            position: "right",
            className: toastClass,
            stopOnFocus: true
        }).showToast();
    }

    // Function to show status information
    function showStatusInfo(status) {
        let message = '';
        let type = 'info';

        switch(status) {
            case 'active':
                message = 'Your subscription is active and will renew automatically.';
                type = 'success';
                break;
            case 'canceled':
                message = 'Your subscription has been canceled and will not renew.';
                type = 'error';
                break;
            case 'past_due':
                message = 'Your subscription payment is past due. Please update your payment method.';
                type = 'warning';
                break;
            case 'unpaid':
                message = 'Your subscription has unpaid invoices. Please update your payment method.';
                type = 'warning';
                break;
            case 'trialing':
                message = 'Your subscription is in trial period.';
                type = 'info';
                break;
            default:
                message = 'Subscription status: ' + status;
        }

        showNotification(message, type);
    }

    // Function to confirm subscription cancellation
    function confirmCancelSubscription(subscriptionId, subscriptionType) {
        // Create a modal dialog for confirmation
        const modalHtml = `
            <div class="modal fade" id="cancelSubscriptionModal" tabindex="-1" aria-labelledby="cancelSubscriptionModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header bg-light">
                            <h5 class="modal-title" id="cancelSubscriptionModalLabel">Cancel Subscription</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="text-center mb-4">
                                <i class="bi bi-exclamation-triangle text-warning" style="font-size: 3rem;"></i>
                            </div>
                            <p>Are you sure you want to cancel your <strong>${subscriptionType}</strong> subscription?</p>
                            <p>This action cannot be undone. You will no longer receive regular shipments of this product.</p>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Keep Subscription</button>
                            <button type="button" class="btn btn-danger" id="confirmCancelBtn">
                                <i class="bi bi-x-circle me-1"></i>Yes, Cancel Subscription
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Add the modal to the document
        const modalContainer = document.createElement('div');
        modalContainer.innerHTML = modalHtml;
        document.body.appendChild(modalContainer);

        // Initialize the modal
        const modal = new bootstrap.Modal(document.getElementById('cancelSubscriptionModal'));
        modal.show();

        // Handle confirmation
        document.getElementById('confirmCancelBtn').addEventListener('click', function() {
            // Show processing notification
            showNotification('Processing your cancellation request...', 'info');

            // Hide the modal
            modal.hide();

            // Submit the cancellation form
            document.getElementById(`cancel-subscription-form-${subscriptionId}`).submit();
        });

        // Clean up the modal when it's hidden
        document.getElementById('cancelSubscriptionModal').addEventListener('hidden.bs.modal', function() {
            document.body.removeChild(modalContainer);
        });
    }
</script>
@endpush
