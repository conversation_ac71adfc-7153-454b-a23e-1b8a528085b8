<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <meta name="author" content="Mohamed EL KHOULI">
    <meta name="creator" content="Mohamed ELKHOULI">
    <meta name="publisher" content="NATRYON">
    <meta name="geo.placename" content="Casablanca, Morocco">

    <title><?php echo $__env->yieldContent('title', 'NATRYON - Premium Greens Powder Supplement'); ?></title>

    <!-- SEO -->
    <?php echo SEO::generate(); ?>


    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Styles -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        :root {
            --primary-color: #2d504b;
            --secondary-color: #428677;
            --accent-color: #8ab0a6;
            --light-color: #b1b7b8;
            --dark-color: #7a868d;
            --darker-color: #24445a;
        }

        body {
            font-family: 'Poppins', sans-serif;
            color: #333;
            background-color: #f8f9fa;
        }

        .navbar {
            background-color: var(--primary-color);
        }

        .navbar-brand, .navbar-nav .nav-link {
            color: white;
        }

        .navbar-nav .nav-link:hover {
            color: var(--light-color);
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-primary:hover {
            background-color: var(--darker-color);
            border-color: var(--darker-color);
        }

        .btn-secondary {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
        }

        .btn-secondary:hover {
            background-color: var(--dark-color);
            border-color: var(--dark-color);
        }

        .footer {
            background-color: var(--primary-color);
            color: white;
            padding: 2rem 0;
        }

        .footer a {
            color: var(--light-color);
        }

        .footer a:hover {
            color: white;
        }
    </style>
    <?php echo $__env->yieldPushContent('styles'); ?>
</head>
<body>
    <header>
        <nav class="navbar navbar-expand-lg navbar-dark">
            <div class="container">
                <a class="navbar-brand" href="<?php echo e(route('home')); ?>">
                    <img src="<?php echo e(asset('images/logos/logo natryon blanc copie.png')); ?>" alt="NATRYON" height="40">
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link <?php echo e(request()->routeIs('home') ? 'active' : ''); ?>" href="<?php echo e(route('home')); ?>"><?php echo e(__('Home')); ?></a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo e(request()->routeIs('products.index') ? 'active' : ''); ?>" href="<?php echo e(route('products.index')); ?>"><?php echo e(__('Products')); ?></a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link <?php echo e(request()->routeIs('about') ? 'active' : ''); ?>" href="<?php echo e(route('about')); ?>"><?php echo e(__('About')); ?></a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo e(request()->routeIs('contact') ? 'active' : ''); ?>" href="<?php echo e(route('contact')); ?>"><?php echo e(__('Contact')); ?></a>
                        </li>
                    </ul>
                    <ul class="navbar-nav">
                        <li class="nav-item me-2">
                            <?php echo $__env->make('components.language-selector', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                        </li>
                        <li class="nav-item me-2">
                            <?php echo $__env->make('components.currency-selector', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo e(route('cart.index')); ?>">
                                <i class="bi bi-cart"></i> <?php echo e(__('Cart')); ?>

                                <?php if(session()->has('cart') && count(session('cart')) > 0): ?>
                                    <span class="badge bg-danger"><?php echo e(count(session('cart'))); ?></span>
                                <?php endif; ?>
                            </a>
                        </li>
                        <?php if(auth()->guard()->guest()): ?>
                            <li class="nav-item">
                                <a class="nav-link" href="<?php echo e(route('login')); ?>"><?php echo e(__('Login')); ?></a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="<?php echo e(route('register')); ?>"><?php echo e(__('Register')); ?></a>
                            </li>
                        <?php else: ?>
                            <li class="nav-item dropdown">
                                <button class="nav-link dropdown-toggle btn btn-link" type="button" id="navbarDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                    <?php echo e(Auth::user()->name); ?>

                                </button>
                                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown">
                                    <?php if(Auth::user()->isAdmin()): ?>
                                        <li><a class="dropdown-item" href="<?php echo e(route('admin.dashboard')); ?>"><?php echo e(__('Admin Dashboard')); ?></a></li>
                                        <li><hr class="dropdown-divider"></li>
                                    <?php endif; ?>
                                    <li><a class="dropdown-item" href="<?php echo e(route('profile.edit')); ?>"><i class="bi bi-person me-2"></i><?php echo e(__('My Profile')); ?></a></li>
                                    <li><a class="dropdown-item" href="<?php echo e(route('orders.index')); ?>"><i class="bi bi-box me-2"></i><?php echo e(__('My Orders')); ?></a></li>
                                    <li><a class="dropdown-item" href="<?php echo e(route('affiliate.index')); ?>"><i class="bi bi-graph-up me-2"></i><?php echo e(__('Affiliate Program')); ?></a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <a class="dropdown-item" href="<?php echo e(route('logout')); ?>" onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
                                            <?php echo e(__('Logout')); ?>

                                        </a>
                                        <form id="logout-form" action="<?php echo e(route('logout')); ?>" method="POST" class="d-none">
                                            <?php echo csrf_field(); ?>
                                        </form>
                                    </li>
                                </ul>
                            </li>
                        <?php endif; ?>
                    </ul>
                </div>
            </div>
        </nav>
    </header>

    <main class="py-0">
        <?php if(session('success') || session('error') || session('info')): ?>
            <div class="container py-3">
                <?php if(session('success')): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?php echo e(session('success')); ?>

                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <?php if(session('error')): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php echo e(session('error')); ?>

                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <?php if(session('info')): ?>
                    <div class="alert alert-info alert-dismissible fade show" role="alert">
                        <?php echo e(session('info')); ?>

                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>
            </div>
        <?php endif; ?>

        <?php echo $__env->yieldContent('content'); ?>
    </main>

    <footer class="footer mt-auto">
        <div class="container">
            <div class="row">
                <div class="col-md-4 mb-4 mb-md-0">
                    <img src="<?php echo e(asset('images/logos/logo natryon blanc copie.png')); ?>" alt="NATRYON" height="40" class="mb-3">
                    <p><?php echo e(__('Premium Greens Powder Supplement for optimal health and nutrition.')); ?></p>
                </div>
                <div class="col-md-2 mb-4 mb-md-0">
                    <h5><?php echo e(__('Links')); ?></h5>
                    <ul class="list-unstyled">
                        <li><a href="<?php echo e(route('home')); ?>"><?php echo e(__('Home')); ?></a></li>
                        <li><a href="<?php echo e(route('products.index')); ?>"><?php echo e(__('Products')); ?></a></li>
                        <li><a href="<?php echo e(route('about')); ?>"><?php echo e(__('About')); ?></a></li>
                        <li><a href="<?php echo e(route('contact')); ?>"><?php echo e(__('Contact')); ?></a></li>
                    </ul>
                </div>
                <div class="col-md-3 mb-4 mb-md-0">
                    <h5><?php echo e(__('Legal')); ?></h5>
                    <ul class="list-unstyled">
                        <li><a href="#"><?php echo e(__('Terms of Service')); ?></a></li>
                        <li><a href="#"><?php echo e(__('Privacy Policy')); ?></a></li>
                        <li><a href="#"><?php echo e(__('Shipping Policy')); ?></a></li>
                        <li><a href="#"><?php echo e(__('Refund Policy')); ?></a></li>
                    </ul>
                </div>
                <div class="col-md-3">
                    <h5><?php echo e(__('Contact')); ?></h5>
                    <ul class="list-unstyled">
                        <li><i class="bi bi-envelope"></i> <a href="mailto:<EMAIL>"><EMAIL></a></li>
                        <li><i class="bi bi-telephone"></i> <a href="tel:+212600000000">+212 600-000-000</a></li>
                        <li><i class="bi bi-geo-alt"></i> Casablanca, Morocco</li>
                        <li class="mt-2"><i class="bi bi-person"></i> <?php echo e(__('Creator')); ?>: Mohamed EL KHOULI</li>
                    </ul>
                </div>
            </div>
            <hr class="my-4 bg-light">
            <div class="row">
                <div class="col-md-6 text-center text-md-start">
                    <p>&copy; <?php echo e(date('Y')); ?> NATRYON. <?php echo e(__('All rights reserved.')); ?></p>
                    <p class="small text-muted" style="display: none;"><?php echo e(__('Developed by')); ?> <a href="#" class="text-decoration-none" style="color: var(--secondary-color);">Mohamed EL KHOULI</a> <?php echo e(__('from Casablanca, Morocco')); ?></p>
                </div>
                <div class="col-md-6 text-center text-md-end">
                    <ul class="list-inline mb-0">
                        <li class="list-inline-item"><a href="#"><i class="bi bi-facebook"></i></a></li>
                        <li class="list-inline-item"><a href="#"><i class="bi bi-instagram"></i></a></li>
                        <li class="list-inline-item"><a href="#"><i class="bi bi-twitter"></i></a></li>
                        <li class="list-inline-item"><a href="#"><i class="bi bi-youtube"></i></a></li>
                        <li class="list-inline-item"><a href="#"><i class="bi bi-github"></i></a></li>
                        <li class="list-inline-item"><a href="#"><i class="bi bi-linkedin"></i></a></li>
                    </ul>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <?php echo $__env->yieldPushContent('scripts'); ?>

    <!-- Structured Data for Creator -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebSite",
        "name": "NATRYON",
        "url": "/",
        "description": "Premium Greens Powder Supplement for optimal health and nutrition",
        "potentialAction": {
            "@type": "SearchAction",
            "target": "/products?search={search_term}",
            "query-input": "required name=search_term"
        },
        "author": {
            "@type": "Person",
            "name": "Mohamed EL KHOULI",
            "jobTitle": "Full Stack Developer",
            "address": {
                "@type": "PostalAddress",
                "addressLocality": "Casablanca",
                "addressCountry": "Morocco"
            }
        }
    }
    </script>
</body>
</html>
<?php /**PATH D:\projects\natryon\resources\views/layouts/app.blade.php ENDPATH**/ ?>