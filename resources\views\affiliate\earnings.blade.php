@extends('layouts.app')

@section('title', 'Affiliate Earnings - NatRyon')

@section('content')
    <!-- Hero Section -->
    <section class="container py-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">Affiliate Earnings</h1>
            <a href="{{ route('affiliate.index') }}" class="btn btn-outline-primary">
                <i class="bi bi-arrow-left me-1"></i> Back to Dashboard
            </a>
        </div>
    </section>

    <!-- Earnings Summary Section -->
    <section class="container py-4">
        <div class="row g-4 mb-4">
            <div class="col-md-4">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body text-center">
                        <h6 class="text-muted mb-2">Total Earnings</h6>
                        <h2 class="mb-0">${{ number_format($totalEarnings, 2) }}</h2>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body text-center">
                        <h6 class="text-muted mb-2">Pending Earnings</h6>
                        <h2 class="mb-0">${{ number_format($pendingEarnings, 2) }}</h2>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body text-center">
                        <h6 class="text-muted mb-2">Earnings This Month</h6>
                        <h2 class="mb-0">${{ number_format($earnings->where('created_at', '>=', now()->startOfMonth())->sum('commission_amount'), 2) }}</h2>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white py-3">
                <h5 class="mb-0">Earnings History</h5>
            </div>
            <div class="card-body">
                @if(count($earnings) > 0)
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Order #</th>
                                    <th>Date</th>
                                    <th>Order Amount</th>
                                    <th>Commission Rate</th>
                                    <th>Commission</th>
                                    <th>Level</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($earnings as $earning)
                                    <tr>
                                        <td>{{ $earning->order->order_number }}</td>
                                        <td>{{ $earning->created_at->format('M d, Y') }}</td>
                                        <td>${{ number_format($earning->order_amount, 2) }}</td>
                                        <td>{{ $earning->commission_rate }}%</td>
                                        <td>${{ number_format($earning->commission_amount, 2) }}</td>
                                        <td>
                                            @if($earning->level == 1)
                                                <span class="badge bg-primary">Direct</span>
                                            @else
                                                <span class="badge bg-secondary">Level {{ $earning->level }}</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($earning->status == 'pending')
                                                <span class="badge bg-warning">Pending</span>
                                            @elseif($earning->status == 'approved')
                                                <span class="badge bg-success">Approved</span>
                                            @elseif($earning->status == 'paid')
                                                <span class="badge bg-primary">Paid</span>
                                                @if($earning->paid_at)
                                                    <small class="d-block text-muted">{{ $earning->paid_at->format('M d, Y') }}</small>
                                                @endif
                                            @elseif($earning->status == 'rejected')
                                                <span class="badge bg-danger">Rejected</span>
                                            @endif
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    <div class="mt-4">
                        {{ $earnings->links() }}
                    </div>
                @else
                    <div class="text-center py-4">
                        <i class="bi bi-cash" style="font-size: 3rem;"></i>
                        <h4 class="mt-3">No earnings yet</h4>
                        <p class="text-muted">Share your affiliate code to start earning commissions.</p>
                        <a href="{{ route('affiliate.index') }}" class="btn btn-primary mt-3">Go to Affiliate Dashboard</a>
                    </div>
                @endif
            </div>
        </div>
    </section>
    
    <!-- Payout Information Section -->
    <section class="container py-4">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white py-3">
                <h5 class="mb-0">Payout Information</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="fw-bold mb-3">Payout Schedule</h6>
                        <p>Affiliate commissions are paid out on the 15th of each month for the previous month's earnings, once they reach the minimum payout threshold of $50.</p>
                    </div>
                    <div class="col-md-6">
                        <h6 class="fw-bold mb-3">Commission Structure</h6>
                        <ul>
                            <li>Direct referrals: 10% commission on all purchases</li>
                            <li>Second-level referrals: 1% commission (10% of your referral's 10%)</li>
                            <li>Maximum referral depth: 2 levels</li>
                            <li>Maximum referral count: 10 clients per affiliate</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection
