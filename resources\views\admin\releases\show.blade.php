@extends('layouts.admin')

@section('title', 'Release Details')

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Release: {{ $release->version }}</h1>
        <div>
            <a href="{{ route('admin.releases.index') }}" class="btn btn-secondary">
                <i class="bi bi-arrow-left"></i> Back to Releases
            </a>
            @if($release->status === 'draft')
                <a href="{{ route('admin.releases.edit', $release->id) }}" class="btn btn-primary">
                    <i class="bi bi-pencil"></i> Edit
                </a>
            @endif
        </div>
    </div>

    <div class="row">
        <div class="col-md-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold">Release Information</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>Version:</strong>
                        <p>{{ $release->version }}</p>
                    </div>
                    <div class="mb-3">
                        <strong>Name:</strong>
                        <p>{{ $release->name }}</p>
                    </div>
                    <div class="mb-3">
                        <strong>Status:</strong>
                        <p>
                            @switch($release->status)
                                @case('draft')
                                    <span class="badge bg-secondary">Draft</span>
                                    @break
                                @case('testing')
                                    <span class="badge bg-info">Testing</span>
                                    @break
                                @case('approved')
                                    <span class="badge bg-success">Approved</span>
                                    @break
                                @case('deployed')
                                    <span class="badge bg-primary">Deployed</span>
                                    @break
                                @case('failed')
                                    <span class="badge bg-danger">Failed</span>
                                    @break
                                @default
                                    <span class="badge bg-secondary">{{ $release->status }}</span>
                            @endswitch
                        </p>
                    </div>
                    <div class="mb-3">
                        <strong>Description:</strong>
                        <p>{{ $release->description ?? 'No description provided.' }}</p>
                    </div>
                    <div class="mb-3">
                        <strong>Created By:</strong>
                        <p>{{ $release->creator->name }}</p>
                    </div>
                    <div class="mb-3">
                        <strong>Created At:</strong>
                        <p>{{ $release->created_at->format('Y-m-d H:i') }}</p>
                    </div>
                    @if($release->approved_by)
                        <div class="mb-3">
                            <strong>Approved By:</strong>
                            <p>{{ $release->approver->name }}</p>
                        </div>
                    @endif
                    @if($release->deployed_at)
                        <div class="mb-3">
                            <strong>Deployed At:</strong>
                            <p>{{ $release->deployed_at->format('Y-m-d H:i') }}</p>
                        </div>
                    @endif
                </div>
            </div>

            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold">Actions</h6>
                </div>
                <div class="card-body">
                    @if($release->status === 'draft')
                        <form action="{{ route('admin.releases.submit-for-testing', $release->id) }}" method="POST" class="mb-2">
                            @csrf
                            <button type="submit" class="btn btn-info btn-block" onclick="return confirm('Are you sure you want to submit this release for testing?')">
                                <i class="bi bi-check2-circle"></i> Submit for Testing
                            </button>
                        </form>
                    @endif

                    @if($release->status === 'testing' && $release->allCheckpointsPassed())
                        <form action="{{ route('admin.releases.approve', $release->id) }}" method="POST" class="mb-2">
                            @csrf
                            <button type="submit" class="btn btn-success btn-block" onclick="return confirm('Are you sure you want to approve this release?')">
                                <i class="bi bi-check2-all"></i> Approve Release
                            </button>
                        </form>
                    @endif

                    @if($release->status === 'approved')
                        <form action="{{ route('admin.releases.deploy', $release->id) }}" method="POST" class="mb-2">
                            @csrf
                            <button type="submit" class="btn btn-primary btn-block" onclick="return confirm('Are you sure you want to deploy this release?')">
                                <i class="bi bi-rocket"></i> Deploy Release
                            </button>
                        </form>
                    @endif

                    @if($release->status === 'draft')
                        <form action="{{ route('admin.releases.destroy', $release->id) }}" method="POST" class="mb-2">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-danger btn-block" onclick="return confirm('Are you sure you want to delete this release?')">
                                <i class="bi bi-trash"></i> Delete Release
                            </button>
                        </form>
                    @endif
                </div>
            </div>
        </div>

        <div class="col-md-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold">Checkpoints</h6>
                    @if($release->status === 'draft')
                        <a href="{{ route('admin.checkpoints.create', ['release_id' => $release->id]) }}" class="btn btn-sm btn-primary">
                            <i class="bi bi-plus-circle"></i> Add Checkpoint
                        </a>
                    @endif
                </div>
                <div class="card-body">
                    @if($release->checkpoints->isEmpty())
                        <div class="alert alert-info">
                            No checkpoints defined for this release.
                        </div>
                    @else
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover" width="100%" cellspacing="0">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Type</th>
                                        <th>Status</th>
                                        <th>Verified By</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($release->checkpoints as $checkpoint)
                                        <tr>
                                            <td>{{ $checkpoint->name }}</td>
                                            <td>{{ ucwords(str_replace('_', ' ', $checkpoint->type)) }}</td>
                                            <td>
                                                @switch($checkpoint->status)
                                                    @case('pending')
                                                        <span class="badge bg-secondary">Pending</span>
                                                        @break
                                                    @case('in_progress')
                                                        <span class="badge bg-info">In Progress</span>
                                                        @break
                                                    @case('passed')
                                                        <span class="badge bg-success">Passed</span>
                                                        @break
                                                    @case('failed')
                                                        <span class="badge bg-danger">Failed</span>
                                                        @break
                                                    @default
                                                        <span class="badge bg-secondary">{{ $checkpoint->status }}</span>
                                                @endswitch
                                            </td>
                                            <td>{{ $checkpoint->verifier->name ?? 'Not verified' }}</td>
                                            <td>
                                                <a href="{{ route('admin.checkpoints.show', $checkpoint->id) }}" class="btn btn-sm btn-info">
                                                    <i class="bi bi-eye"></i> View
                                                </a>
                                                @if($release->status === 'draft' && $checkpoint->status === 'pending')
                                                    <a href="{{ route('admin.checkpoints.edit', $checkpoint->id) }}" class="btn btn-sm btn-primary">
                                                        <i class="bi bi-pencil"></i> Edit
                                                    </a>
                                                    <form action="{{ route('admin.checkpoints.destroy', $checkpoint->id) }}" method="POST" class="d-inline">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to delete this checkpoint?')">
                                                            <i class="bi bi-trash"></i>
                                                        </button>
                                                    </form>
                                                @endif
                                                @if($release->status === 'testing' && $checkpoint->status === 'pending')
                                                    <form action="{{ route('admin.checkpoints.start-verification', $checkpoint->id) }}" method="POST" class="d-inline">
                                                        @csrf
                                                        <button type="submit" class="btn btn-sm btn-warning">
                                                            <i class="bi bi-play"></i> Start
                                                        </button>
                                                    </form>
                                                @endif
                                                @if($release->status === 'testing' && ($checkpoint->status === 'in_progress' || $checkpoint->status === 'pending'))
                                                    <button type="button" class="btn btn-sm btn-success" data-bs-toggle="modal" data-bs-target="#passCheckpointModal{{ $checkpoint->id }}">
                                                        <i class="bi bi-check"></i> Pass
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#failCheckpointModal{{ $checkpoint->id }}">
                                                        <i class="bi bi-x"></i> Fail
                                                    </button>
                                                @endif
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @endif
                </div>
            </div>

            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold">Partitions</h6>
                    @if($release->status === 'draft')
                        <a href="{{ route('admin.partitions.create', ['release_id' => $release->id]) }}" class="btn btn-sm btn-primary">
                            <i class="bi bi-plus-circle"></i> Add Partition
                        </a>
                    @endif
                </div>
                <div class="card-body">
                    @if($release->partitions->isEmpty())
                        <div class="alert alert-info">
                            No partitions defined for this release.
                        </div>
                    @else
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover" width="100%" cellspacing="0">
                                <thead>
                                    <tr>
                                        <th>Order</th>
                                        <th>Name</th>
                                        <th>Type</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($release->partitions->sortBy('deployment_order') as $partition)
                                        <tr>
                                            <td>{{ $partition->deployment_order }}</td>
                                            <td>{{ $partition->name }}</td>
                                            <td>{{ ucfirst($partition->type) }}</td>
                                            <td>
                                                @switch($partition->status)
                                                    @case('pending')
                                                        <span class="badge bg-secondary">Pending</span>
                                                        @break
                                                    @case('deployed')
                                                        <span class="badge bg-success">Deployed</span>
                                                        @break
                                                    @case('failed')
                                                        <span class="badge bg-danger">Failed</span>
                                                        @break
                                                    @case('rolled_back')
                                                        <span class="badge bg-warning">Rolled Back</span>
                                                        @break
                                                    @default
                                                        <span class="badge bg-secondary">{{ $partition->status }}</span>
                                                @endswitch
                                            </td>
                                            <td>
                                                <a href="{{ route('admin.partitions.show', $partition->id) }}" class="btn btn-sm btn-info">
                                                    <i class="bi bi-eye"></i> View
                                                </a>
                                                @if($release->status === 'draft')
                                                    <a href="{{ route('admin.partitions.edit', $partition->id) }}" class="btn btn-sm btn-primary">
                                                        <i class="bi bi-pencil"></i> Edit
                                                    </a>
                                                    <form action="{{ route('admin.partitions.destroy', $partition->id) }}" method="POST" class="d-inline">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to delete this partition?')">
                                                            <i class="bi bi-trash"></i>
                                                        </button>
                                                    </form>
                                                @endif
                                                @if($release->status === 'approved' && $partition->status === 'pending')
                                                    <button type="button" class="btn btn-sm btn-success" data-bs-toggle="modal" data-bs-target="#deployPartitionModal{{ $partition->id }}">
                                                        <i class="bi bi-rocket"></i> Deploy
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#failPartitionModal{{ $partition->id }}">
                                                        <i class="bi bi-x"></i> Fail
                                                    </button>
                                                @endif
                                                @if($partition->status === 'deployed')
                                                    <button type="button" class="btn btn-sm btn-warning" data-bs-toggle="modal" data-bs-target="#rollbackPartitionModal{{ $partition->id }}">
                                                        <i class="bi bi-arrow-counterclockwise"></i> Rollback
                                                    </button>
                                                @endif
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Checkpoint Modals -->
@foreach($release->checkpoints as $checkpoint)
    <!-- Pass Checkpoint Modal -->
    <div class="modal fade" id="passCheckpointModal{{ $checkpoint->id }}" tabindex="-1" aria-labelledby="passCheckpointModalLabel{{ $checkpoint->id }}" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <form action="{{ route('admin.checkpoints.pass', $checkpoint->id) }}" method="POST">
                    @csrf
                    <div class="modal-header">
                        <h5 class="modal-title" id="passCheckpointModalLabel{{ $checkpoint->id }}">Pass Checkpoint: {{ $checkpoint->name }}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="verification_notes" class="form-label">Verification Notes</label>
                            <textarea class="form-control" id="verification_notes" name="verification_notes" rows="3" placeholder="Enter any notes about the verification process"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-success">Mark as Passed</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Fail Checkpoint Modal -->
    <div class="modal fade" id="failCheckpointModal{{ $checkpoint->id }}" tabindex="-1" aria-labelledby="failCheckpointModalLabel{{ $checkpoint->id }}" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <form action="{{ route('admin.checkpoints.fail', $checkpoint->id) }}" method="POST">
                    @csrf
                    <div class="modal-header">
                        <h5 class="modal-title" id="failCheckpointModalLabel{{ $checkpoint->id }}">Fail Checkpoint: {{ $checkpoint->name }}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="verification_notes" class="form-label">Failure Reason</label>
                            <textarea class="form-control" id="verification_notes" name="verification_notes" rows="3" placeholder="Enter the reason for failure" required></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-danger">Mark as Failed</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endforeach

<!-- Partition Modals -->
@foreach($release->partitions as $partition)
    <!-- Deploy Partition Modal -->
    <div class="modal fade" id="deployPartitionModal{{ $partition->id }}" tabindex="-1" aria-labelledby="deployPartitionModalLabel{{ $partition->id }}" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <form action="{{ route('admin.partitions.deploy', $partition->id) }}" method="POST">
                    @csrf
                    <div class="modal-header">
                        <h5 class="modal-title" id="deployPartitionModalLabel{{ $partition->id }}">Deploy Partition: {{ $partition->name }}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="deployment_notes" class="form-label">Deployment Notes</label>
                            <textarea class="form-control" id="deployment_notes" name="deployment_notes" rows="3" placeholder="Enter any notes about the deployment process"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-success">Deploy</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Fail Partition Modal -->
    <div class="modal fade" id="failPartitionModal{{ $partition->id }}" tabindex="-1" aria-labelledby="failPartitionModalLabel{{ $partition->id }}" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <form action="{{ route('admin.partitions.fail', $partition->id) }}" method="POST">
                    @csrf
                    <div class="modal-header">
                        <h5 class="modal-title" id="failPartitionModalLabel{{ $partition->id }}">Fail Partition: {{ $partition->name }}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="deployment_notes" class="form-label">Failure Reason</label>
                            <textarea class="form-control" id="deployment_notes" name="deployment_notes" rows="3" placeholder="Enter the reason for failure" required></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-danger">Mark as Failed</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Rollback Partition Modal -->
    <div class="modal fade" id="rollbackPartitionModal{{ $partition->id }}" tabindex="-1" aria-labelledby="rollbackPartitionModalLabel{{ $partition->id }}" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <form action="{{ route('admin.partitions.rollback', $partition->id) }}" method="POST">
                    @csrf
                    <div class="modal-header">
                        <h5 class="modal-title" id="rollbackPartitionModalLabel{{ $partition->id }}">Rollback Partition: {{ $partition->name }}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="deployment_notes" class="form-label">Rollback Reason</label>
                            <textarea class="form-control" id="deployment_notes" name="deployment_notes" rows="3" placeholder="Enter the reason for rollback" required></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-warning">Rollback</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endforeach
@endsection
