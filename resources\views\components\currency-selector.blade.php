<div class="currency-selector">
    <div class="dropdown">
        <button class="btn btn-outline-secondary dropdown-toggle currency-btn" type="button" id="currencyDropdown" data-bs-toggle="dropdown" aria-expanded="false">
            <i class="bi bi-currency-exchange me-2"></i>
            <span class="current-currency">{{ $currentCurrency ?? 'EUR' }}</span>
            <span class="current-symbol">({{ $currentCurrencySymbol ?? '€' }})</span>
        </button>
        <ul class="dropdown-menu currency-menu" aria-labelledby="currencyDropdown">
            @foreach($activeCurrencies ?? [] as $currency)
                <li>
                    <a class="dropdown-item currency-option {{ ($currentCurrency ?? 'EUR') === $currency->code ? 'active' : '' }}"
                       href="#"
                       data-currency="{{ $currency->code }}"
                       data-symbol="{{ $currency->symbol }}">
                        <div class="currency-item">
                            <div class="currency-main">
                                <span class="currency-code">{{ $currency->code }}</span>
                                <span class="currency-symbol">{{ $currency->symbol }}</span>
                            </div>
                            <div class="currency-name">{{ $currency->name }}</div>
                        </div>
                        @if($currency->is_default)
                            <span class="badge bg-primary ms-2">Default</span>
                        @endif
                    </a>
                </li>
            @endforeach
        </ul>
    </div>
</div>

<style>
.currency-selector {
    position: relative;
}

.currency-btn {
    min-width: 120px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.3);
    color: white;
    font-weight: 500;
    font-size: 0.9rem;
}

.currency-btn:hover, .currency-btn:focus {
    background-color: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
    color: white;
}

.current-currency {
    font-weight: 600;
}

.current-symbol {
    font-size: 0.9rem;
    opacity: 0.8;
}

.currency-menu {
    min-width: 200px;
    border: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-radius: 0.5rem;
    padding: 0.5rem 0;
}

.currency-option {
    padding: 0.75rem 1rem;
    transition: all 0.2s ease;
    border: none;
}

.currency-option:hover {
    background-color: #F6E8D2;
    color: #024C3D;
}

.currency-option.active {
    background-color: #024C3D;
    color: white;
}

.currency-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.currency-main {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.currency-code {
    font-weight: 600;
    font-size: 0.95rem;
}

.currency-symbol {
    font-weight: 500;
    color: #6c757d;
    font-size: 0.9rem;
}

.currency-name {
    font-size: 0.8rem;
    color: #6c757d;
}

.currency-option.active .currency-symbol,
.currency-option.active .currency-name {
    color: rgba(255, 255, 255, 0.8);
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const currencyOptions = document.querySelectorAll('.currency-option');

    currencyOptions.forEach(option => {
        option.addEventListener('click', function(e) {
            e.preventDefault();

            const currency = this.dataset.currency;
            const symbol = this.dataset.symbol;

            // Update UI immediately
            document.querySelector('.current-currency').textContent = currency;
            document.querySelector('.current-symbol').textContent = `(${symbol})`;

            // Remove active class from all options
            currencyOptions.forEach(opt => opt.classList.remove('active'));
            // Add active class to clicked option
            this.classList.add('active');

            // Send AJAX request to change currency
            fetch('/currency/change', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({ currency: currency })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Reload page to update all prices
                    window.location.reload();
                } else {
                    console.error('Failed to change currency:', data.message);
                }
            })
            .catch(error => {
                console.error('Error changing currency:', error);
            });
        });
    });
});
</script>
