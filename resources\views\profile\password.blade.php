@extends('layouts.app')

@section('title', 'Change Password - NATRYON')

@section('content')
    <!-- Hero Section -->
    <section class="py-4 bg-light">
        <div class="container">
            <div class="text-center mb-5">
                <h1 class="display-5 fw-bold" style="color: #428677;">My Profile</h1>
                <p class="lead">Change your password</p>
            </div>
        </div>
    </section>

    <!-- Password Section -->
    <section class="py-5">
        <div class="container">
            <div class="row">
                <!-- Sidebar -->
                <div class="col-lg-3 mb-4">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-white py-3">
                            <h5 class="mb-0" style="color: #428677;">Account Menu</h5>
                        </div>
                        <div class="list-group list-group-flush">
                            <a href="{{ route('profile.edit') }}" class="list-group-item list-group-item-action">
                                <i class="bi bi-person me-2"></i> Profile Information
                            </a>
                            <a href="{{ route('profile.password') }}" class="list-group-item list-group-item-action active">
                                <i class="bi bi-shield-lock me-2"></i> Change Password
                            </a>
                            <a href="{{ route('orders.index') }}" class="list-group-item list-group-item-action">
                                <i class="bi bi-box me-2"></i> My Orders
                            </a>
                            <a href="{{ route('affiliate.index') }}" class="list-group-item list-group-item-action">
                                <i class="bi bi-graph-up me-2"></i> Affiliate Dashboard
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Password Form -->
                <div class="col-lg-9">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-white py-3">
                            <h5 class="mb-0" style="color: #428677;">Change Password</h5>
                        </div>
                        <div class="card-body">
                            @if(session('success'))
                                <div class="alert alert-success alert-dismissible fade show" role="alert">
                                    {{ session('success') }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                </div>
                            @endif

                            <form action="{{ route('profile.password.update') }}" method="POST">
                                @csrf
                                @method('PUT')

                                <div class="mb-3">
                                    <label for="current_password" class="form-label">Current Password</label>
                                    <input type="password" class="form-control @error('current_password') is-invalid @enderror" id="current_password" name="current_password" required>
                                    @error('current_password')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="password" class="form-label">New Password</label>
                                    <input type="password" class="form-control @error('password') is-invalid @enderror" id="password" name="password" required>
                                    @error('password')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">Password must be at least 8 characters long.</div>
                                </div>

                                <div class="mb-3">
                                    <label for="password_confirmation" class="form-label">Confirm New Password</label>
                                    <input type="password" class="form-control" id="password_confirmation" name="password_confirmation" required>
                                </div>

                                <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                                    <button type="submit" class="btn btn-primary" style="background-color: #428677; border-color: #428677;">
                                        <i class="bi bi-check-circle me-2"></i>Update Password
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection
