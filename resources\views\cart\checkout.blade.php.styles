@push('styles')
<style>
    .payment-method-option {
        position: relative;
        flex: 1;
        min-width: 150px;
    }
    
    .payment-method-label {
        display: block;
        padding: 15px;
        border: 1px solid #ddd;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.2s ease;
    }
    
    .payment-method-option.active .payment-method-label {
        border-color: #428677;
        background-color: rgba(66, 134, 119, 0.05);
    }
    
    .payment-method-option input:checked + .payment-method-label {
        border-color: #428677;
        background-color: rgba(66, 134, 119, 0.05);
    }
    
    /* Disabled payment method styling */
    .payment-method-option.disabled {
        opacity: 0.7;
    }
    
    .payment-method-option.disabled .payment-method-label {
        cursor: not-allowed;
        background-color: #f8f9fa;
        border-color: #e9ecef;
    }
    
    .disabled-label {
        cursor: not-allowed !important;
    }
    
    .payment-processing-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(255, 255, 255, 0.9);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.3s ease, visibility 0.3s ease;
    }
    
    .payment-processing-overlay.active {
        opacity: 1;
        visibility: visible;
    }
    
    .payment-processing-spinner {
        width: 60px;
        height: 60px;
        border: 5px solid rgba(66, 134, 119, 0.2);
        border-radius: 50%;
        border-top-color: #428677;
        animation: spin 1s linear infinite;
        margin-bottom: 20px;
    }
    
    @keyframes spin {
        to {
            transform: rotate(360deg);
        }
    }
    
    .notification-toast {
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        border-radius: 8px;
        background-color: #fff;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        z-index: 9999;
        display: flex;
        align-items: center;
        max-width: 350px;
        transform: translateY(-20px);
        opacity: 0;
        transition: transform 0.3s ease, opacity 0.3s ease;
    }
    
    .notification-toast.active {
        transform: translateY(0);
        opacity: 1;
    }
    
    .notification-toast.success {
        border-left: 4px solid #28a745;
    }
    
    .notification-toast.error {
        border-left: 4px solid #dc3545;
    }
    
    .notification-toast.info {
        border-left: 4px solid #17a2b8;
    }
    
    .notification-toast.warning {
        border-left: 4px solid #ffc107;
    }
    
    .notification-toast .notification-icon {
        margin-right: 15px;
        font-size: 20px;
    }
    
    .notification-toast.success .notification-icon {
        color: #28a745;
    }
    
    .notification-toast.error .notification-icon {
        color: #dc3545;
    }
    
    .notification-toast.info .notification-icon {
        color: #17a2b8;
    }
    
    .notification-toast.warning .notification-icon {
        color: #ffc107;
    }
</style>
@endpush
