<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Product;
use App\Models\Category;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Auth;

class AdminProductController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Product::query();

        // Search
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Category filter
        if ($request->has('category')) {
            $query->where('category_id', $request->category);
        }

        // Sort
        if ($request->has('sort')) {
            switch ($request->sort) {
                case 'name_asc':
                    $query->orderBy('name', 'asc');
                    break;
                case 'name_desc':
                    $query->orderBy('name', 'desc');
                    break;
                case 'price_asc':
                    $query->orderBy('price', 'asc');
                    break;
                case 'price_desc':
                    $query->orderBy('price', 'desc');
                    break;
                case 'newest':
                    $query->orderBy('created_at', 'desc');
                    break;
                case 'oldest':
                    $query->orderBy('created_at', 'asc');
                    break;
                default:
                    $query->orderBy('created_at', 'desc');
            }
        } else {
            $query->orderBy('created_at', 'desc');
        }

        $products = $query->paginate(10);
        $categories = Category::all();

        return view('admin.products.index', compact('products', 'categories'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $categories = Category::all();
        return view('admin.products.create', compact('categories'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'slug' => 'required|string|max:255|unique:products',
            'description' => 'required|string',
            'price' => 'required|numeric|min:0',
            'sale_price' => 'nullable|numeric|min:0',
            'stock' => 'nullable|integer|min:0',
            'category_id' => 'nullable|exists:categories,id',
            'image' => 'nullable|image|max:2048',
            'gallery.*' => 'nullable|image|max:2048',
            'ingredients' => 'nullable|string',
            'benefits' => 'nullable|string',
            'how_to_use' => 'nullable|string',
            'is_active' => 'nullable|boolean',
            'is_featured' => 'nullable|boolean',
            'allow_subscription' => 'nullable|boolean',
            'subscription_price' => 'nullable|numeric|min:0',
            'sku' => 'nullable|string|max:50|unique:products',
        ]);

        $product = new Product();
        $product->name = $request->name;
        $product->slug = $request->slug;
        $product->description = $request->description;
        $product->price = $request->price;
        $product->sale_price = $request->sale_price;
        $product->stock = $request->stock ?? 0;
        $product->category_id = $request->category_id;
        $product->ingredients = $request->ingredients;
        $product->benefits = $request->benefits;
        $product->how_to_use = $request->how_to_use;
        $product->is_active = $request->has('is_active');
        $product->is_featured = $request->has('is_featured');
        $product->allow_subscription = $request->has('allow_subscription');
        $product->subscription_only = $request->has('subscription_only');
        $product->subscription_price = $request->subscription_price;
        $product->user_id = Auth::id();

        // Generate SKU if not provided
        $product->sku = $request->sku ?? 'SKU-' . strtoupper(Str::random(8));

        // Handle image upload
        if ($request->hasFile('image')) {
            $imagePath = $request->file('image')->store('products', 'public');
            $product->image = $imagePath;
        }

        // Handle gallery uploads
        if ($request->hasFile('gallery')) {
            $galleryPaths = [];
            foreach ($request->file('gallery') as $image) {
                $galleryPaths[] = $image->store('products/gallery', 'public');
            }
            $product->gallery = $galleryPaths;
        }

        $product->save();

        return redirect()->route('admin.products.index')->with('success', 'Product created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $product = Product::with('category')->findOrFail($id);

        // Calculate total sales (quantity) from regular orders
        $regularOrderItems = \App\Models\OrderItem::where('product_id', $product->id)
            ->whereHas('order', function($query) {
                $query->where('is_paid', true);
            })
            ->get();

        $regularSalesCount = $regularOrderItems->sum('quantity');
        $regularRevenue = $regularOrderItems->sum(function($item) {
            return $item->price * $item->quantity;
        });

        // Calculate total sales from subscriptions
        $subscriptionItems = \App\Models\SubscriptionItem::where('stripe_product', $product->id)
            ->whereHas('subscription', function($query) {
                $query->where('stripe_status', 'active')
                    ->orWhere('stripe_status', 'trialing');
            })
            ->get();

        $subscriptionSalesCount = $subscriptionItems->sum('quantity');
        $subscriptionRevenue = 0;

        // Calculate subscription revenue
        foreach ($subscriptionItems as $item) {
            // Use subscription price if available, otherwise use regular price
            $price = $product->subscription_price ?? $product->price;
            $subscriptionRevenue += $price * $item->quantity;
        }

        // Total sales and revenue
        $totalSales = $regularSalesCount + $subscriptionSalesCount;
        $totalRevenue = $regularRevenue + $subscriptionRevenue;

        // Get recent orders for this product
        $recentOrders = \App\Models\Order::whereHas('items', function($query) use ($product) {
                $query->where('product_id', $product->id);
            })
            ->with(['user', 'items' => function($query) use ($product) {
                $query->where('product_id', $product->id);
            }])
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();

        // Get monthly sales data for chart
        $monthlySales = [];
        $monthlyLabels = [];

        // Get data for the last 6 months
        for ($i = 5; $i >= 0; $i--) {
            $month = now()->subMonths($i);
            $monthlyLabels[] = $month->format('M');

            // Regular orders for this month
            $regularSales = \App\Models\OrderItem::where('product_id', $product->id)
                ->whereHas('order', function($query) use ($month) {
                    $query->where('is_paid', true)
                        ->whereMonth('created_at', $month->month)
                        ->whereYear('created_at', $month->year);
                })
                ->sum('quantity');

            // Subscription orders for this month (estimate based on active subscriptions)
            $subscriptionSales = \App\Models\SubscriptionItem::where('stripe_product', $product->id)
                ->whereHas('subscription', function($query) use ($month) {
                    $query->where(function($q) {
                        $q->where('stripe_status', 'active')
                          ->orWhere('stripe_status', 'trialing');
                    })
                    ->where(function($q) use ($month) {
                        $q->whereMonth('created_at', '<=', $month->month)
                          ->whereYear('created_at', '<=', $month->year)
                          ->where(function($q2) use ($month) {
                              $q2->whereNull('ends_at')
                                ->orWhere(function($q3) use ($month) {
                                    $q3->whereMonth('ends_at', '>=', $month->month)
                                      ->whereYear('ends_at', '>=', $month->year);
                                });
                          });
                    });
                })
                ->sum('quantity');

            $monthlySales[] = $regularSales + $subscriptionSales;
        }

        // Pass sales data to view
        $salesData = [
            'labels' => $monthlyLabels,
            'data' => $monthlySales
        ];

        return view('admin.products.show', compact(
            'product',
            'totalSales',
            'totalRevenue',
            'regularSalesCount',
            'regularRevenue',
            'subscriptionSalesCount',
            'subscriptionRevenue',
            'recentOrders',
            'salesData'
        ));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $product = Product::findOrFail($id);
        $categories = Category::all();
        return view('admin.products.edit', compact('product', 'categories'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $product = Product::findOrFail($id);

        $request->validate([
            'name' => 'required|string|max:255',
            'slug' => 'required|string|max:255|unique:products,slug,' . $product->id,
            'description' => 'required|string',
            'price' => 'required|numeric|min:0',
            'sale_price' => 'nullable|numeric|min:0',
            'stock' => 'nullable|integer|min:0',
            'category_id' => 'nullable|exists:categories,id',
            'image' => 'nullable|image|max:2048',
            'gallery.*' => 'nullable|image|max:2048',
            'ingredients' => 'nullable|string',
            'benefits' => 'nullable|string',
            'how_to_use' => 'nullable|string',
            'is_active' => 'nullable|boolean',
            'is_featured' => 'nullable|boolean',
            'allow_subscription' => 'nullable|boolean',
            'subscription_only' => 'nullable|boolean',
            'subscription_price' => 'nullable|numeric|min:0',
            'sku' => 'nullable|string|max:50|unique:products,sku,' . $product->id,
        ]);

        $product->name = $request->name;
        $product->slug = $request->slug;
        $product->description = $request->description;
        $product->price = $request->price;
        $product->sale_price = $request->sale_price;
        $product->stock = $request->stock ?? 0;
        $product->category_id = $request->category_id;
        $product->ingredients = $request->ingredients;
        $product->benefits = $request->benefits;
        $product->how_to_use = $request->how_to_use;
        $product->is_active = $request->has('is_active');
        $product->is_featured = $request->has('is_featured');
        $product->allow_subscription = $request->has('allow_subscription');
        $product->subscription_only = $request->has('subscription_only');
        $product->subscription_price = $request->subscription_price;

        // Update SKU if provided, otherwise keep the existing one
        if ($request->has('sku')) {
            $product->sku = $request->sku;
        } elseif (!$product->sku) {
            // Generate a new SKU if it doesn't exist
            $product->sku = 'SKU-' . strtoupper(Str::random(8));
        }

        // Handle image upload
        if ($request->hasFile('image')) {
            // Delete old image
            if ($product->image) {
                Storage::disk('public')->delete($product->image);
            }
            $imagePath = $request->file('image')->store('products', 'public');
            $product->image = $imagePath;
        }

        // Remove image if requested
        if ($request->has('remove_image') && $product->image) {
            Storage::disk('public')->delete($product->image);
            $product->image = null;
        }

        // Handle gallery uploads
        if ($request->hasFile('gallery')) {
            $galleryPaths = $product->gallery ?? [];
            foreach ($request->file('gallery') as $image) {
                $galleryPaths[] = $image->store('products/gallery', 'public');
            }
            $product->gallery = $galleryPaths;
        }

        // Remove gallery images if requested
        if ($request->has('remove_gallery') && $product->gallery) {
            $galleryToRemove = $request->remove_gallery;
            $gallery = $product->gallery;

            foreach ($galleryToRemove as $index) {
                if (isset($gallery[$index])) {
                    Storage::disk('public')->delete($gallery[$index]);
                    unset($gallery[$index]);
                }
            }

            $product->gallery = array_values($gallery);
        }

        $product->save();

        return redirect()->route('admin.products.index')->with('success', 'Product updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $product = Product::findOrFail($id);

        // Delete image
        if ($product->image) {
            Storage::disk('public')->delete($product->image);
        }

        // Delete gallery images
        if ($product->gallery) {
            foreach ($product->gallery as $image) {
                Storage::disk('public')->delete($image);
            }
        }

        $product->delete();

        return redirect()->route('admin.products.index')->with('success', 'Product deleted successfully.');
    }
}
