<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ShippingOption;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class AdminShippingController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $shippingOptions = ShippingOption::orderBy('display_order')->get();
        return view('admin.shipping.index', compact('shippingOptions'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.shipping.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'cost' => 'required|numeric|min:0',
            'location_type' => 'required|in:all,country,region',
            'locations' => 'nullable|array',
            'delivery_time_min' => 'nullable|integer|min:0',
            'delivery_time_max' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
            'display_order' => 'nullable|integer|min:0',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $data = $request->all();
        $data['is_active'] = $request->has('is_active');
        
        if (!isset($data['display_order']) || $data['display_order'] === null) {
            // Get the highest display order and add 1
            $maxOrder = ShippingOption::max('display_order') ?? 0;
            $data['display_order'] = $maxOrder + 1;
        }

        ShippingOption::create($data);

        return redirect()->route('admin.shipping.index')
            ->with('success', 'Shipping option created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $shippingOption = ShippingOption::findOrFail($id);
        return view('admin.shipping.show', compact('shippingOption'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $shippingOption = ShippingOption::findOrFail($id);
        return view('admin.shipping.edit', compact('shippingOption'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $shippingOption = ShippingOption::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'cost' => 'required|numeric|min:0',
            'location_type' => 'required|in:all,country,region',
            'locations' => 'nullable|array',
            'delivery_time_min' => 'nullable|integer|min:0',
            'delivery_time_max' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
            'display_order' => 'nullable|integer|min:0',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $data = $request->all();
        $data['is_active'] = $request->has('is_active');

        $shippingOption->update($data);

        return redirect()->route('admin.shipping.index')
            ->with('success', 'Shipping option updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $shippingOption = ShippingOption::findOrFail($id);
        $shippingOption->delete();

        return redirect()->route('admin.shipping.index')
            ->with('success', 'Shipping option deleted successfully.');
    }

    /**
     * Update the display order of shipping options.
     */
    public function updateOrder(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'orders' => 'required|array',
            'orders.*' => 'required|integer|exists:shipping_options,id',
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()], 400);
        }

        foreach ($request->orders as $index => $id) {
            ShippingOption::where('id', $id)->update(['display_order' => $index + 1]);
        }

        return response()->json(['success' => true]);
    }
}
