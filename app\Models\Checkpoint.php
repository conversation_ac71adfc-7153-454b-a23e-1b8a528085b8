<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Checkpoint extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'release_id',
        'name',
        'description',
        'type',
        'status',
        'verification_notes',
        'verified_by',
        'verified_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'verified_at' => 'datetime',
    ];

    /**
     * Get the release that owns the checkpoint.
     */
    public function release(): BelongsTo
    {
        return $this->belongsTo(Release::class);
    }

    /**
     * Get the user who verified the checkpoint.
     */
    public function verifier(): BelongsTo
    {
        return $this->belongsTo(User::class, 'verified_by');
    }

    /**
     * Mark the checkpoint as passed.
     */
    public function markAsPassed(?string $notes = null, ?int $userId = null): void
    {
        $this->status = 'passed';
        $this->verification_notes = $notes;
        $this->verified_by = $userId;
        $this->verified_at = now();
        $this->save();
    }

    /**
     * Mark the checkpoint as failed.
     */
    public function markAsFailed(?string $notes = null, ?int $userId = null): void
    {
        $this->status = 'failed';
        $this->verification_notes = $notes;
        $this->verified_by = $userId;
        $this->verified_at = now();
        $this->save();
    }

    /**
     * Mark the checkpoint as in progress.
     */
    public function markAsInProgress(): void
    {
        $this->status = 'in_progress';
        $this->save();
    }

    /**
     * Check if the checkpoint is passed.
     */
    public function isPassed(): bool
    {
        return $this->status === 'passed';
    }

    /**
     * Check if the checkpoint is failed.
     */
    public function isFailed(): bool
    {
        return $this->status === 'failed';
    }

    /**
     * Check if the checkpoint is pending.
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Check if the checkpoint is in progress.
     */
    public function isInProgress(): bool
    {
        return $this->status === 'in_progress';
    }
}
