/* NATRYON Admin Theme CSS */

:root {
    --natryon-grey-1: #b1b7b8;  /* 28.4% */
    --natryon-green-1: #428677; /* 24.5% */
    --natryon-grey-2: #7a868d;  /* 19.5% */
    --natryon-green-2: #2d504b; /* 16.7% */
    --natryon-grey-3: #8ab0a6;  /* 6.6% */
    --natryon-blue: #24445a;    /* 4.3% */
}

/* Text Colors */
.text-natryon-primary {
    color: var(--natryon-green-2) !important;
}

.text-natryon-secondary {
    color: var(--natryon-green-1) !important;
}

.text-natryon-grey {
    color: var(--natryon-grey-2) !important;
}

.text-natryon-blue {
    color: var(--natryon-blue) !important;
}

/* Background Colors */
.bg-natryon-primary {
    background-color: var(--natryon-green-2) !important;
}

.bg-natryon-secondary {
    background-color: var(--natryon-green-1) !important;
}

.bg-natryon-grey-1 {
    background-color: var(--natryon-grey-1) !important;
}

.bg-natryon-grey-2 {
    background-color: var(--natryon-grey-2) !important;
}

.bg-natryon-grey-3 {
    background-color: var(--natryon-grey-3) !important;
}

.bg-natryon-blue {
    background-color: var(--natryon-blue) !important;
}

/* Button Styles */
.btn-natryon-primary {
    background-color: var(--natryon-green-2);
    border-color: var(--natryon-green-2);
    color: #fff;
}

.btn-natryon-primary:hover, 
.btn-natryon-primary:focus {
    background-color: #234039;
    border-color: #234039;
    color: #fff;
}

.btn-natryon-secondary {
    background-color: var(--natryon-green-1);
    border-color: var(--natryon-green-1);
    color: #fff;
}

.btn-natryon-secondary:hover, 
.btn-natryon-secondary:focus {
    background-color: #367568;
    border-color: #367568;
    color: #fff;
}

.btn-natryon-blue {
    background-color: var(--natryon-blue);
    border-color: var(--natryon-blue);
    color: #fff;
}

.btn-natryon-blue:hover, 
.btn-natryon-blue:focus {
    background-color: #1c3648;
    border-color: #1c3648;
    color: #fff;
}

.btn-outline-natryon-primary {
    color: var(--natryon-green-2);
    border-color: var(--natryon-green-2);
}

.btn-outline-natryon-primary:hover, 
.btn-outline-natryon-primary:focus {
    background-color: var(--natryon-green-2);
    border-color: var(--natryon-green-2);
    color: #fff;
}

.btn-outline-natryon-secondary {
    color: var(--natryon-green-1);
    border-color: var(--natryon-green-1);
}

.btn-outline-natryon-secondary:hover, 
.btn-outline-natryon-secondary:focus {
    background-color: var(--natryon-green-1);
    border-color: var(--natryon-green-1);
    color: #fff;
}

.btn-outline-natryon-blue {
    color: var(--natryon-blue);
    border-color: var(--natryon-blue);
}

.btn-outline-natryon-blue:hover, 
.btn-outline-natryon-blue:focus {
    background-color: var(--natryon-blue);
    border-color: var(--natryon-blue);
    color: #fff;
}

/* Badge Styles */
.badge-natryon-primary {
    background-color: var(--natryon-green-2);
    color: #fff;
}

.badge-natryon-secondary {
    background-color: var(--natryon-green-1);
    color: #fff;
}

.badge-natryon-blue {
    background-color: var(--natryon-blue);
    color: #fff;
}

.badge-natryon-grey {
    background-color: var(--natryon-grey-2);
    color: #fff;
}

/* Card Styles */
.card-natryon {
    border: none;
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.card-natryon:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.card-natryon .card-header {
    background-color: var(--natryon-green-2);
    color: #fff;
    border-bottom: none;
    border-radius: 0.5rem 0.5rem 0 0;
}

/* Table Styles */
.table-natryon thead th {
    background-color: var(--natryon-grey-1);
    color: var(--natryon-green-2);
    border-top: none;
    border-bottom: 2px solid var(--natryon-green-2);
}

.table-natryon tbody tr:hover {
    background-color: rgba(177, 183, 184, 0.1);
}

/* Avatar Styles */
.avatar-natryon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(45, 80, 75, 0.1);
    color: var(--natryon-green-2);
    font-weight: 600;
}

/* Status Indicators */
.status-indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 5px;
}

.status-active {
    background-color: var(--natryon-green-1);
}

.status-pending {
    background-color: var(--natryon-grey-2);
}

.status-inactive {
    background-color: #dc3545;
}

/* Custom Pagination */
.pagination .page-item.active .page-link {
    background-color: var(--natryon-green-2);
    border-color: var(--natryon-green-2);
}

.pagination .page-link {
    color: var(--natryon-green-2);
}

.pagination .page-link:hover {
    color: var(--natryon-green-1);
}

/* Custom Progress */
progress {
    width: 100%;
    height: 8px;
    border-radius: 4px;
    overflow: hidden;
}

progress::-webkit-progress-bar {
    background-color: #f0f0f0;
    border-radius: 4px;
}

progress::-webkit-progress-value {
    background-color: var(--natryon-green-2);
    border-radius: 4px;
}

progress::-moz-progress-bar {
    background-color: var(--natryon-green-2);
    border-radius: 4px;
}

/* Custom Form Controls */
.form-control:focus {
    border-color: var(--natryon-green-1);
    box-shadow: 0 0 0 0.25rem rgba(45, 80, 75, 0.25);
}

/* Custom Dropdown */
.dropdown-item.active, 
.dropdown-item:active {
    background-color: var(--natryon-green-2);
}

/* Custom Switch */
.form-switch .form-check-input:checked {
    background-color: var(--natryon-green-1);
    border-color: var(--natryon-green-1);
}

/* Custom Modal */
.modal-header {
    background-color: var(--natryon-green-2);
    color: #fff;
    border-bottom: none;
}

.modal-header .btn-close {
    color: #fff;
    opacity: 0.8;
}

/* Custom Alerts */
.alert-natryon {
    background-color: rgba(45, 80, 75, 0.1);
    border-color: var(--natryon-green-2);
    color: var(--natryon-green-2);
}
