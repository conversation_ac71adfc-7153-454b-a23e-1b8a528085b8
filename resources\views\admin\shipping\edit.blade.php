@extends('layouts.admin')

@section('title', 'Edit Shipping Option')

@section('content')
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Edit Shipping Option</h1>
        <a href="{{ route('admin.shipping.index') }}" class="d-none d-sm-inline-block btn btn-secondary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to Shipping Options
        </a>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Shipping Option Details</h6>
        </div>
        <div class="card-body">
            <form action="{{ route('admin.shipping.update', $shippingOption->id) }}" method="POST">
                @csrf
                @method('PUT')
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="name">Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('name') is-invalid @enderror" id="name" name="name" value="{{ old('name', $shippingOption->name) }}" required>
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="cost">Cost ($) <span class="text-danger">*</span></label>
                            <input type="number" step="0.01" min="0" class="form-control @error('cost') is-invalid @enderror" id="cost" name="cost" value="{{ old('cost', $shippingOption->cost) }}" required>
                            @error('cost')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="description">Description</label>
                    <textarea class="form-control @error('description') is-invalid @enderror" id="description" name="description" rows="3">{{ old('description', $shippingOption->description) }}</textarea>
                    @error('description')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="location_type">Location Type <span class="text-danger">*</span></label>
                            <select class="form-control @error('location_type') is-invalid @enderror" id="location_type" name="location_type" required>
                                <option value="all" {{ old('location_type', $shippingOption->location_type) == 'all' ? 'selected' : '' }}>All Locations</option>
                                <option value="country" {{ old('location_type', $shippingOption->location_type) == 'country' ? 'selected' : '' }}>Specific Countries</option>
                                <option value="region" {{ old('location_type', $shippingOption->location_type) == 'region' ? 'selected' : '' }}>Specific Regions</option>
                            </select>
                            @error('location_type')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="display_order">Display Order</label>
                            <input type="number" min="0" class="form-control @error('display_order') is-invalid @enderror" id="display_order" name="display_order" value="{{ old('display_order', $shippingOption->display_order) }}">
                            @error('display_order')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="delivery_time_min">Minimum Delivery Time (days)</label>
                            <input type="number" min="0" class="form-control @error('delivery_time_min') is-invalid @enderror" id="delivery_time_min" name="delivery_time_min" value="{{ old('delivery_time_min', $shippingOption->delivery_time_min) }}">
                            @error('delivery_time_min')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="delivery_time_max">Maximum Delivery Time (days)</label>
                            <input type="number" min="0" class="form-control @error('delivery_time_max') is-invalid @enderror" id="delivery_time_max" name="delivery_time_max" value="{{ old('delivery_time_max', $shippingOption->delivery_time_max) }}">
                            @error('delivery_time_max')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>
                
                <div class="form-group" id="locations-container" style="{{ $shippingOption->location_type === 'all' ? 'display: none;' : '' }}">
                    <label for="locations">Locations</label>
                    <select class="form-control @error('locations') is-invalid @enderror" id="locations" name="locations[]" multiple>
                        <!-- Locations will be populated via JavaScript -->
                    </select>
                    <small class="form-text text-muted">Hold Ctrl (or Cmd on Mac) to select multiple locations</small>
                    @error('locations')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
                
                <div class="form-group form-check">
                    <input type="checkbox" class="form-check-input" id="is_active" name="is_active" value="1" {{ old('is_active', $shippingOption->is_active) ? 'checked' : '' }}>
                    <label class="form-check-label" for="is_active">Active</label>
                </div>
                
                <button type="submit" class="btn btn-primary">Update Shipping Option</button>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const locationTypeSelect = document.getElementById('location_type');
        const locationsContainer = document.getElementById('locations-container');
        const locationsSelect = document.getElementById('locations');
        
        // Countries list
        const countries = {
            'US': 'United States',
            'CA': 'Canada',
            'MX': 'Mexico',
            'GB': 'United Kingdom',
            'FR': 'France',
            'DE': 'Germany',
            'IT': 'Italy',
            'ES': 'Spain',
            'JP': 'Japan',
            'CN': 'China',
            'AU': 'Australia',
            'NZ': 'New Zealand',
            'BR': 'Brazil',
            'AR': 'Argentina',
            'ZA': 'South Africa',
            'IN': 'India',
            'RU': 'Russia',
            // Add more countries as needed
        };
        
        // Regions list
        const regions = {
            'NA': 'North America',
            'SA': 'South America',
            'EU': 'Europe',
            'AS': 'Asia',
            'AF': 'Africa',
            'OC': 'Oceania',
            'ME': 'Middle East',
            // Add more regions as needed
        };
        
        // Get the current locations from the model
        const currentLocations = @json($shippingOption->locations ?? []);
        
        // Function to populate locations select
        function populateLocations(type) {
            // Clear existing options
            locationsSelect.innerHTML = '';
            
            // Show or hide locations container based on type
            if (type === 'all') {
                locationsContainer.style.display = 'none';
            } else {
                locationsContainer.style.display = 'block';
                
                // Add options based on type
                const options = type === 'country' ? countries : regions;
                
                for (const [code, name] of Object.entries(options)) {
                    const option = document.createElement('option');
                    option.value = code;
                    option.textContent = name;
                    
                    // Select if in current locations
                    if (currentLocations.includes(code)) {
                        option.selected = true;
                    }
                    
                    locationsSelect.appendChild(option);
                }
            }
        }
        
        // Initial population
        populateLocations(locationTypeSelect.value);
        
        // Update on change
        locationTypeSelect.addEventListener('change', function() {
            populateLocations(this.value);
        });
    });
</script>
@endpush
