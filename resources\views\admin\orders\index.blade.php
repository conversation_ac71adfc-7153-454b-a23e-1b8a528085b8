@extends('layouts.admin')

@section('title', 'Manage Orders')

@section('content')
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-natryon-primary">
            <i class="bi bi-cart-check me-2"></i>Orders
        </h1>
        <div class="d-flex">
            <a href="{{ route('admin.orders.index', ['export' => 'csv']) }}" class="btn btn-outline-natryon-primary me-2">
                <i class="bi bi-download me-1"></i> Export CSV
            </a>
            <div class="dropdown">
                <button class="btn btn-natryon-primary dropdown-toggle" type="button" id="orderActions" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="bi bi-gear me-1"></i> Actions
                </button>
                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="orderActions">
                    <li><a class="dropdown-item" href="{{ route('admin.orders.index', ['status' => 'pending']) }}">
                        <i class="bi bi-hourglass me-2 text-natryon-grey"></i>View Pending Orders
                    </a></li>
                    <li><a class="dropdown-item" href="{{ route('admin.orders.index', ['status' => 'processing']) }}">
                        <i class="bi bi-gear me-2 text-natryon-blue"></i>View Processing Orders
                    </a></li>
                    <li><a class="dropdown-item" href="{{ route('admin.orders.index', ['status' => 'completed']) }}">
                        <i class="bi bi-check-circle me-2 text-natryon-secondary"></i>View Completed Orders
                    </a></li>
                    <li><a class="dropdown-item" href="{{ route('admin.orders.index', ['status' => 'declined']) }}">
                        <i class="bi bi-x-circle me-2 text-danger"></i>View Declined Orders
                    </a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="{{ route('admin.orders.index', ['payment' => 'paid']) }}">
                        <i class="bi bi-credit-card-fill me-2 text-natryon-secondary"></i>View Paid Orders
                    </a></li>
                    <li><a class="dropdown-item" href="{{ route('admin.orders.index', ['payment' => 'unpaid']) }}">
                        <i class="bi bi-credit-card me-2 text-natryon-grey"></i>View Unpaid Orders
                    </a></li>
                </ul>
            </div>
        </div>
    </div>

    <div class="card card-natryon">
        <div class="card-header bg-natryon-primary text-white py-3">
            <h5 class="mb-0 fw-bold"><i class="bi bi-list-ul me-2"></i>Order List</h5>
        </div>
        <div class="card-body">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <form action="{{ route('admin.orders.index') }}" method="GET" class="d-flex">
                    <div class="input-group">
                        <input type="text" name="search" class="form-control" placeholder="Search by order #, customer name, or email..." value="{{ request('search') }}">
                        <button type="submit" class="btn btn-outline-natryon-primary">
                            <i class="bi bi-search"></i>
                        </button>
                    </div>
                </form>
                <div class="d-flex">
                    <div class="dropdown me-2">
                        <button class="btn btn-outline-natryon-primary dropdown-toggle" type="button" id="filterDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi bi-funnel me-1"></i> Filter
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="filterDropdown">
                            <li><h6 class="dropdown-header">Order Status</h6></li>
                            <li><a class="dropdown-item" href="{{ route('admin.orders.index', ['status' => 'pending']) }}">
                                <i class="bi bi-hourglass me-2 text-natryon-grey"></i>Pending
                            </a></li>
                            <li><a class="dropdown-item" href="{{ route('admin.orders.index', ['status' => 'processing']) }}">
                                <i class="bi bi-gear me-2 text-natryon-blue"></i>Processing
                            </a></li>
                            <li><a class="dropdown-item" href="{{ route('admin.orders.index', ['status' => 'completed']) }}">
                                <i class="bi bi-check-circle me-2 text-natryon-secondary"></i>Completed
                            </a></li>
                            <li><a class="dropdown-item" href="{{ route('admin.orders.index', ['status' => 'declined']) }}">
                                <i class="bi bi-x-circle me-2 text-danger"></i>Declined
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><h6 class="dropdown-header">Payment Status</h6></li>
                            <li><a class="dropdown-item" href="{{ route('admin.orders.index', ['payment' => 'paid']) }}">
                                <i class="bi bi-credit-card-fill me-2 text-natryon-secondary"></i>Paid
                            </a></li>
                            <li><a class="dropdown-item" href="{{ route('admin.orders.index', ['payment' => 'unpaid']) }}">
                                <i class="bi bi-credit-card me-2 text-natryon-grey"></i>Unpaid
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ route('admin.orders.index') }}">
                                <i class="bi bi-x-circle me-2"></i>Clear Filters
                            </a></li>
                        </ul>
                    </div>
                    <div class="dropdown">
                        <button class="btn btn-outline-natryon-secondary dropdown-toggle" type="button" id="sortDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi bi-sort-down me-1"></i> Sort
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="sortDropdown">
                            <li><a class="dropdown-item" href="{{ route('admin.orders.index', ['sort' => 'newest']) }}">
                                <i class="bi bi-calendar-check me-2"></i>Date (Newest First)
                            </a></li>
                            <li><a class="dropdown-item" href="{{ route('admin.orders.index', ['sort' => 'oldest']) }}">
                                <i class="bi bi-calendar me-2"></i>Date (Oldest First)
                            </a></li>
                            <li><a class="dropdown-item" href="{{ route('admin.orders.index', ['sort' => 'total_high']) }}">
                                <i class="bi bi-arrow-down me-2"></i>Total (High to Low)
                            </a></li>
                            <li><a class="dropdown-item" href="{{ route('admin.orders.index', ['sort' => 'total_low']) }}">
                                <i class="bi bi-arrow-up me-2"></i>Total (Low to High)
                            </a></li>
                        </ul>
                    </div>
                </div>
            </div>

            @if(isset($orders) && count($orders) > 0)
                <div class="table-responsive">
                    <table class="table table-natryon table-hover align-middle">
                        <thead>
                            <tr>
                                <th>Order #</th>
                                <th>Customer</th>
                                <th>Date</th>
                                <th>Total</th>
                                <th>Status</th>
                                <th>Payment</th>
                                <th style="width: 150px;">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($orders as $order)
                                <tr>
                                    <td>
                                        <span class="fw-bold text-natryon-primary">{{ $order->order_number }}</span>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-natryon me-2">
                                                {{ strtoupper(substr($order->billing_name, 0, 1)) }}
                                            </div>
                                            <div>
                                                <div class="fw-medium">{{ $order->billing_name }}</div>
                                                <small class="text-muted">{{ $order->billing_email }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="bi bi-calendar-date me-2 text-natryon-grey"></i>
                                            {{ $order->created_at->format('M d, Y H:i') }}
                                        </div>
                                    </td>
                                    <td>
                                        <span class="fw-bold">${{ number_format($order->total, 2) }}</span>
                                    </td>
                                    <td>
                                        <div class="dropdown">
                                            <button class="btn btn-sm dropdown-toggle rounded-pill
                                                @if($order->status == 'pending') btn-outline-natryon-grey
                                                @elseif($order->status == 'processing') btn-outline-natryon-blue
                                                @elseif($order->status == 'completed') btn-outline-natryon-secondary
                                                @elseif($order->status == 'declined') btn-outline-danger
                                                @endif"
                                                type="button" id="statusDropdown{{ $order->id }}" data-bs-toggle="dropdown" aria-expanded="false">
                                                <i class="bi
                                                    @if($order->status == 'pending') bi-hourglass
                                                    @elseif($order->status == 'processing') bi-gear
                                                    @elseif($order->status == 'completed') bi-check-circle
                                                    @elseif($order->status == 'declined') bi-x-circle
                                                    @endif me-1"></i>
                                                {{ ucfirst($order->status) }}
                                            </button>
                                            <ul class="dropdown-menu" aria-labelledby="statusDropdown{{ $order->id }}">
                                                <li>
                                                    <form action="{{ route('admin.orders.update', $order->id) }}" method="POST">
                                                        @csrf
                                                        @method('PUT')
                                                        <input type="hidden" name="status" value="pending">
                                                        <button type="submit" class="dropdown-item status-confirm" data-status="pending" data-name="Order #{{ $order->order_number }}">
                                                            <i class="bi bi-hourglass me-2 text-natryon-grey"></i>Pending
                                                        </button>
                                                    </form>
                                                </li>
                                                <li>
                                                    <form action="{{ route('admin.orders.update', $order->id) }}" method="POST">
                                                        @csrf
                                                        @method('PUT')
                                                        <input type="hidden" name="status" value="processing">
                                                        <button type="submit" class="dropdown-item status-confirm" data-status="processing" data-name="Order #{{ $order->order_number }}">
                                                            <i class="bi bi-gear me-2 text-natryon-blue"></i>Processing
                                                        </button>
                                                    </form>
                                                </li>
                                                <li>
                                                    <form action="{{ route('admin.orders.update', $order->id) }}" method="POST">
                                                        @csrf
                                                        @method('PUT')
                                                        <input type="hidden" name="status" value="completed">
                                                        <button type="submit" class="dropdown-item status-confirm" data-status="completed" data-name="Order #{{ $order->order_number }}">
                                                            <i class="bi bi-check-circle me-2 text-natryon-secondary"></i>Completed
                                                        </button>
                                                    </form>
                                                </li>
                                                <li>
                                                    <form action="{{ route('admin.orders.update', $order->id) }}" method="POST">
                                                        @csrf
                                                        @method('PUT')
                                                        <input type="hidden" name="status" value="declined">
                                                        <button type="submit" class="dropdown-item status-confirm" data-status="declined" data-name="Order #{{ $order->order_number }}">
                                                            <i class="bi bi-x-circle me-2 text-danger"></i>Declined
                                                        </button>
                                                    </form>
                                                </li>
                                            </ul>
                                        </div>
                                    </td>
                                    <td>
                                        @if($order->is_paid)
                                            <span class="badge badge-natryon-secondary rounded-pill px-3 py-2">
                                                <i class="bi bi-credit-card-fill me-1"></i> Paid
                                            </span>
                                            @if($order->paid_at)
                                                <small class="d-block text-muted mt-1">{{ $order->paid_at->format('M d, Y') }}</small>
                                            @endif
                                        @else
                                            <span class="badge badge-natryon-grey rounded-pill px-3 py-2">
                                                <i class="bi bi-credit-card me-1"></i> Unpaid
                                            </span>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="d-flex gap-1">
                                            <a href="{{ route('admin.orders.show', $order->id) }}" class="btn btn-sm btn-outline-natryon-primary rounded-circle" title="View Order">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                            <a href="{{ route('admin.orders.edit', $order->id) }}" class="btn btn-sm btn-outline-natryon-secondary rounded-circle" title="Edit Order">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-outline-danger rounded-circle" data-bs-toggle="modal" data-bs-target="#deleteModal{{ $order->id }}" title="Delete Order">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </div>

                                        <!-- Delete Modal -->
                                        <div class="modal fade" id="deleteModal{{ $order->id }}" tabindex="-1" aria-labelledby="deleteModalLabel{{ $order->id }}" aria-hidden="true">
                                            <div class="modal-dialog">
                                                <div class="modal-content">
                                                    <div class="modal-header bg-natryon-primary text-white">
                                                        <h5 class="modal-title" id="deleteModalLabel{{ $order->id }}">
                                                            <i class="bi bi-exclamation-triangle-fill me-2"></i>Confirm Delete
                                                        </h5>
                                                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                                                    </div>
                                                    <div class="modal-body">
                                                        <p class="mb-0">Are you sure you want to delete Order <strong class="text-natryon-primary">#{{ $order->order_number }}</strong>?</p>
                                                        <p class="text-danger mb-0"><small><i class="bi bi-exclamation-circle me-1"></i>This action cannot be undone.</small></p>
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                                                            <i class="bi bi-x-circle me-1"></i>Cancel
                                                        </button>
                                                        <form action="{{ route('admin.orders.destroy', $order->id) }}" method="POST">
                                                            @csrf
                                                            @method('DELETE')
                                                            <button type="submit" class="btn btn-danger delete-confirm" data-name="Order #{{ $order->order_number }}">
                                                                <i class="bi bi-trash me-1"></i>Delete
                                                            </button>
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <div class="d-flex justify-content-between align-items-center mt-4">
                    <div class="text-natryon-grey">
                        <i class="bi bi-info-circle me-1"></i> Showing {{ $orders->firstItem() ?? 0 }} to {{ $orders->lastItem() ?? 0 }} of {{ $orders->total() ?? 0 }} orders
                    </div>
                    <div>
                        {{ $orders->withQueryString()->links() }}
                    </div>
                </div>
            @else
                <div class="text-center py-5">
                    <div class="mb-4">
                        <span class="d-inline-block p-3 bg-natryon-grey-1 bg-opacity-25 rounded-circle">
                            <i class="bi bi-cart-x text-natryon-grey" style="font-size: 3rem;"></i>
                        </span>
                    </div>
                    <h4 class="text-natryon-primary mb-3">No orders found</h4>
                    <p class="text-natryon-grey mb-4">
                        @if(request('search'))
                            No orders match your search criteria. <a href="{{ route('admin.orders.index') }}" class="text-natryon-secondary">Clear search</a>
                        @elseif(request('status'))
                            No orders with status "{{ request('status') }}". <a href="{{ route('admin.orders.index') }}" class="text-natryon-secondary">View all orders</a>
                        @elseif(request('payment'))
                            No {{ request('payment') }} orders found. <a href="{{ route('admin.orders.index') }}" class="text-natryon-secondary">View all orders</a>
                        @else
                            There are no orders in the system yet.
                        @endif
                    </p>
                    <a href="{{ route('admin.dashboard') }}" class="btn btn-natryon-primary">
                        <i class="bi bi-arrow-left me-1"></i> Back to Dashboard
                    </a>
                </div>
            @endif
        </div>
    </div>
@endsection
