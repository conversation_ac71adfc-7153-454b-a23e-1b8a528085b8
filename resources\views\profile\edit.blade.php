@extends('layouts.app')

@section('title', 'Edit Profile - NATRYON')

@section('content')
    <!-- Hero Section -->
    <section class="py-4 bg-light">
        <div class="container">
            <div class="text-center mb-5">
                <h1 class="display-5 fw-bold" style="color: #428677;">My Profile</h1>
                <p class="lead">Update your personal information</p>
            </div>
        </div>
    </section>

    <!-- Profile Section -->
    <section class="py-5">
        <div class="container">
            <div class="row">
                <!-- Sidebar -->
                <div class="col-lg-3 mb-4">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-white py-3">
                            <h5 class="mb-0" style="color: #428677;">Account Menu</h5>
                        </div>
                        <div class="list-group list-group-flush">
                            <a href="{{ route('profile.edit') }}" class="list-group-item list-group-item-action active">
                                <i class="bi bi-person me-2"></i> Profile Information
                            </a>
                            <a href="{{ route('profile.password') }}" class="list-group-item list-group-item-action">
                                <i class="bi bi-shield-lock me-2"></i> Change Password
                            </a>
                            <a href="{{ route('orders.index') }}" class="list-group-item list-group-item-action">
                                <i class="bi bi-box me-2"></i> My Orders
                            </a>
                            <a href="{{ route('affiliate.index') }}" class="list-group-item list-group-item-action">
                                <i class="bi bi-graph-up me-2"></i> Affiliate Dashboard
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Profile Form -->
                <div class="col-lg-9">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-white py-3">
                            <h5 class="mb-0" style="color: #428677;">Edit Profile</h5>
                        </div>
                        <div class="card-body">
                            @if(session('success'))
                                <div class="alert alert-success alert-dismissible fade show" role="alert">
                                    {{ session('success') }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                </div>
                            @endif

                            <form action="{{ route('profile.update') }}" method="POST">
                                @csrf
                                @method('PUT')

                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label for="name" class="form-label">Full Name</label>
                                        <input type="text" class="form-control @error('name') is-invalid @enderror" id="name" name="name" value="{{ old('name', $user->name) }}" required>
                                        @error('name')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <div class="col-md-6">
                                        <label for="email" class="form-label">Email Address</label>
                                        <input type="email" class="form-control @error('email') is-invalid @enderror" id="email" name="email" value="{{ old('email', $user->email) }}" required>
                                        @error('email')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <div class="col-md-6">
                                        <label for="phone" class="form-label">Phone Number</label>
                                        <input type="text" class="form-control @error('phone') is-invalid @enderror" id="phone" name="phone" value="{{ old('phone', $user->phone) }}">
                                        @error('phone')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <div class="col-md-6">
                                        <label for="country" class="form-label">Country</label>
                                        <select class="form-select @error('country') is-invalid @enderror" id="country" name="country">
                                            <option value="">Select Country</option>

                                            <!-- Africa -->
                                            <optgroup label="Africa">
                                                <option value="MA" {{ old('country', $user->country) == 'MA' ? 'selected' : '' }}>Morocco</option>
                                            </optgroup>

                                            <!-- Americas -->
                                            <optgroup label="Americas">
                                                <option value="AR" {{ old('country', $user->country) == 'AR' ? 'selected' : '' }}>Argentina</option>
                                                <option value="BS" {{ old('country', $user->country) == 'BS' ? 'selected' : '' }}>Bahamas</option>
                                                <option value="BR" {{ old('country', $user->country) == 'BR' ? 'selected' : '' }}>Brazil</option>
                                                <option value="CA" {{ old('country', $user->country) == 'CA' ? 'selected' : '' }}>Canada</option>
                                                <option value="CL" {{ old('country', $user->country) == 'CL' ? 'selected' : '' }}>Chile</option>
                                                <option value="CO" {{ old('country', $user->country) == 'CO' ? 'selected' : '' }}>Colombia</option>
                                                <option value="CR" {{ old('country', $user->country) == 'CR' ? 'selected' : '' }}>Costa Rica</option>
                                                <option value="DO" {{ old('country', $user->country) == 'DO' ? 'selected' : '' }}>Dominican Republic</option>
                                                <option value="EC" {{ old('country', $user->country) == 'EC' ? 'selected' : '' }}>Ecuador</option>
                                                <option value="SV" {{ old('country', $user->country) == 'SV' ? 'selected' : '' }}>El Salvador</option>
                                                <option value="GT" {{ old('country', $user->country) == 'GT' ? 'selected' : '' }}>Guatemala</option>
                                                <option value="HN" {{ old('country', $user->country) == 'HN' ? 'selected' : '' }}>Honduras</option>
                                                <option value="JM" {{ old('country', $user->country) == 'JM' ? 'selected' : '' }}>Jamaica</option>
                                                <option value="MX" {{ old('country', $user->country) == 'MX' ? 'selected' : '' }}>Mexico</option>
                                                <option value="NI" {{ old('country', $user->country) == 'NI' ? 'selected' : '' }}>Nicaragua</option>
                                                <option value="PA" {{ old('country', $user->country) == 'PA' ? 'selected' : '' }}>Panama</option>
                                                <option value="PY" {{ old('country', $user->country) == 'PY' ? 'selected' : '' }}>Paraguay</option>
                                                <option value="PE" {{ old('country', $user->country) == 'PE' ? 'selected' : '' }}>Peru</option>
                                                <option value="PR" {{ old('country', $user->country) == 'PR' ? 'selected' : '' }}>Puerto Rico</option>
                                                <option value="TT" {{ old('country', $user->country) == 'TT' ? 'selected' : '' }}>Trinidad and Tobago</option>
                                                <option value="US" {{ old('country', $user->country) == 'US' ? 'selected' : '' }}>United States</option>
                                                <option value="UY" {{ old('country', $user->country) == 'UY' ? 'selected' : '' }}>Uruguay</option>
                                                <option value="VE" {{ old('country', $user->country) == 'VE' ? 'selected' : '' }}>Venezuela</option>
                                            </optgroup>

                                            <!-- Asia -->
                                            <optgroup label="Asia">
                                                <option value="BD" {{ old('country', $user->country) == 'BD' ? 'selected' : '' }}>Bangladesh</option>
                                                <option value="CN" {{ old('country', $user->country) == 'CN' ? 'selected' : '' }}>China</option>
                                                <option value="HK" {{ old('country', $user->country) == 'HK' ? 'selected' : '' }}>Hong Kong</option>
                                                <option value="IN" {{ old('country', $user->country) == 'IN' ? 'selected' : '' }}>India</option>
                                                <option value="ID" {{ old('country', $user->country) == 'ID' ? 'selected' : '' }}>Indonesia</option>
                                                <option value="IL" {{ old('country', $user->country) == 'IL' ? 'selected' : '' }}>Israel</option>
                                                <option value="JP" {{ old('country', $user->country) == 'JP' ? 'selected' : '' }}>Japan</option>
                                                <option value="JO" {{ old('country', $user->country) == 'JO' ? 'selected' : '' }}>Jordan</option>
                                                <option value="KW" {{ old('country', $user->country) == 'KW' ? 'selected' : '' }}>Kuwait</option>
                                                <option value="LB" {{ old('country', $user->country) == 'LB' ? 'selected' : '' }}>Lebanon</option>
                                                <option value="MY" {{ old('country', $user->country) == 'MY' ? 'selected' : '' }}>Malaysia</option>
                                                <option value="PK" {{ old('country', $user->country) == 'PK' ? 'selected' : '' }}>Pakistan</option>
                                                <option value="PH" {{ old('country', $user->country) == 'PH' ? 'selected' : '' }}>Philippines</option>
                                                <option value="QA" {{ old('country', $user->country) == 'QA' ? 'selected' : '' }}>Qatar</option>
                                                <option value="SA" {{ old('country', $user->country) == 'SA' ? 'selected' : '' }}>Saudi Arabia</option>
                                                <option value="SG" {{ old('country', $user->country) == 'SG' ? 'selected' : '' }}>Singapore</option>
                                                <option value="KR" {{ old('country', $user->country) == 'KR' ? 'selected' : '' }}>South Korea</option>
                                                <option value="LK" {{ old('country', $user->country) == 'LK' ? 'selected' : '' }}>Sri Lanka</option>
                                                <option value="TW" {{ old('country', $user->country) == 'TW' ? 'selected' : '' }}>Taiwan</option>
                                                <option value="TH" {{ old('country', $user->country) == 'TH' ? 'selected' : '' }}>Thailand</option>
                                                <option value="TR" {{ old('country', $user->country) == 'TR' ? 'selected' : '' }}>Turkey</option>
                                                <option value="AE" {{ old('country', $user->country) == 'AE' ? 'selected' : '' }}>United Arab Emirates</option>
                                                <option value="VN" {{ old('country', $user->country) == 'VN' ? 'selected' : '' }}>Vietnam</option>
                                            </optgroup>

                                            <!-- Europe -->
                                            <optgroup label="Europe">
                                                <option value="AT" {{ old('country', $user->country) == 'AT' ? 'selected' : '' }}>Austria</option>
                                                <option value="BE" {{ old('country', $user->country) == 'BE' ? 'selected' : '' }}>Belgium</option>
                                                <option value="BG" {{ old('country', $user->country) == 'BG' ? 'selected' : '' }}>Bulgaria</option>
                                                <option value="HR" {{ old('country', $user->country) == 'HR' ? 'selected' : '' }}>Croatia</option>
                                                <option value="CY" {{ old('country', $user->country) == 'CY' ? 'selected' : '' }}>Cyprus</option>
                                                <option value="CZ" {{ old('country', $user->country) == 'CZ' ? 'selected' : '' }}>Czech Republic</option>
                                                <option value="DK" {{ old('country', $user->country) == 'DK' ? 'selected' : '' }}>Denmark</option>
                                                <option value="EE" {{ old('country', $user->country) == 'EE' ? 'selected' : '' }}>Estonia</option>
                                                <option value="FI" {{ old('country', $user->country) == 'FI' ? 'selected' : '' }}>Finland</option>
                                                <option value="FR" {{ old('country', $user->country) == 'FR' ? 'selected' : '' }}>France</option>
                                                <option value="DE" {{ old('country', $user->country) == 'DE' ? 'selected' : '' }}>Germany</option>
                                                <option value="GR" {{ old('country', $user->country) == 'GR' ? 'selected' : '' }}>Greece</option>
                                                <option value="HU" {{ old('country', $user->country) == 'HU' ? 'selected' : '' }}>Hungary</option>
                                                <option value="IS" {{ old('country', $user->country) == 'IS' ? 'selected' : '' }}>Iceland</option>
                                                <option value="IE" {{ old('country', $user->country) == 'IE' ? 'selected' : '' }}>Ireland</option>
                                                <option value="IT" {{ old('country', $user->country) == 'IT' ? 'selected' : '' }}>Italy</option>
                                                <option value="LV" {{ old('country', $user->country) == 'LV' ? 'selected' : '' }}>Latvia</option>
                                                <option value="LT" {{ old('country', $user->country) == 'LT' ? 'selected' : '' }}>Lithuania</option>
                                                <option value="LU" {{ old('country', $user->country) == 'LU' ? 'selected' : '' }}>Luxembourg</option>
                                                <option value="MT" {{ old('country', $user->country) == 'MT' ? 'selected' : '' }}>Malta</option>
                                                <option value="NL" {{ old('country', $user->country) == 'NL' ? 'selected' : '' }}>Netherlands</option>
                                                <option value="NO" {{ old('country', $user->country) == 'NO' ? 'selected' : '' }}>Norway</option>
                                                <option value="PL" {{ old('country', $user->country) == 'PL' ? 'selected' : '' }}>Poland</option>
                                                <option value="PT" {{ old('country', $user->country) == 'PT' ? 'selected' : '' }}>Portugal</option>
                                                <option value="RO" {{ old('country', $user->country) == 'RO' ? 'selected' : '' }}>Romania</option>
                                                <option value="RU" {{ old('country', $user->country) == 'RU' ? 'selected' : '' }}>Russia</option>
                                                <option value="RS" {{ old('country', $user->country) == 'RS' ? 'selected' : '' }}>Serbia</option>
                                                <option value="SK" {{ old('country', $user->country) == 'SK' ? 'selected' : '' }}>Slovakia</option>
                                                <option value="SI" {{ old('country', $user->country) == 'SI' ? 'selected' : '' }}>Slovenia</option>
                                                <option value="ES" {{ old('country', $user->country) == 'ES' ? 'selected' : '' }}>Spain</option>
                                                <option value="SE" {{ old('country', $user->country) == 'SE' ? 'selected' : '' }}>Sweden</option>
                                                <option value="CH" {{ old('country', $user->country) == 'CH' ? 'selected' : '' }}>Switzerland</option>
                                                <option value="UA" {{ old('country', $user->country) == 'UA' ? 'selected' : '' }}>Ukraine</option>
                                                <option value="GB" {{ old('country', $user->country) == 'GB' ? 'selected' : '' }}>United Kingdom</option>
                                            </optgroup>

                                            <!-- Oceania -->
                                            <optgroup label="Oceania">
                                                <option value="AU" {{ old('country', $user->country) == 'AU' ? 'selected' : '' }}>Australia</option>
                                                <option value="NZ" {{ old('country', $user->country) == 'NZ' ? 'selected' : '' }}>New Zealand</option>
                                            </optgroup>
                                        </select>
                                        @error('country')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <div class="col-12">
                                        <label for="address" class="form-label">Address</label>
                                        <input type="text" class="form-control @error('address') is-invalid @enderror" id="address" name="address" value="{{ old('address', $user->address) }}">
                                        @error('address')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <div class="col-md-6">
                                        <label for="city" class="form-label">City</label>
                                        <input type="text" class="form-control @error('city') is-invalid @enderror" id="city" name="city" value="{{ old('city', $user->city) }}">
                                        @error('city')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <div class="col-md-4">
                                        <label for="state" class="form-label">State/Province</label>
                                        <input type="text" class="form-control @error('state') is-invalid @enderror" id="state" name="state" value="{{ old('state', $user->state) }}">
                                        @error('state')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <div class="col-md-2">
                                        <label for="zip_code" class="form-label">Zip/Postal</label>
                                        <input type="text" class="form-control @error('zip_code') is-invalid @enderror" id="zip_code" name="zip_code" value="{{ old('zip_code', $user->zip_code) }}">
                                        @error('zip_code')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                                    <button type="submit" class="btn btn-primary" style="background-color: #428677; border-color: #428677;">
                                        <i class="bi bi-check-circle me-2"></i>Save Changes
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection
