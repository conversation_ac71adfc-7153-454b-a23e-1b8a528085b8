<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Currency extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'code',
        'name',
        'symbol',
        'exchange_rate',
        'is_active',
        'is_default',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'exchange_rate' => 'decimal:6',
        'is_active' => 'boolean',
        'is_default' => 'boolean',
    ];

    /**
     * Get the user preferences for this currency.
     */
    public function userPreferences(): Has<PERSON>any
    {
        return $this->hasMany(UserCurrencyPreference::class, 'currency_code', 'code');
    }

    /**
     * Get the products using this currency.
     */
    public function products(): HasMany
    {
        return $this->hasMany(Product::class, 'currency', 'code');
    }

    /**
     * Get the orders using this currency.
     */
    public function orders(): HasMany
    {
        return $this->hasMany(Order::class, 'currency', 'code');
    }

    /**
     * Get active currencies.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Get the default currency.
     */
    public static function getDefault()
    {
        return static::where('is_default', true)->first() ?? static::where('code', 'EUR')->first();
    }

    /**
     * Convert amount from one currency to another.
     */
    public static function convert($amount, $fromCurrency, $toCurrency)
    {
        if ($fromCurrency === $toCurrency) {
            return $amount;
        }

        $fromRate = static::where('code', $fromCurrency)->value('exchange_rate') ?? 1;
        $toRate = static::where('code', $toCurrency)->value('exchange_rate') ?? 1;

        // Convert to base currency (EUR) first, then to target currency
        $baseAmount = $amount / $fromRate;
        return $baseAmount * $toRate;
    }

    /**
     * Format amount with currency symbol.
     */
    public function formatAmount($amount)
    {
        return $this->symbol . number_format($amount, 2);
    }

    /**
     * Get currency symbol by code.
     */
    public static function getSymbol($code)
    {
        return static::where('code', $code)->value('symbol') ?? $code;
    }
}
