@extends('layouts.admin')

@section('title', 'Edit User')

@section('content')
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-natryon-primary">
            <i class="bi bi-person-gear me-2"></i>Edit User
        </h1>
        <div>
            <a href="{{ route('admin.users.show', $user->id) }}" class="btn btn-outline-natryon-blue me-2">
                <i class="bi bi-eye me-1"></i> View User
            </a>
            <a href="{{ route('admin.users.index') }}" class="btn btn-outline-natryon-primary">
                <i class="bi bi-arrow-left me-1"></i> Back to Users
            </a>
        </div>
    </div>

    <div class="card card-natryon">
        <div class="card-header bg-natryon-primary text-white py-3">
            <h5 class="mb-0 fw-bold"><i class="bi bi-person-fill-gear me-2"></i>Edit User: {{ $user->name }}</h5>
        </div>
        <div class="card-body">
            <form action="{{ route('admin.users.update', $user->id) }}" method="POST">
                @csrf
                @method('PUT')
                
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="alert alert-natryon">
                            <div class="d-flex">
                                <div class="me-3">
                                    <i class="bi bi-info-circle-fill fs-4 text-natryon-primary"></i>
                                </div>
                                <div>
                                    <h5 class="alert-heading text-natryon-primary">User Details</h5>
                                    <p class="mb-0">Update the user's information. Leave the password fields blank to keep the current password.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="alert alert-natryon" style="background-color: rgba(36, 68, 90, 0.1);">
                            <div class="d-flex">
                                <div class="me-3">
                                    <i class="bi bi-shield-lock-fill fs-4 text-natryon-blue"></i>
                                </div>
                                <div>
                                    <h5 class="alert-heading text-natryon-blue">User Roles</h5>
                                    <p class="mb-0">Modify user roles to change access permissions. Be careful when changing admin roles.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="name" class="form-label fw-medium">Name <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="bi bi-person"></i></span>
                                <input type="text" class="form-control @error('name') is-invalid @enderror" id="name" name="name" value="{{ old('name', $user->name) }}" required>
                            </div>
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="email" class="form-label fw-medium">Email <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="bi bi-envelope"></i></span>
                                <input type="email" class="form-control @error('email') is-invalid @enderror" id="email" name="email" value="{{ old('email', $user->email) }}" required>
                            </div>
                            @error('email')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="password" class="form-label fw-medium">New Password</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="bi bi-lock"></i></span>
                                <input type="password" class="form-control @error('password') is-invalid @enderror" id="password" name="password">
                            </div>
                            <small class="text-muted">Leave blank to keep current password</small>
                            @error('password')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="password_confirmation" class="form-label fw-medium">Confirm New Password</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="bi bi-lock-fill"></i></span>
                                <input type="password" class="form-control" id="password_confirmation" name="password_confirmation">
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="phone" class="form-label fw-medium">Phone Number</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="bi bi-telephone"></i></span>
                                <input type="text" class="form-control @error('phone') is-invalid @enderror" id="phone" name="phone" value="{{ old('phone', $user->phone) }}">
                            </div>
                            @error('phone')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-4">
                            <label class="form-label fw-medium">User Role <span class="text-danger">*</span></label>
                            <div class="d-flex flex-column gap-2">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="role" id="roleAdmin" value="admin" {{ $user->hasRole('admin') ? 'checked' : '' }}>
                                    <label class="form-check-label d-flex align-items-center" for="roleAdmin">
                                        <span class="badge badge-natryon-blue rounded-pill px-3 py-2 me-2">
                                            <i class="bi bi-shield-lock-fill me-1"></i>Admin
                                        </span>
                                        <span class="text-natryon-grey">Full access to all features</span>
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="role" id="roleClient" value="client" {{ $user->hasRole('client') ? 'checked' : '' }}>
                                    <label class="form-check-label d-flex align-items-center" for="roleClient">
                                        <span class="badge badge-natryon-secondary rounded-pill px-3 py-2 me-2">
                                            <i class="bi bi-person-fill me-1"></i>Client
                                        </span>
                                        <span class="text-natryon-grey">Standard user access</span>
                                    </label>
                                </div>
                            </div>
                            @error('role')
                                <div class="text-danger mt-1">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="createAffiliate" name="create_affiliate" {{ $user->affiliateCodes->count() > 0 ? 'checked disabled' : '' }}>
                                <label class="form-check-label d-flex align-items-center" for="createAffiliate">
                                    <span class="badge badge-natryon-secondary rounded-pill px-3 py-2 me-2">
                                        <i class="bi bi-diagram-3 me-1"></i>Affiliate
                                    </span>
                                    <span class="text-natryon-grey">
                                        @if($user->affiliateCodes->count() > 0)
                                            User is already an affiliate
                                        @else
                                            Create affiliate code for this user
                                        @endif
                                    </span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="border-top pt-4 mt-4">
                    <h5 class="text-natryon-primary mb-3"><i class="bi bi-geo-alt me-2"></i>Address Information</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="address" class="form-label fw-medium">Address</label>
                                <input type="text" class="form-control @error('address') is-invalid @enderror" id="address" name="address" value="{{ old('address', $user->address) }}">
                                @error('address')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="city" class="form-label fw-medium">City</label>
                                <input type="text" class="form-control @error('city') is-invalid @enderror" id="city" name="city" value="{{ old('city', $user->city) }}">
                                @error('city')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="state" class="form-label fw-medium">State/Province</label>
                                <input type="text" class="form-control @error('state') is-invalid @enderror" id="state" name="state" value="{{ old('state', $user->state) }}">
                                @error('state')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="zip_code" class="form-label fw-medium">ZIP/Postal Code</label>
                                <input type="text" class="form-control @error('zip_code') is-invalid @enderror" id="zip_code" name="zip_code" value="{{ old('zip_code', $user->zip_code) }}">
                                @error('zip_code')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="country" class="form-label fw-medium">Country</label>
                                <input type="text" class="form-control @error('country') is-invalid @enderror" id="country" name="country" value="{{ old('country', $user->country) }}">
                                @error('country')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>

                <div class="d-flex justify-content-end mt-4">
                    <button type="button" class="btn btn-outline-secondary me-2" onclick="window.location.href='{{ route('admin.users.index') }}'">
                        <i class="bi bi-x-circle me-1"></i>Cancel
                    </button>
                    <button type="submit" class="btn btn-natryon-primary">
                        <i class="bi bi-save me-1"></i>Update User
                    </button>
                </div>
            </form>
        </div>
    </div>
@endsection
