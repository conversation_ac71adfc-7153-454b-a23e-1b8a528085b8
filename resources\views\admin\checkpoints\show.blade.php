@extends('layouts.admin')

@section('title', 'Checkpoint Details')

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Checkpoint: {{ $checkpoint->name }}</h1>
        <div>
            <a href="{{ route('admin.releases.show', $checkpoint->release_id) }}" class="btn btn-secondary">
                <i class="bi bi-arrow-left"></i> Back to Release
            </a>
            @if($checkpoint->release->status === 'draft' && $checkpoint->status === 'pending')
                <a href="{{ route('admin.checkpoints.edit', $checkpoint->id) }}" class="btn btn-primary">
                    <i class="bi bi-pencil"></i> Edit
                </a>
            @endif
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold">Checkpoint Information</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>Name:</strong>
                        <p>{{ $checkpoint->name }}</p>
                    </div>
                    <div class="mb-3">
                        <strong>Release:</strong>
                        <p>
                            <a href="{{ route('admin.releases.show', $checkpoint->release_id) }}">
                                {{ $checkpoint->release->version }} - {{ $checkpoint->release->name }}
                            </a>
                        </p>
                    </div>
                    <div class="mb-3">
                        <strong>Type:</strong>
                        <p>{{ ucwords(str_replace('_', ' ', $checkpoint->type)) }}</p>
                    </div>
                    <div class="mb-3">
                        <strong>Status:</strong>
                        <p>
                            @switch($checkpoint->status)
                                @case('pending')
                                    <span class="badge bg-secondary">Pending</span>
                                    @break
                                @case('in_progress')
                                    <span class="badge bg-info">In Progress</span>
                                    @break
                                @case('passed')
                                    <span class="badge bg-success">Passed</span>
                                    @break
                                @case('failed')
                                    <span class="badge bg-danger">Failed</span>
                                    @break
                                @default
                                    <span class="badge bg-secondary">{{ $checkpoint->status }}</span>
                            @endswitch
                        </p>
                    </div>
                    <div class="mb-3">
                        <strong>Description:</strong>
                        <p>{{ $checkpoint->description ?? 'No description provided.' }}</p>
                    </div>
                    @if($checkpoint->verified_by)
                        <div class="mb-3">
                            <strong>Verified By:</strong>
                            <p>{{ $checkpoint->verifier->name }}</p>
                        </div>
                    @endif
                    @if($checkpoint->verified_at)
                        <div class="mb-3">
                            <strong>Verified At:</strong>
                            <p>{{ $checkpoint->verified_at->format('Y-m-d H:i') }}</p>
                        </div>
                    @endif
                    @if($checkpoint->verification_notes)
                        <div class="mb-3">
                            <strong>Verification Notes:</strong>
                            <p>{{ $checkpoint->verification_notes }}</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold">Actions</h6>
                </div>
                <div class="card-body">
                    @if($checkpoint->release->status === 'draft' && $checkpoint->status === 'pending')
                        <form action="{{ route('admin.checkpoints.destroy', $checkpoint->id) }}" method="POST" class="mb-2">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-danger btn-block" onclick="return confirm('Are you sure you want to delete this checkpoint?')">
                                <i class="bi bi-trash"></i> Delete Checkpoint
                            </button>
                        </form>
                    @endif

                    @if($checkpoint->release->status === 'testing' && $checkpoint->status === 'pending')
                        <form action="{{ route('admin.checkpoints.start-verification', $checkpoint->id) }}" method="POST" class="mb-2">
                            @csrf
                            <button type="submit" class="btn btn-warning btn-block">
                                <i class="bi bi-play"></i> Start Verification
                            </button>
                        </form>
                    @endif

                    @if($checkpoint->release->status === 'testing' && ($checkpoint->status === 'in_progress' || $checkpoint->status === 'pending'))
                        <button type="button" class="btn btn-success btn-block mb-2" data-bs-toggle="modal" data-bs-target="#passCheckpointModal">
                            <i class="bi bi-check"></i> Mark as Passed
                        </button>
                        <button type="button" class="btn btn-danger btn-block" data-bs-toggle="modal" data-bs-target="#failCheckpointModal">
                            <i class="bi bi-x"></i> Mark as Failed
                        </button>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Pass Checkpoint Modal -->
<div class="modal fade" id="passCheckpointModal" tabindex="-1" aria-labelledby="passCheckpointModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form action="{{ route('admin.checkpoints.pass', $checkpoint->id) }}" method="POST">
                @csrf
                <div class="modal-header">
                    <h5 class="modal-title" id="passCheckpointModalLabel">Pass Checkpoint: {{ $checkpoint->name }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="verification_notes" class="form-label">Verification Notes</label>
                        <textarea class="form-control" id="verification_notes" name="verification_notes" rows="3" placeholder="Enter any notes about the verification process"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">Mark as Passed</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Fail Checkpoint Modal -->
<div class="modal fade" id="failCheckpointModal" tabindex="-1" aria-labelledby="failCheckpointModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form action="{{ route('admin.checkpoints.fail', $checkpoint->id) }}" method="POST">
                @csrf
                <div class="modal-header">
                    <h5 class="modal-title" id="failCheckpointModalLabel">Fail Checkpoint: {{ $checkpoint->name }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="verification_notes" class="form-label">Failure Reason</label>
                        <textarea class="form-control" id="verification_notes" name="verification_notes" rows="3" placeholder="Enter the reason for failure" required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">Mark as Failed</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection
