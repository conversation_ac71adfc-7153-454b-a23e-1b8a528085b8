<?php

namespace App\Http\Controllers;

use App\Models\Category;
use App\Models\Product;
use Artesaos\SEOTools\Facades\SEOMeta;
use Artesaos\SEOTools\Facades\OpenGraph;
use Illuminate\Http\Request;

class ProductController extends Controller
{
    /**
     * Display a listing of the products.
     */
    public function index(Request $request)
    {
        // Set SEO metadata
        SEOMeta::setTitle('Products - NatRyon');
        SEOMeta::setDescription('Browse our premium greens powder supplements for optimal health and nutrition.');
        SEOMeta::setCanonical(url('/products'));

        OpenGraph::setTitle('Products - NatRyon');
        OpenGraph::setDescription('Browse our premium greens powder supplements for optimal health and nutrition.');
        OpenGraph::setUrl(url('/products'));
        OpenGraph::addProperty('type', 'website');

        // Get categories for filtering
        $categories = Category::where('is_active', true)->get();

        // Get products with optional filtering
        $query = Product::where('is_active', true);

        // Filter by category if provided
        if ($request->has('category')) {
            $query->where('category_id', $request->category);
        }

        // Sort products
        $sort = $request->input('sort', 'newest');
        switch ($sort) {
            case 'price_low':
                $query->orderBy('price', 'asc');
                break;
            case 'price_high':
                $query->orderBy('price', 'desc');
                break;
            case 'name':
                $query->orderBy('name', 'asc');
                break;
            case 'newest':
            default:
                $query->orderBy('created_at', 'desc');
                break;
        }

        $products = $query->paginate(12);

        return view('products.index', compact('products', 'categories', 'sort'));
    }

    /**
     * Display the specified product.
     */
    public function show(string $slug)
    {
        $product = Product::where('slug', $slug)
            ->where('is_active', true)
            ->firstOrFail();

        // Set SEO metadata
        SEOMeta::setTitle($product->name . ' - NatRyon');
        SEOMeta::setDescription(substr(strip_tags($product->description), 0, 160));
        SEOMeta::setCanonical(url('/products/' . $product->slug));

        OpenGraph::setTitle($product->name . ' - NatRyon');
        OpenGraph::setDescription(substr(strip_tags($product->description), 0, 160));
        OpenGraph::setUrl(url('/products/' . $product->slug));
        OpenGraph::addProperty('type', 'product');
        if ($product->image) {
            OpenGraph::addImage(asset('storage/' . $product->image));
        }

        // Get related products
        $relatedProducts = Product::where('category_id', $product->category_id)
            ->where('id', '!=', $product->id)
            ->where('is_active', true)
            ->take(4)
            ->get();

        return view('products.show', compact('product', 'relatedProducts'));
    }

    /**
     * Add a product to the cart.
     */
    public function addToCart(Request $request)
    {
        $request->validate([
            'product_id' => 'required|exists:products,id',
            'quantity' => 'required|integer|min:1',
            'is_subscription' => 'nullable|boolean',
        ]);

        $product = Product::findOrFail($request->product_id);

        // Check if the product is available
        if (!$product->is_active || $product->stock < $request->quantity) {
            return back()->with('error', 'Sorry, this product is not available in the requested quantity.');
        }

        // Check if subscription is allowed for this product
        if ($request->is_subscription && !$product->allow_subscription) {
            return back()->with('error', 'This product does not allow subscriptions.');
        }

        // Check if product is subscription-only but not being purchased as a subscription
        if ($product->subscription_only && !$request->is_subscription) {
            return back()->with('error', 'This product is only available with a subscription.');
        }

        // Get the cart from the session
        $cart = session()->get('cart', []);

        // Generate a unique cart item ID
        $cartItemId = $product->id . '-' . ($request->is_subscription ? 'sub' : 'one');

        // Check if the product is already in the cart
        if (isset($cart[$cartItemId])) {
            // Update the quantity
            $cart[$cartItemId]['quantity'] += $request->quantity;
        } else {
            // Add the product to the cart
            $cart[$cartItemId] = [
                'id' => $product->id,
                'name' => $product->name,
                'slug' => $product->slug,
                'price' => $request->is_subscription ? $product->subscription_price : $product->price,
                'quantity' => $request->quantity,
                'image' => $product->image,
                'is_subscription' => $request->is_subscription ? true : false,
            ];
        }

        // Save the cart back to the session
        session()->put('cart', $cart);

        return redirect()->route('cart.index')->with('success', 'Product added to cart successfully!');
    }

    /**
     * Remove a product from the cart.
     */
    public function removeFromCart(Request $request)
    {
        $cartItemId = $request->cart_item_id;

        // Get the cart from the session
        $cart = session()->get('cart', []);

        // Remove the product from the cart
        if (isset($cart[$cartItemId])) {
            unset($cart[$cartItemId]);
            session()->put('cart', $cart);
            return back()->with('success', 'Product removed from cart successfully!');
        }

        return back()->with('error', 'Product not found in cart.');
    }

    /**
     * Update the cart.
     */
    public function updateCart(Request $request)
    {
        $request->validate([
            'cart_item_id' => 'required|string',
            'quantity' => 'required|integer|min:1',
        ]);

        // Get the cart from the session
        $cart = session()->get('cart', []);

        // Update the quantity
        if (isset($cart[$request->cart_item_id])) {
            $cart[$request->cart_item_id]['quantity'] = $request->quantity;
            session()->put('cart', $cart);
            return back()->with('success', 'Cart updated successfully!');
        }

        return back()->with('error', 'Product not found in cart.');
    }
}
