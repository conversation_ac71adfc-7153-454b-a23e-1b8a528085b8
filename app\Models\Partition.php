<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Partition extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'release_id',
        'name',
        'description',
        'file_paths',
        'type',
        'deployment_order',
        'status',
        'deployment_notes',
        'deployed_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'file_paths' => 'array',
        'deployed_at' => 'datetime',
    ];

    /**
     * Get the release that owns the partition.
     */
    public function release(): BelongsTo
    {
        return $this->belongsTo(Release::class);
    }

    /**
     * Mark the partition as deployed.
     */
    public function markAsDeployed(?string $notes = null): void
    {
        $this->status = 'deployed';
        $this->deployment_notes = $notes;
        $this->deployed_at = now();
        $this->save();
    }

    /**
     * Mark the partition as failed.
     */
    public function markAsFailed(?string $notes = null): void
    {
        $this->status = 'failed';
        $this->deployment_notes = $notes;
        $this->save();
    }

    /**
     * Mark the partition as rolled back.
     */
    public function markAsRolledBack(?string $notes = null): void
    {
        $this->status = 'rolled_back';
        $this->deployment_notes = $notes;
        $this->save();
    }

    /**
     * Check if the partition is deployed.
     */
    public function isDeployed(): bool
    {
        return $this->status === 'deployed';
    }

    /**
     * Check if the partition is failed.
     */
    public function isFailed(): bool
    {
        return $this->status === 'failed';
    }

    /**
     * Check if the partition is rolled back.
     */
    public function isRolledBack(): bool
    {
        return $this->status === 'rolled_back';
    }

    /**
     * Check if the partition is pending.
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Add a file path to the partition.
     */
    public function addFilePath(string $path): void
    {
        $paths = $this->file_paths ?? [];
        if (!in_array($path, $paths)) {
            $paths[] = $path;
            $this->file_paths = $paths;
            $this->save();
        }
    }

    /**
     * Remove a file path from the partition.
     */
    public function removeFilePath(string $path): void
    {
        $paths = $this->file_paths ?? [];
        $key = array_search($path, $paths);
        if ($key !== false) {
            unset($paths[$key]);
            $this->file_paths = array_values($paths);
            $this->save();
        }
    }
}
