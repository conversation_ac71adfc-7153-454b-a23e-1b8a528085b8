@extends('layouts.admin')

@section('title', 'Manage Users')

@section('content')
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-natryon-primary">
            <i class="bi bi-people me-2"></i>Users
        </h1>
        <a href="{{ route('admin.users.create') }}" class="btn btn-natryon-primary">
            <i class="bi bi-person-plus me-1"></i> Add New User
        </a>
    </div>

    <div class="card card-natryon">
        <div class="card-header bg-natryon-primary text-white py-3">
            <h5 class="mb-0 fw-bold"><i class="bi bi-person-lines-fill me-2"></i>User Management</h5>
        </div>
        <div class="card-body">
            <div class="alert alert-natryon mb-4">
                <div class="d-flex">
                    <div class="me-3">
                        <i class="bi bi-info-circle-fill fs-4 text-natryon-primary"></i>
                    </div>
                    <div>
                        <h5 class="alert-heading text-natryon-primary">User Management</h5>
                        <p class="mb-0">Manage your users, assign roles, and track their activity. You can filter users by role or affiliate status.</p>
                    </div>
                </div>
            </div>

            <div class="d-flex justify-content-between align-items-center mb-3">
                <form action="{{ route('admin.users.index') }}" method="GET" class="d-flex">
                    <div class="input-group">
                        <input type="text" name="search" class="form-control" placeholder="Search users..." value="{{ request('search') }}">
                        <button type="submit" class="btn btn-outline-natryon-primary">
                            <i class="bi bi-search"></i>
                        </button>
                    </div>
                </form>
                <div class="d-flex">
                    <div class="dropdown me-2">
                        <button class="btn btn-outline-natryon-primary dropdown-toggle" type="button" id="filterDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi bi-funnel me-1"></i> Filter
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="filterDropdown">
                            <li><h6 class="dropdown-header">User Role</h6></li>
                            <li><a class="dropdown-item" href="{{ route('admin.users.index', ['role' => 'admin']) }}">
                                <i class="bi bi-shield-lock me-2 text-natryon-blue"></i>Admins
                            </a></li>
                            <li><a class="dropdown-item" href="{{ route('admin.users.index', ['role' => 'client']) }}">
                                <i class="bi bi-person me-2 text-natryon-secondary"></i>Clients
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><h6 class="dropdown-header">Affiliate Status</h6></li>
                            <li><a class="dropdown-item" href="{{ route('admin.users.index', ['affiliate' => 'yes']) }}">
                                <i class="bi bi-diagram-3 me-2 text-natryon-secondary"></i>Affiliates
                            </a></li>
                            <li><a class="dropdown-item" href="{{ route('admin.users.index', ['affiliate' => 'no']) }}">
                                <i class="bi bi-person-badge me-2 text-natryon-grey"></i>Non-Affiliates
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ route('admin.users.index') }}">
                                <i class="bi bi-x-circle me-2"></i>Clear Filters
                            </a></li>
                        </ul>
                    </div>
                    <div class="dropdown">
                        <button class="btn btn-outline-natryon-secondary dropdown-toggle" type="button" id="sortDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi bi-sort-down me-1"></i> Sort
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="sortDropdown">
                            <li><a class="dropdown-item" href="{{ route('admin.users.index', ['sort' => 'name_asc']) }}">
                                <i class="bi bi-sort-alpha-down me-2"></i>Name (A-Z)
                            </a></li>
                            <li><a class="dropdown-item" href="{{ route('admin.users.index', ['sort' => 'name_desc']) }}">
                                <i class="bi bi-sort-alpha-up me-2"></i>Name (Z-A)
                            </a></li>
                            <li><a class="dropdown-item" href="{{ route('admin.users.index', ['sort' => 'newest']) }}">
                                <i class="bi bi-calendar-check me-2"></i>Newest
                            </a></li>
                            <li><a class="dropdown-item" href="{{ route('admin.users.index', ['sort' => 'oldest']) }}">
                                <i class="bi bi-calendar me-2"></i>Oldest
                            </a></li>
                            <li><a class="dropdown-item" href="{{ route('admin.users.index', ['sort' => 'orders_high']) }}">
                                <i class="bi bi-arrow-down me-2"></i>Most Orders
                            </a></li>
                            <li><a class="dropdown-item" href="{{ route('admin.users.index', ['sort' => 'orders_low']) }}">
                                <i class="bi bi-arrow-up me-2"></i>Least Orders
                            </a></li>
                        </ul>
                    </div>
                </div>
            </div>

            @if(isset($users) && count($users) > 0)
                <div class="table-responsive">
                    <table class="table table-natryon table-hover align-middle">
                        <thead>
                            <tr>
                                <th style="width: 60px;">ID</th>
                                <th>Name</th>
                                <th>Email</th>
                                <th>Role</th>
                                <th>Orders</th>
                                <th>Registered</th>
                                <th style="width: 150px;">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($users as $user)
                                <tr>
                                    <td>
                                        <span class="badge badge-natryon-grey rounded-pill">{{ $user->id }}</span>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-natryon me-2">
                                                {{ strtoupper(substr($user->name, 0, 1)) }}
                                            </div>
                                            <span class="fw-medium text-natryon-primary">{{ $user->name }}</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="bi bi-envelope me-2 text-natryon-grey"></i>
                                            {{ $user->email }}
                                        </div>
                                    </td>
                                    <td>
                                        @if($user->hasRole('admin'))
                                            <span class="badge badge-natryon-blue rounded-pill px-3 py-2">
                                                <i class="bi bi-shield-lock-fill me-1"></i>Admin
                                            </span>
                                        @elseif($user->hasRole('client'))
                                            <span class="badge badge-natryon-secondary rounded-pill px-3 py-2">
                                                <i class="bi bi-person-fill me-1"></i>Client
                                            </span>
                                        @else
                                            <span class="badge badge-natryon-grey rounded-pill px-3 py-2">
                                                <i class="bi bi-question-circle-fill me-1"></i>No Role
                                            </span>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="bi bi-cart me-2 text-natryon-grey"></i>
                                            <span class="fw-medium">{{ $user->orders->count() }}</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="bi bi-calendar-date me-2 text-natryon-grey"></i>
                                            {{ $user->created_at->format('M d, Y') }}
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex gap-1">
                                            <a href="{{ route('admin.users.show', $user->id) }}" class="btn btn-sm btn-outline-natryon-blue rounded-circle" title="View User">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                            <a href="{{ route('admin.users.edit', $user->id) }}" class="btn btn-sm btn-outline-natryon-primary rounded-circle" title="Edit User">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-outline-danger rounded-circle" data-bs-toggle="modal" data-bs-target="#deleteModal{{ $user->id }}" title="Delete User">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </div>

                                        <!-- Delete Modal -->
                                        <div class="modal fade" id="deleteModal{{ $user->id }}" tabindex="-1" aria-labelledby="deleteModalLabel{{ $user->id }}" aria-hidden="true">
                                            <div class="modal-dialog">
                                                <div class="modal-content">
                                                    <div class="modal-header bg-natryon-primary text-white">
                                                        <h5 class="modal-title" id="deleteModalLabel{{ $user->id }}">
                                                            <i class="bi bi-exclamation-triangle-fill me-2"></i>Confirm Delete
                                                        </h5>
                                                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                                                    </div>
                                                    <div class="modal-body">
                                                        <p class="mb-0">Are you sure you want to delete <strong class="text-natryon-primary">{{ $user->name }}</strong>?</p>
                                                        <p class="text-danger mb-0"><small><i class="bi bi-exclamation-circle me-1"></i>This action cannot be undone and will remove all associated data.</small></p>
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                                                            <i class="bi bi-x-circle me-1"></i>Cancel
                                                        </button>
                                                        <form action="{{ route('admin.users.destroy', $user->id) }}" method="POST">
                                                            @csrf
                                                            @method('DELETE')
                                                            <button type="submit" class="btn btn-danger delete-confirm" data-name="{{ $user->name }}">
                                                                <i class="bi bi-trash me-1"></i>Delete
                                                            </button>
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <div class="d-flex justify-content-between align-items-center mt-4">
                    <div class="text-natryon-grey">
                        <i class="bi bi-info-circle me-1"></i> Showing {{ $users->firstItem() ?? 0 }} to {{ $users->lastItem() ?? 0 }} of {{ $users->total() ?? 0 }} users
                    </div>
                    <div>
                        {{ $users->withQueryString()->links() }}
                    </div>
                </div>
            @else
                <div class="text-center py-5">
                    <div class="mb-4">
                        <span class="d-inline-block p-3 bg-natryon-grey-1 bg-opacity-25 rounded-circle">
                            <i class="bi bi-people text-natryon-grey" style="font-size: 3rem;"></i>
                        </span>
                    </div>
                    <h4 class="text-natryon-primary mb-3">No users found</h4>
                    <p class="text-natryon-grey mb-4">
                        @if(request('search'))
                            No users match your search criteria. <a href="{{ route('admin.users.index') }}" class="text-natryon-secondary">Clear search</a>
                        @elseif(request('role'))
                            No users with role "{{ request('role') }}". <a href="{{ route('admin.users.index') }}" class="text-natryon-secondary">View all users</a>
                        @elseif(request('affiliate'))
                            No {{ request('affiliate') == 'yes' ? 'affiliate' : 'non-affiliate' }} users found. <a href="{{ route('admin.users.index') }}" class="text-natryon-secondary">View all users</a>
                        @else
                            There are no users in the system yet. Start by adding your first user.
                        @endif
                    </p>
                    <a href="{{ route('admin.users.create') }}" class="btn btn-natryon-primary mt-3">
                        <i class="bi bi-person-plus me-1"></i> Add New User
                    </a>
                </div>
            @endif
        </div>
    </div>
@endsection
