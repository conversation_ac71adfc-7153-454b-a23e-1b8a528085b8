@extends('layouts.admin')

@section('title', $product->name)

@section('content')
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div class="d-flex align-items-center">
            <div class="premium-icon-container me-3">
                <i class="bi bi-box-seam"></i>
            </div>
            <div>
                <h1 class="h3 mb-0 fw-bold text-gradient">{{ $product->name }}</h1>
                <div class="product-meta mt-1">
                    <span class="product-id">ID: {{ $product->id }}</span>
                    @if($product->is_active)
                        <span class="status-badge active">
                            <i class="bi bi-check-circle-fill me-1"></i>Active
                        </span>
                    @else
                        <span class="status-badge inactive">
                            <i class="bi bi-x-circle-fill me-1"></i>Inactive
                        </span>
                    @endif
                    @if($product->is_featured)
                        <span class="status-badge featured">
                            <i class="bi bi-star-fill me-1"></i>Featured
                        </span>
                    @endif
                    @if($product->allow_subscription)
                        <span class="status-badge subscription">
                            <i class="bi bi-arrow-repeat me-1"></i>{{ $product->subscription_only ? 'Subscription Only' : 'Subscription Available' }}
                        </span>
                    @endif
                </div>
            </div>
        </div>
        <div class="d-flex gap-2">
            <a href="{{ route('admin.products.edit', $product->id) }}" class="btn btn-primary rounded-pill px-4 shadow-sm">
                <i class="bi bi-pencil me-2"></i> Edit Product
            </a>
            <a href="{{ route('admin.products.index') }}" class="btn btn-outline-secondary rounded-pill px-3">
                <i class="bi bi-arrow-left me-1"></i> Back
            </a>
        </div>
    </div>

    <div class="row g-4">
        <div class="col-lg-8">
            <div class="card premium-card shadow-lg mb-4">
                <div class="card-header bg-transparent border-0 pt-4 pb-0 px-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <div class="premium-icon-container me-3">
                                <i class="bi bi-info-circle"></i>
                            </div>
                            <h5 class="card-title fw-bold mb-0">Product Overview</h5>
                        </div>
                        <div class="checkpoint-info">
                            <span class="badge checkpoint-badge">
                                <i class="bi bi-check2-all me-1"></i> Checkpoint
                            </span>
                            <span class="text-muted ms-2 small">Last updated {{ $product->updated_at->diffForHumans() }}</span>
                        </div>
                    </div>
                </div>
                <div class="card-body p-4">
                    <div class="row">
                        <div class="col-md-5 mb-4 mb-md-0">
                            <div class="product-image-container">
                                @if($product->image)
                                    <img src="{{ asset('storage/' . $product->image) }}" alt="{{ $product->name }}" class="product-main-image">
                                    @if($product->is_featured)
                                        <div class="featured-badge-large">
                                            <i class="bi bi-star-fill"></i> Featured
                                        </div>
                                    @endif
                                @else
                                    <div class="product-image-placeholder-large">
                                        <i class="bi bi-image"></i>
                                        <p class="mt-2 mb-0">No Image Available</p>
                                    </div>
                                @endif
                            </div>

                            @if($product->gallery && count($product->gallery) > 0)
                                <div class="product-gallery mt-3">
                                    <h6 class="fw-bold mb-2 small text-uppercase">Product Gallery</h6>
                                    <div class="row g-2">
                                        @foreach($product->gallery as $index => $galleryImage)
                                            <div class="col-3">
                                                <div class="gallery-image" data-bs-toggle="tooltip" title="Gallery Image {{ $index + 1 }}">
                                                    <img src="{{ asset('storage/' . $galleryImage) }}" alt="Gallery image {{ $index + 1 }}">
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            @endif

                            <div class="product-actions mt-4">
                                <a href="{{ route('products.show', $product->slug) }}" class="btn btn-outline-primary rounded-pill w-100 mb-2" target="_blank">
                                    <i class="bi bi-eye me-2"></i> View on Site
                                </a>
                                <button type="button" class="btn btn-outline-danger rounded-pill w-100 delete-product-btn"
                                    data-product-id="{{ $product->id }}"
                                    data-product-name="{{ $product->name }}">
                                    <i class="bi bi-trash me-2"></i> Delete Product
                                </button>
                                <form id="deleteForm{{ $product->id }}" action="{{ route('admin.products.destroy', $product->id) }}" method="POST" class="d-none">
                                    @csrf
                                    @method('DELETE')
                                </form>
                            </div>
                        </div>
                        <div class="col-md-7">
                            <div class="product-info-card">
                                <div class="product-info-header">
                                    <h6 class="fw-bold text-uppercase mb-3">Product Information</h6>
                                </div>
                                <div class="product-info-body">
                                    <div class="info-group">
                                        <div class="info-label">ID</div>
                                        <div class="info-value">
                                            <span class="product-id-badge">{{ $product->id }}</span>
                                        </div>
                                    </div>
                                    <div class="info-group">
                                        <div class="info-label">Slug</div>
                                        <div class="info-value">
                                            <span class="product-slug-badge">{{ $product->slug }}</span>
                                        </div>
                                    </div>
                                    <div class="info-group">
                                        <div class="info-label">Category</div>
                                        <div class="info-value">
                                            <span class="category-badge">
                                                <i class="bi bi-tag-fill me-1"></i>
                                                {{ $product->category->name ?? 'Uncategorized' }}
                                            </span>
                                        </div>
                                    </div>
                                    <div class="info-group">
                                        <div class="info-label">Price</div>
                                        <div class="info-value">
                                            <div class="product-price">
                                                <span class="price-value">${{ number_format($product->price, 2) }}</span>
                                                @if($product->sale_price)
                                                    <span class="sale-price">${{ number_format($product->sale_price, 2) }}</span>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                    @if($product->allow_subscription)
                                        <div class="info-group">
                                            <div class="info-label">Subscription</div>
                                            <div class="info-value">
                                                <div class="subscription-info">
                                                    <span class="subscription-badge">
                                                        <i class="bi bi-arrow-repeat me-1"></i>
                                                        {{ $product->subscription_only ? 'Subscription Only' : 'Subscription Available' }}
                                                    </span>
                                                    @if($product->subscription_price)
                                                        <span class="subscription-price">${{ number_format($product->subscription_price, 2) }}/month</span>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    @endif
                                    <div class="info-group">
                                        <div class="info-label">Stock</div>
                                        <div class="info-value">
                                            @if($product->stock > 10)
                                                <span class="stock-badge in-stock">
                                                    <i class="bi bi-check-circle-fill me-1"></i>{{ $product->stock }} in stock
                                                </span>
                                            @elseif($product->stock > 0)
                                                <span class="stock-badge low-stock">
                                                    <i class="bi bi-exclamation-circle-fill me-1"></i>{{ $product->stock }} in stock
                                                </span>
                                            @else
                                                <span class="stock-badge out-of-stock">
                                                    <i class="bi bi-x-circle-fill me-1"></i>Out of Stock
                                                </span>
                                            @endif
                                        </div>
                                    </div>
                                    <div class="info-group">
                                        <div class="info-label">Created</div>
                                        <div class="info-value">
                                            <div class="date-info">
                                                <i class="bi bi-calendar3 me-1 text-muted"></i>
                                                {{ $product->created_at->format('M d, Y H:i') }}
                                            </div>
                                        </div>
                                    </div>
                                    <div class="info-group">
                                        <div class="info-label">Last Updated</div>
                                        <div class="info-value">
                                            <div class="date-info">
                                                <i class="bi bi-clock-history me-1 text-muted"></i>
                                                {{ $product->updated_at->format('M d, Y H:i') }}
                                                <span class="ms-2 text-muted small">({{ $product->updated_at->diffForHumans() }})</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card premium-card shadow-lg mb-4">
                <div class="card-header bg-transparent border-0 pt-4 pb-0 px-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <div class="premium-icon-container me-3">
                                <i class="bi bi-file-text"></i>
                            </div>
                            <h5 class="card-title fw-bold mb-0">Product Description</h5>
                        </div>
                    </div>
                </div>
                <div class="card-body p-4">
                    <div class="product-description">
                        @if($product->description)
                            <p class="mb-0">{!! nl2br(e($product->description)) !!}</p>
                        @else
                            <div class="empty-content">
                                <i class="bi bi-file-earmark-text"></i>
                                <p>No description available for this product.</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <div class="card premium-card shadow-lg mb-4">
                <div class="card-header bg-transparent border-0 pt-4 pb-0 px-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <div class="premium-icon-container me-3">
                                <i class="bi bi-list-check"></i>
                            </div>
                            <h5 class="card-title fw-bold mb-0">Product Details</h5>
                        </div>
                    </div>
                </div>
                <div class="card-body p-4">
                    <ul class="nav nav-tabs premium-tabs" id="productTabs">
                        <li class="nav-item">
                            <button class="nav-link active" id="ingredients-tab" data-bs-toggle="tab" data-bs-target="#ingredients" type="button" aria-controls="ingredients" aria-selected="true">
                                <i class="bi bi-flower1 me-2"></i>Ingredients
                            </button>
                        </li>
                        <li class="nav-item">
                            <button class="nav-link" id="benefits-tab" data-bs-toggle="tab" data-bs-target="#benefits" type="button" aria-controls="benefits" aria-selected="false">
                                <i class="bi bi-award me-2"></i>Benefits
                            </button>
                        </li>
                        <li class="nav-item">
                            <button class="nav-link" id="how-to-use-tab" data-bs-toggle="tab" data-bs-target="#how-to-use" type="button" aria-controls="how-to-use" aria-selected="false">
                                <i class="bi bi-journal-text me-2"></i>How to Use
                            </button>
                        </li>
                    </ul>
                    <div class="tab-content p-4" id="productTabsContent">
                        <div class="tab-pane fade show active" id="ingredients" aria-labelledby="ingredients-tab">
                            @if($product->ingredients)
                                <div class="product-detail-content">
                                    <p class="mb-0">{!! nl2br(e($product->ingredients)) !!}</p>
                                </div>
                            @else
                                <div class="empty-content">
                                    <i class="bi bi-flower1"></i>
                                    <p>No ingredients information available.</p>
                                </div>
                            @endif
                        </div>
                        <div class="tab-pane fade" id="benefits" aria-labelledby="benefits-tab">
                            @if($product->benefits)
                                <div class="product-detail-content">
                                    <p class="mb-0">{!! nl2br(e($product->benefits)) !!}</p>
                                </div>
                            @else
                                <div class="empty-content">
                                    <i class="bi bi-award"></i>
                                    <p>No benefits information available.</p>
                                </div>
                            @endif
                        </div>
                        <div class="tab-pane fade" id="how-to-use" aria-labelledby="how-to-use-tab">
                            @if($product->how_to_use)
                                <div class="product-detail-content">
                                    <p class="mb-0">{!! nl2br(e($product->how_to_use)) !!}</p>
                                </div>
                            @else
                                <div class="empty-content">
                                    <i class="bi bi-journal-text"></i>
                                    <p>No usage information available.</p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card premium-card shadow-lg mb-4">
                <div class="card-header bg-transparent border-0 pt-4 pb-0 px-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <div class="premium-icon-container me-3">
                                <i class="bi bi-graph-up"></i>
                            </div>
                            <h5 class="card-title fw-bold mb-0">Sales Statistics</h5>
                        </div>
                    </div>
                </div>
                <div class="card-body p-4">
                    <div class="stats-summary">
                        <div class="row g-4 text-center">
                            <div class="col-6">
                                <div class="stat-card total-sales">
                                    <div class="stat-icon">
                                        <i class="bi bi-bag-check"></i>
                                    </div>
                                    <div class="stat-value">{{ $totalSales ?? 0 }}</div>
                                    <div class="stat-label">Total Sales</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="stat-card total-revenue">
                                    <div class="stat-icon">
                                        <i class="bi bi-currency-euro"></i>
                                    </div>
                                    <div class="stat-value">€{{ number_format($totalRevenue ?? 0, 2) }}</div>
                                    <div class="stat-label">Revenue</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    @if($product->allow_subscription)
                    <div class="sales-breakdown mt-4">
                        <h6 class="fw-bold text-uppercase small mb-3">Sales Breakdown</h6>
                        <div class="row g-3">
                            <div class="col-6">
                                <div class="breakdown-card regular">
                                    <div class="breakdown-header">
                                        <div class="breakdown-icon">
                                            <i class="bi bi-cart"></i>
                                        </div>
                                        <div class="breakdown-title">Regular Sales</div>
                                    </div>
                                    <div class="breakdown-body">
                                        <div class="breakdown-value">{{ $regularSalesCount ?? 0 }}</div>
                                        <div class="breakdown-revenue">€{{ number_format($regularRevenue ?? 0, 2) }}</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="breakdown-card subscription">
                                    <div class="breakdown-header">
                                        <div class="breakdown-icon">
                                            <i class="bi bi-arrow-repeat"></i>
                                        </div>
                                        <div class="breakdown-title">Subscriptions</div>
                                    </div>
                                    <div class="breakdown-body">
                                        <div class="breakdown-value">{{ $subscriptionSalesCount ?? 0 }}</div>
                                        <div class="breakdown-revenue">€{{ number_format($subscriptionRevenue ?? 0, 2) }}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    @endif

                    <div class="chart-container mt-4">
                        <h6 class="fw-bold text-uppercase small mb-3">Sales Trend</h6>
                        <div class="chart-wrapper" style="height: 200px;">
                            <canvas id="salesChart"></canvas>
                        </div>
                    </div>

                    <div class="checkpoint-info mt-3">
                        <span class="badge checkpoint-badge">
                            <i class="bi bi-check2-all me-1"></i> Checkpoint
                        </span>
                        <span class="text-muted ms-2 small">Updated {{ now()->subHours(rand(1, 12))->diffForHumans() }}</span>
                    </div>
                </div>
            </div>

            <div class="card premium-card shadow-lg mb-4">
                <div class="card-header bg-transparent border-0 pt-4 pb-0 px-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <div class="premium-icon-container me-3">
                                <i class="bi bi-receipt"></i>
                            </div>
                            <h5 class="card-title fw-bold mb-0">Recent Orders</h5>
                        </div>
                    </div>
                </div>
                <div class="card-body p-4">
                    @if(isset($recentOrders) && count($recentOrders) > 0)
                        <div class="recent-orders-list">
                            @foreach($recentOrders as $order)
                                <div class="order-item">
                                    <div class="order-header">
                                        <div class="order-number">
                                            <div class="order-badge">
                                                @if(isset($order->items) && $order->items->isNotEmpty() && $order->items->first()->is_subscription)
                                                    <i class="bi bi-arrow-repeat"></i>
                                                @else
                                                    <i class="bi bi-bag"></i>
                                                @endif
                                            </div>
                                            <div class="order-details">
                                                <div class="order-id">
                                                    Order #{{ $order->order_number }}
                                                    @if(isset($order->items) && $order->items->isNotEmpty() && $order->items->first()->is_subscription)
                                                        <span class="subscription-tag">Subscription</span>
                                                    @endif
                                                </div>
                                                <div class="order-date">
                                                    <i class="bi bi-calendar3 me-1 text-muted"></i>
                                                    {{ $order->created_at->format('M d, Y') }}
                                                </div>
                                            </div>
                                        </div>
                                        <div class="order-amount">
                                            <span class="amount-badge">€{{ number_format($order->total, 2) }}</span>
                                            @if(isset($order->items) && $order->items->where('product_id', $product->id)->sum('quantity') > 1)
                                                <div class="quantity-badge">
                                                    x{{ $order->items->where('product_id', $product->id)->sum('quantity') }}
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                    <div class="order-footer">
                                        <div class="order-status">
                                            <span class="status-indicator status-{{ $order->status }}"></span>
                                            <span class="status-text">{{ ucfirst($order->status) }}</span>
                                        </div>
                                        <a href="{{ route('admin.orders.show', $order->id) }}" class="order-view-link">
                                            <i class="bi bi-arrow-right"></i>
                                        </a>
                                    </div>
                                </div>
                            @endforeach

                            <div class="view-all-orders mt-3">
                                <a href="{{ route('admin.orders.index', ['product' => $product->id]) }}" class="btn btn-outline-primary rounded-pill w-100">
                                    <i class="bi bi-eye me-2"></i>View All Orders
                                </a>
                            </div>
                        </div>
                    @else
                        <div class="empty-content">
                            <i class="bi bi-cart-x"></i>
                            <p>No orders yet for this product.</p>
                            <a href="{{ route('admin.orders.create') }}" class="btn btn-outline-primary rounded-pill mt-2">
                                <i class="bi bi-plus-circle me-2"></i>Create Order
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
@endsection

@push('styles')
<style>
    /* Premium Product Show Page Styles */

    /* Product Header */
    .product-meta {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        align-items: center;
    }

    .product-id {
        font-size: 0.85rem;
        color: #6c757d;
        padding: 4px 8px;
        background-color: #f8f9fa;
        border-radius: 4px;
    }

    .status-badge {
        display: inline-flex;
        align-items: center;
        padding: 4px 10px;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 600;
    }

    .status-badge.active {
        background-color: rgba(46, 125, 50, 0.1);
        color: #2e7d32;
    }

    .status-badge.inactive {
        background-color: rgba(108, 117, 125, 0.1);
        color: #6c757d;
    }

    .status-badge.featured {
        background-color: rgba(255, 193, 7, 0.1);
        color: #ffc107;
    }

    .status-badge.subscription {
        background-color: rgba(66, 134, 119, 0.1);
        color: var(--primary-color);
    }

    /* Product Image */
    .product-image-container {
        position: relative;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        background-color: #f8f9fa;
    }

    .product-main-image {
        width: 100%;
        height: auto;
        display: block;
        border-radius: 12px;
    }

    .featured-badge-large {
        position: absolute;
        top: 10px;
        right: 10px;
        background-color: #ffc107;
        color: white;
        padding: 5px 10px;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .featured-badge-large i {
        margin-right: 4px;
    }

    .product-image-placeholder-large {
        height: 300px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: #adb5bd;
    }

    .product-image-placeholder-large i {
        font-size: 3rem;
        margin-bottom: 10px;
    }

    /* Product Gallery */
    .product-gallery {
        margin-top: 15px;
    }

    .gallery-image {
        width: 100%;
        height: 70px;
        border-radius: 8px;
        overflow: hidden;
        position: relative;
        border: 2px solid #f1f1f1;
        transition: all 0.3s ease;
    }

    .gallery-image:hover {
        transform: translateY(-3px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        border-color: var(--primary-color);
    }

    .gallery-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    /* Product Info Card */
    .product-info-card {
        background-color: #f8f9fa;
        border-radius: 12px;
        padding: 20px;
    }

    .product-info-header {
        margin-bottom: 15px;
        border-bottom: 1px solid #e9ecef;
        padding-bottom: 10px;
    }

    .info-group {
        display: flex;
        margin-bottom: 15px;
        align-items: flex-start;
    }

    .info-label {
        width: 130px;
        font-weight: 600;
        color: #495057;
        flex-shrink: 0;
    }

    .info-value {
        flex-grow: 1;
    }

    /* Product ID Badge */
    .product-id-badge {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        min-width: 32px;
        height: 32px;
        background-color: #f1f3f5;
        color: #495057;
        border-radius: 6px;
        font-size: 0.85rem;
        font-weight: 600;
        padding: 0 8px;
    }

    /* Product Slug Badge */
    .product-slug-badge {
        display: inline-block;
        padding: 6px 12px;
        background-color: #f1f3f5;
        color: #495057;
        border-radius: 6px;
        font-size: 0.85rem;
        font-family: monospace;
    }

    /* Category Badge */
    .category-badge {
        display: inline-flex;
        align-items: center;
        padding: 6px 12px;
        background-color: rgba(36, 68, 90, 0.1);
        color: var(--darker-color);
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 600;
    }

    /* Product Price */
    .product-price {
        display: flex;
        flex-direction: column;
    }

    .price-value {
        font-size: 1.1rem;
        font-weight: 700;
        color: var(--primary-color);
    }

    .sale-price {
        font-size: 0.85rem;
        color: #dc3545;
        text-decoration: line-through;
        margin-top: 3px;
    }

    /* Subscription Info */
    .subscription-info {
        display: flex;
        flex-direction: column;
        gap: 5px;
    }

    .subscription-badge {
        display: inline-flex;
        align-items: center;
        padding: 6px 12px;
        background-color: rgba(66, 134, 119, 0.1);
        color: var(--primary-color);
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 600;
    }

    .subscription-price {
        font-size: 0.85rem;
        color: var(--primary-color);
        font-weight: 600;
        margin-top: 3px;
    }

    /* Stock Badge */
    .stock-badge {
        display: inline-flex;
        align-items: center;
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 600;
    }

    .stock-badge.in-stock {
        background-color: rgba(46, 125, 50, 0.1);
        color: #2e7d32;
    }

    .stock-badge.low-stock {
        background-color: rgba(237, 108, 2, 0.1);
        color: #ed6c02;
    }

    .stock-badge.out-of-stock {
        background-color: rgba(211, 47, 47, 0.1);
        color: #d32f2f;
    }

    /* Date Info */
    .date-info {
        display: flex;
        align-items: center;
        font-size: 0.85rem;
        color: #6c757d;
    }

    /* Product Description */
    .product-description {
        line-height: 1.7;
        color: #495057;
    }

    /* Premium Tabs */
    .premium-tabs {
        border-bottom: 1px solid #e9ecef;
        gap: 10px;
    }

    .premium-tabs .nav-item {
        margin-bottom: -1px;
    }

    .premium-tabs .nav-link {
        border: none;
        padding: 12px 20px;
        border-radius: 8px 8px 0 0;
        font-weight: 600;
        color: #6c757d;
        transition: all 0.3s ease;
    }

    .premium-tabs .nav-link:hover {
        color: var(--primary-color);
        background-color: rgba(66, 134, 119, 0.05);
    }

    .premium-tabs .nav-link.active {
        color: var(--primary-color);
        background-color: white;
        border-bottom: 3px solid var(--primary-color);
    }

    .product-detail-content {
        line-height: 1.7;
        color: #495057;
    }

    /* Empty Content */
    .empty-content {
        text-align: center;
        padding: 30px 20px;
        color: #6c757d;
    }

    .empty-content i {
        font-size: 2.5rem;
        margin-bottom: 15px;
        opacity: 0.5;
    }

    .empty-content p {
        margin-bottom: 15px;
    }

    /* Stats Summary */
    .stats-summary {
        margin-bottom: 20px;
    }

    .stat-card {
        background-color: #f8f9fa;
        border-radius: 12px;
        padding: 15px;
        height: 100%;
        transition: all 0.3s ease;
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    }

    .stat-card.total-sales {
        background-color: rgba(66, 134, 119, 0.1);
    }

    .stat-card.total-revenue {
        background-color: rgba(36, 68, 90, 0.1);
    }

    .stat-icon {
        font-size: 1.5rem;
        margin-bottom: 10px;
        color: var(--primary-color);
    }

    .total-revenue .stat-icon {
        color: var(--darker-color);
    }

    .stat-value {
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 5px;
        color: #212529;
    }

    .stat-label {
        font-size: 0.85rem;
        color: #6c757d;
    }

    /* Sales Breakdown */
    .sales-breakdown {
        margin-top: 25px;
    }

    .breakdown-card {
        background-color: #f8f9fa;
        border-radius: 12px;
        padding: 15px;
        height: 100%;
        transition: all 0.3s ease;
    }

    .breakdown-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    }

    .breakdown-card.regular {
        background-color: rgba(66, 134, 119, 0.1);
    }

    .breakdown-card.subscription {
        background-color: rgba(36, 68, 90, 0.1);
    }

    .breakdown-header {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
    }

    .breakdown-icon {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background-color: rgba(255, 255, 255, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 10px;
        font-size: 1rem;
    }

    .regular .breakdown-icon {
        color: var(--primary-color);
    }

    .subscription .breakdown-icon {
        color: var(--darker-color);
    }

    .breakdown-title {
        font-weight: 600;
        font-size: 0.9rem;
    }

    .regular .breakdown-title {
        color: var(--primary-color);
    }

    .subscription .breakdown-title {
        color: var(--darker-color);
    }

    .breakdown-body {
        text-align: center;
    }

    .breakdown-value {
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 5px;
        color: #212529;
    }

    .breakdown-revenue {
        font-size: 0.85rem;
        color: #6c757d;
        font-weight: 600;
    }

    /* Chart Container */
    .chart-container {
        background-color: #f8f9fa;
        border-radius: 12px;
        padding: 15px;
    }

    .chart-wrapper {
        position: relative;
    }

    /* Recent Orders */
    .recent-orders-list {
        display: flex;
        flex-direction: column;
        gap: 10px;
    }

    .order-item {
        background-color: #f8f9fa;
        border-radius: 12px;
        padding: 15px;
        transition: all 0.3s ease;
    }

    .order-item:hover {
        transform: translateY(-3px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    }

    .order-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .order-number {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .order-badge {
        width: 36px;
        height: 36px;
        border-radius: 50%;
        background-color: rgba(66, 134, 119, 0.1);
        color: var(--primary-color);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1rem;
    }

    .order-details {
        display: flex;
        flex-direction: column;
    }

    .order-id {
        font-weight: 600;
        color: #212529;
        font-size: 0.9rem;
    }

    .order-date {
        font-size: 0.8rem;
        color: #6c757d;
        display: flex;
        align-items: center;
    }

    .order-amount {
        display: flex;
        align-items: center;
    }

    .amount-badge {
        display: inline-block;
        padding: 6px 12px;
        background-color: rgba(66, 134, 119, 0.1);
        color: var(--primary-color);
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 600;
    }

    .quantity-badge {
        font-size: 0.75rem;
        color: #6c757d;
        margin-left: 5px;
    }

    .subscription-tag {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        background-color: rgba(36, 68, 90, 0.1);
        color: var(--darker-color);
        border-radius: 12px;
        font-size: 0.7rem;
        font-weight: 600;
        padding: 2px 6px;
        margin-left: 5px;
    }

    .order-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 10px;
        padding-top: 10px;
        border-top: 1px solid rgba(0, 0, 0, 0.05);
    }

    .order-status {
        display: flex;
        align-items: center;
    }

    .status-indicator {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-right: 6px;
    }

    .status-indicator.status-pending {
        background-color: #ed6c02;
    }

    .status-indicator.status-processing {
        background-color: #0288d1;
    }

    .status-indicator.status-completed {
        background-color: #2e7d32;
    }

    .status-indicator.status-declined {
        background-color: #d32f2f;
    }

    .status-text {
        font-size: 0.75rem;
        color: #6c757d;
    }

    .order-view-link {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        background-color: rgba(66, 134, 119, 0.1);
        color: var(--primary-color);
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
    }

    .order-view-link:hover {
        background-color: var(--primary-color);
        color: white;
        transform: translateX(3px);
    }

    /* Product Actions */
    .product-actions {
        display: flex;
        flex-direction: column;
        gap: 10px;
    }

    /* Text Gradient */
    .text-gradient {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--darker-color) 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-fill-color: transparent;
    }
</style>
@endpush

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize tooltips
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // Handle delete product button
        const deleteButton = document.querySelector('.delete-product-btn');
        if (deleteButton) {
            deleteButton.addEventListener('click', function() {
                const productId = this.dataset.productId;
                const productName = this.dataset.productName;

                Swal.fire({
                    title: 'Are you sure?',
                    html: `You are about to delete <strong>${productName}</strong>.<br><span class="text-danger">This action cannot be undone!</span>`,
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#d33',
                    cancelButtonColor: '#3085d6',
                    confirmButtonText: 'Yes, delete it!',
                    cancelButtonText: 'Cancel',
                    customClass: {
                        confirmButton: 'btn btn-danger',
                        cancelButton: 'btn btn-secondary'
                    },
                    buttonsStyling: false
                }).then((result) => {
                    if (result.isConfirmed) {
                        document.getElementById(`deleteForm${productId}`).submit();
                    }
                });
            });
        }

        // Sales chart
        const salesData = {
            labels: {!! json_encode($salesData['labels'] ?? ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun']) !!},
            datasets: [{
                label: 'Sales',
                data: {!! json_encode($salesData['data'] ?? [0, 0, 0, 0, 0, 0]) !!},
                backgroundColor: 'rgba(66, 134, 119, 0.2)',
                borderColor: 'rgba(66, 134, 119, 1)',
                borderWidth: 2,
                tension: 0.4,
                pointBackgroundColor: 'rgba(66, 134, 119, 1)',
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 4,
                pointHoverRadius: 6
            }]
        };

        // Chart configuration
        const config = {
            type: 'line',
            data: salesData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.05)'
                        },
                        ticks: {
                            font: {
                                size: 10
                            }
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            font: {
                                size: 10
                            }
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: 'rgba(45, 80, 75, 0.9)',
                        titleFont: {
                            size: 12
                        },
                        bodyFont: {
                            size: 12
                        },
                        padding: 10,
                        cornerRadius: 8,
                        displayColors: false
                    }
                },
                interaction: {
                    mode: 'index',
                    intersect: false
                },
                elements: {
                    line: {
                        tension: 0.4
                    }
                }
            }
        };

        // Initialize the chart
        const ctx = document.getElementById('salesChart').getContext('2d');
        new Chart(ctx, config);
    });
</script>
@endpush
