@extends('layouts.admin')

@section('title', 'Edit Product')

@section('content')
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Edit Product: {{ $product->name }}</h1>
        <div>
            <a href="{{ route('admin.products.show', $product->id) }}" class="btn btn-outline-info me-2">
                <i class="bi bi-eye me-1"></i> View
            </a>
            <a href="{{ route('admin.products.index') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left me-1"></i> Back to Products
            </a>
        </div>
    </div>

    <div class="card border-0 shadow-sm">
        <div class="card-body">
            <form action="{{ route('admin.products.update', $product->id) }}" method="POST" enctype="multipart/form-data">
                @csrf
                @method('PUT')
                <div class="row">
                    <!-- Basic Information -->
                    <div class="col-md-8">
                        <div class="card mb-4">
                            <div class="card-header bg-white py-3">
                                <h5 class="mb-0">Basic Information</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="name" class="form-label">Product Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('name') is-invalid @enderror" id="name" name="name" value="{{ old('name', $product->name) }}" required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="slug" class="form-label">Slug <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('slug') is-invalid @enderror" id="slug" name="slug" value="{{ old('slug', $product->slug) }}" required>
                                    <small class="text-muted">The slug is used in the URL. It should be unique and contain only letters, numbers, and hyphens.</small>
                                    @error('slug')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="description" class="form-label">Description <span class="text-danger">*</span></label>
                                    <textarea class="form-control @error('description') is-invalid @enderror" id="description" name="description" rows="5" required>{{ old('description', $product->description) }}</textarea>
                                    @error('description')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="category_id" class="form-label">Category</label>
                                        <select class="form-select @error('category_id') is-invalid @enderror" id="category_id" name="category_id">
                                            <option value="">Select Category</option>
                                            @foreach($categories ?? [] as $category)
                                                <option value="{{ $category->id }}" {{ old('category_id', $product->category_id) == $category->id ? 'selected' : '' }}>{{ $category->name }}</option>
                                            @endforeach
                                        </select>
                                        @error('category_id')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="stock" class="form-label">Stock Quantity</label>
                                        <input type="number" class="form-control @error('stock') is-invalid @enderror" id="stock" name="stock" value="{{ old('stock', $product->stock) }}" min="0">
                                        @error('stock')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card mb-4">
                            <div class="card-header bg-white py-3">
                                <h5 class="mb-0">Product Details</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="ingredients" class="form-label">Ingredients</label>
                                    <textarea class="form-control @error('ingredients') is-invalid @enderror" id="ingredients" name="ingredients" rows="3">{{ old('ingredients', $product->ingredients) }}</textarea>
                                    @error('ingredients')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="benefits" class="form-label">Benefits</label>
                                    <textarea class="form-control @error('benefits') is-invalid @enderror" id="benefits" name="benefits" rows="3">{{ old('benefits', $product->benefits) }}</textarea>
                                    @error('benefits')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="how_to_use" class="form-label">How to Use</label>
                                    <textarea class="form-control @error('how_to_use') is-invalid @enderror" id="how_to_use" name="how_to_use" rows="3">{{ old('how_to_use', $product->how_to_use) }}</textarea>
                                    @error('how_to_use')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Sidebar -->
                    <div class="col-md-4">
                        <div class="card mb-4">
                            <div class="card-header bg-white py-3">
                                <h5 class="mb-0">Pricing</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="price" class="form-label">Regular Price ($) <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control @error('price') is-invalid @enderror" id="price" name="price" value="{{ old('price', $product->price) }}" step="0.01" min="0" required>
                                    @error('price')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="sale_price" class="form-label">Sale Price ($)</label>
                                    <input type="number" class="form-control @error('sale_price') is-invalid @enderror" id="sale_price" name="sale_price" value="{{ old('sale_price', $product->sale_price) }}" step="0.01" min="0">
                                    <small class="text-muted">Leave empty if not on sale</small>
                                    @error('sale_price')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input @error('allow_subscription') is-invalid @enderror" type="checkbox" id="allow_subscription" name="allow_subscription" value="1" {{ old('allow_subscription', $product->allow_subscription) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="allow_subscription">
                                            Allow Subscription
                                        </label>
                                    </div>
                                    <small class="text-muted">Enable subscription option for this product</small>
                                    @error('allow_subscription')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3" id="subscription_only_container" style="{{ old('allow_subscription', $product->allow_subscription) ? '' : 'display: none;' }}">
                                    <div class="form-check">
                                        <input class="form-check-input @error('subscription_only') is-invalid @enderror" type="checkbox" id="subscription_only" name="subscription_only" value="1" {{ old('subscription_only', $product->subscription_only ?? false) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="subscription_only">
                                            Subscription Only
                                        </label>
                                    </div>
                                    <small class="text-muted">Make this product available only as a subscription (cannot be purchased as a one-time purchase)</small>
                                    @error('subscription_only')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3" id="subscription_price_container" style="{{ old('allow_subscription', $product->allow_subscription) ? '' : 'display: none;' }}">
                                    <label for="subscription_price" class="form-label">Subscription Price ($)</label>
                                    <input type="number" class="form-control @error('subscription_price') is-invalid @enderror" id="subscription_price" name="subscription_price" value="{{ old('subscription_price', $product->subscription_price) }}" step="0.01" min="0">
                                    @error('subscription_price')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="card mb-4">
                            <div class="card-header bg-white py-3">
                                <h5 class="mb-0">Image</h5>
                            </div>
                            <div class="card-body">
                                @if($product->image)
                                    <div class="mb-3">
                                        <label class="form-label">Current Image</label>
                                        <div class="mb-2">
                                            <img src="{{ asset('storage/' . $product->image) }}" alt="{{ $product->name }}" class="img-thumbnail" style="max-height: 150px;">
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="remove_image" name="remove_image" value="1">
                                            <label class="form-check-label" for="remove_image">
                                                Remove current image
                                            </label>
                                        </div>
                                    </div>
                                @endif

                                <div class="mb-3">
                                    <label for="image" class="form-label">{{ $product->image ? 'Replace Image' : 'Product Image' }}</label>
                                    <input type="file" class="form-control @error('image') is-invalid @enderror" id="image" name="image" accept="image/*">
                                    <small class="text-muted">Recommended size: 800x800 pixels</small>
                                    @error('image')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                @if($product->gallery && count($product->gallery) > 0)
                                    <div class="mb-3">
                                        <label class="form-label">Current Gallery</label>
                                        <div class="row g-2 mb-2">
                                            @foreach($product->gallery as $index => $galleryImage)
                                                <div class="col-4">
                                                    <div class="position-relative">
                                                        <img src="{{ asset('storage/' . $galleryImage) }}" alt="Gallery image {{ $index + 1 }}" class="img-thumbnail" style="height: 80px; object-fit: cover;">
                                                        <div class="form-check position-absolute" style="bottom: 5px; right: 5px;">
                                                            <input class="form-check-input" type="checkbox" id="remove_gallery_{{ $index }}" name="remove_gallery[]" value="{{ $index }}">
                                                            <label class="form-check-label visually-hidden" for="remove_gallery_{{ $index }}">
                                                                Remove
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                            @endforeach
                                        </div>
                                        <small class="text-muted">Check the images you want to remove</small>
                                    </div>
                                @endif

                                <div class="mb-3">
                                    <label for="gallery" class="form-label">{{ $product->gallery && count($product->gallery) > 0 ? 'Add to Gallery' : 'Product Gallery' }}</label>
                                    <input type="file" class="form-control @error('gallery') is-invalid @enderror" id="gallery" name="gallery[]" accept="image/*" multiple>
                                    <small class="text-muted">You can select multiple images</small>
                                    @error('gallery')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="card mb-4">
                            <div class="card-header bg-white py-3">
                                <h5 class="mb-0">Status</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input @error('is_active') is-invalid @enderror" type="checkbox" id="is_active" name="is_active" value="1" {{ old('is_active', $product->is_active) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="is_active">
                                            Active
                                        </label>
                                    </div>
                                    <small class="text-muted">Inactive products are not visible to customers</small>
                                    @error('is_active')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input @error('is_featured') is-invalid @enderror" type="checkbox" id="is_featured" name="is_featured" value="1" {{ old('is_featured', $product->is_featured) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="is_featured">
                                            Featured
                                        </label>
                                    </div>
                                    <small class="text-muted">Featured products are displayed prominently on the homepage</small>
                                    @error('is_featured')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="d-flex justify-content-end mt-4">
                    <button type="button" class="btn btn-outline-secondary me-2" onclick="window.location.href='{{ route('admin.products.index') }}'">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Product</button>
                </div>
            </form>
        </div>
    </div>
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Toggle subscription price and subscription only fields
        const allowSubscriptionCheckbox = document.getElementById('allow_subscription');
        const subscriptionPriceContainer = document.getElementById('subscription_price_container');
        const subscriptionOnlyContainer = document.getElementById('subscription_only_container');

        allowSubscriptionCheckbox.addEventListener('change', function() {
            subscriptionPriceContainer.style.display = this.checked ? 'block' : 'none';
            subscriptionOnlyContainer.style.display = this.checked ? 'block' : 'none';

            // If allow_subscription is unchecked, also uncheck subscription_only
            if (!this.checked) {
                document.getElementById('subscription_only').checked = false;
            }
        });
    });
</script>
@endpush
