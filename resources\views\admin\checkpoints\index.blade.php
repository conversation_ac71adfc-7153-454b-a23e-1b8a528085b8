@extends('layouts.admin')

@section('title', 'Checkpoints')

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Checkpoints</h1>
        <a href="{{ route('admin.checkpoints.create') }}" class="btn btn-primary">
            <i class="bi bi-plus-circle"></i> New Checkpoint
        </a>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold">All Checkpoints</h6>
        </div>
        <div class="card-body">
            @if($checkpoints->isEmpty())
                <div class="alert alert-info">
                    No checkpoints found. Click the "New Checkpoint" button to create one.
                </div>
            @else
                <div class="table-responsive">
                    <table class="table table-bordered table-hover" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Release</th>
                                <th>Type</th>
                                <th>Status</th>
                                <th>Verified By</th>
                                <th>Verified At</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($checkpoints as $checkpoint)
                                <tr>
                                    <td>{{ $checkpoint->name }}</td>
                                    <td>
                                        <a href="{{ route('admin.releases.show', $checkpoint->release_id) }}">
                                            {{ $checkpoint->release->version }} - {{ $checkpoint->release->name }}
                                        </a>
                                    </td>
                                    <td>{{ ucwords(str_replace('_', ' ', $checkpoint->type)) }}</td>
                                    <td>
                                        @switch($checkpoint->status)
                                            @case('pending')
                                                <span class="badge bg-secondary">Pending</span>
                                                @break
                                            @case('in_progress')
                                                <span class="badge bg-info">In Progress</span>
                                                @break
                                            @case('passed')
                                                <span class="badge bg-success">Passed</span>
                                                @break
                                            @case('failed')
                                                <span class="badge bg-danger">Failed</span>
                                                @break
                                            @default
                                                <span class="badge bg-secondary">{{ $checkpoint->status }}</span>
                                        @endswitch
                                    </td>
                                    <td>{{ $checkpoint->verifier->name ?? 'Not verified' }}</td>
                                    <td>{{ $checkpoint->verified_at ? $checkpoint->verified_at->format('Y-m-d H:i') : 'Not verified' }}</td>
                                    <td>
                                        <a href="{{ route('admin.checkpoints.show', $checkpoint->id) }}" class="btn btn-sm btn-info">
                                            <i class="bi bi-eye"></i> View
                                        </a>
                                        @if($checkpoint->release->status === 'draft' && $checkpoint->status === 'pending')
                                            <a href="{{ route('admin.checkpoints.edit', $checkpoint->id) }}" class="btn btn-sm btn-primary">
                                                <i class="bi bi-pencil"></i> Edit
                                            </a>
                                            <form action="{{ route('admin.checkpoints.destroy', $checkpoint->id) }}" method="POST" class="d-inline">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to delete this checkpoint?')">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </form>
                                        @endif
                                        @if($checkpoint->release->status === 'testing' && $checkpoint->status === 'pending')
                                            <form action="{{ route('admin.checkpoints.start-verification', $checkpoint->id) }}" method="POST" class="d-inline">
                                                @csrf
                                                <button type="submit" class="btn btn-sm btn-warning">
                                                    <i class="bi bi-play"></i> Start
                                                </button>
                                            </form>
                                        @endif
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                <div class="mt-4">
                    {{ $checkpoints->links() }}
                </div>
            @endif
        </div>
    </div>
</div>
@endsection
