<?php

use App\Http\Controllers\AffiliateController;
use App\Http\Controllers\CartController;
use App\Http\Controllers\CurrencyController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\LanguageController;
use App\Http\Controllers\OrderController;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\SubscriptionController;
use App\Http\Controllers\UserProfileController;
use App\Http\Controllers\Admin\AdminController;
use App\Http\Controllers\Admin\AdminProductController;
use App\Http\Controllers\Admin\AdminOrderController;
use App\Http\Controllers\Admin\AdminAffiliateController;
use App\Http\Controllers\Admin\AdminUserController;
use App\Http\Controllers\Admin\ReleaseController;
use App\Http\Controllers\Admin\CheckpointController;
use App\Http\Controllers\Admin\PartitionController;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

// Public routes
Route::get('/', [HomeController::class, 'index'])->name('home');
Route::get('/about', [HomeController::class, 'about'])->name('about');
Route::get('/contact', [HomeController::class, 'contact'])->name('contact');
Route::post('/contact', [HomeController::class, 'contactSubmit'])->name('contact.submit');
Route::get('/natryon-pack', [HomeController::class, 'natryonPackLanding'])->name('natryon.pack');

// Language routes
Route::get('/language/{locale}', [LanguageController::class, 'switch'])->name('language.switch');

// Currency routes
Route::post('/currency/change', [CurrencyController::class, 'changeCurrency'])->name('currency.change');
Route::get('/api/currencies', [CurrencyController::class, 'getCurrencies'])->name('api.currencies');
Route::post('/api/currency/convert', [CurrencyController::class, 'convert'])->name('api.currency.convert');

// Currency test page
Route::get('/currency-test', function() {
    return view('currency-test');
})->name('currency-test');
Route::get('/products/{slug}', [ProductController::class, 'show'])->name('products.show');
Route::get('/products', [ProductController::class, 'index'])->name('products.index');
Route::get('/landing/{slug}', [HomeController::class, 'landingPage'])->name('landing');

// Stripe Test Routes
Route::get('/stripe-test', [App\Http\Controllers\StripeTestController::class, 'index'])->name('stripe.test');
Route::post('/api/create-payment-intent', [App\Http\Controllers\StripeTestController::class, 'createPaymentIntent']);
Route::get('/checkout-test', [App\Http\Controllers\StripeTestController::class, 'checkoutTest'])->name('checkout.test');
Route::post('/api/create-checkout-session', [App\Http\Controllers\StripeTestController::class, 'createCheckoutSession']);

// Cart routes
Route::get('/cart', [CartController::class, 'index'])->name('cart.index');
Route::post('/cart/add', [ProductController::class, 'addToCart'])->name('cart.add');
Route::post('/cart/remove', [ProductController::class, 'removeFromCart'])->name('cart.remove');
Route::post('/cart/update', [ProductController::class, 'updateCart'])->name('cart.update');
Route::post('/cart/clear', [CartController::class, 'clear'])->name('cart.clear');
Route::post('/cart/apply-affiliate-code', [CartController::class, 'applyAffiliateCode'])->name('cart.apply-affiliate-code');
Route::post('/cart/remove-affiliate-code', [CartController::class, 'removeAffiliateCode'])->name('cart.remove-affiliate-code');
Route::get('/checkout', [CartController::class, 'checkout'])->name('checkout');
Route::post('/checkout/update-shipping', [CartController::class, 'updateShipping'])->name('checkout.update-shipping');

// Auth routes
Auth::routes();

// Stripe webhook
Route::post('/stripe/webhook', function() {
    // This route will handle Stripe webhook events
    $endpoint_secret = env('STRIPE_WEBHOOK_SECRET');
    $payload = @file_get_contents('php://input');
    $sig_header = $_SERVER['HTTP_STRIPE_SIGNATURE'];
    $event = null;

    try {
        $event = \Stripe\Webhook::constructEvent(
            $payload, $sig_header, $endpoint_secret
        );
    } catch(\UnexpectedValueException $e) {
        // Invalid payload
        return response()->json(['error' => 'Invalid payload'], 400);
    } catch(\Stripe\Exception\SignatureVerificationException $e) {
        // Invalid signature
        return response()->json(['error' => 'Invalid signature'], 400);
    }

    // Handle the event
    switch ($event->type) {
        case 'payment_intent.succeeded':
            $paymentIntent = $event->data->object;
            // Log the successful payment
            \Illuminate\Support\Facades\Log::info('Payment succeeded: ' . $paymentIntent->id);
            break;
        case 'invoice.payment_succeeded':
            $invoice = $event->data->object;
            // Log the successful subscription payment
            \Illuminate\Support\Facades\Log::info('Subscription payment succeeded: ' . $invoice->id);
            break;
        default:
            // Log unexpected event type
            \Illuminate\Support\Facades\Log::info('Unhandled event type: ' . $event->type);
            break;
    }

    return response()->json(['status' => 'success']);
});

// User routes (authenticated)
Route::middleware(['auth'])->group(function () {
    // Profile routes
    Route::get('/profile', [UserProfileController::class, 'edit'])->name('profile.edit');
    Route::put('/profile', [UserProfileController::class, 'update'])->name('profile.update');
    Route::get('/profile/password', [UserProfileController::class, 'editPassword'])->name('profile.password');
    Route::put('/profile/password', [UserProfileController::class, 'updatePassword'])->name('profile.password.update');

    // Order routes
    Route::post('/orders', [OrderController::class, 'store'])->name('orders.store');
    Route::get('/orders', [OrderController::class, 'index'])->name('orders.index');
    Route::get('/orders/payment/callback/{order_id}', [OrderController::class, 'paymentCallback'])->name('orders.payment.callback');
    Route::get('/orders/{id}', [OrderController::class, 'show'])->name('orders.show');

    // Subscription routes
    Route::get('/subscriptions', [SubscriptionController::class, 'index'])->name('subscriptions.index');
    Route::get('/subscriptions/{id}', [SubscriptionController::class, 'show'])->name('subscriptions.show');
    Route::post('/subscriptions/{id}/cancel', [SubscriptionController::class, 'cancel'])->name('subscriptions.cancel');
    Route::post('/orders/{order}/items/{item}/cancel-subscription', [SubscriptionController::class, 'cancelFromOrder'])->name('subscriptions.cancel-from-order');

    // Affiliate routes
    Route::get('/affiliate', [AffiliateController::class, 'index'])->name('affiliate.index');
    Route::post('/affiliate/generate-code', [AffiliateController::class, 'generateCode'])->name('affiliate.generate-code');
    Route::get('/affiliate/earnings', [AffiliateController::class, 'earnings'])->name('affiliate.earnings');
    Route::get('/affiliate/referrals', [AffiliateController::class, 'referrals'])->name('affiliate.referrals');
});

// Admin routes
Route::prefix('admin')->name('admin.')->middleware(['auth', 'role:admin'])->group(function () {
    Route::get('/', [AdminController::class, 'index'])->name('dashboard');

    // Admin product routes
    Route::resource('products', AdminProductController::class);

    // Admin order routes
    Route::resource('orders', AdminOrderController::class);
    Route::get('orders/{id}/pdf', [AdminOrderController::class, 'generatePdf'])->name('orders.pdf');
    Route::get('orders/{id}/email', [AdminOrderController::class, 'sendEmail'])->name('orders.email');

    // Admin user routes
    Route::resource('users', AdminUserController::class);

    // Admin affiliate routes
    Route::resource('affiliates', AdminAffiliateController::class);
    Route::put('affiliates/earnings/{id}', [AdminAffiliateController::class, 'updateEarning'])->name('affiliates.update-earning');
    Route::get('affiliates/earnings', [AdminAffiliateController::class, 'earnings'])->name('affiliates.earnings');

    // Admin shipping routes
    Route::resource('shipping', App\Http\Controllers\Admin\AdminShippingController::class);
    Route::post('shipping/update-order', [App\Http\Controllers\Admin\AdminShippingController::class, 'updateOrder'])->name('shipping.update-order');

    // Admin release management routes
    Route::resource('releases', ReleaseController::class);
    Route::post('releases/{id}/submit-for-testing', [ReleaseController::class, 'submitForTesting'])->name('releases.submit-for-testing');
    Route::post('releases/{id}/approve', [ReleaseController::class, 'approve'])->name('releases.approve');
    Route::post('releases/{id}/deploy', [ReleaseController::class, 'deploy'])->name('releases.deploy');

    // Admin checkpoint routes
    Route::resource('checkpoints', CheckpointController::class);
    Route::post('checkpoints/{id}/start-verification', [CheckpointController::class, 'startVerification'])->name('checkpoints.start-verification');
    Route::post('checkpoints/{id}/pass', [CheckpointController::class, 'pass'])->name('checkpoints.pass');
    Route::post('checkpoints/{id}/fail', [CheckpointController::class, 'fail'])->name('checkpoints.fail');

    // Admin partition routes
    Route::resource('partitions', PartitionController::class);
    Route::post('partitions/{id}/deploy', [PartitionController::class, 'deploy'])->name('partitions.deploy');
    Route::post('partitions/{id}/fail', [PartitionController::class, 'fail'])->name('partitions.fail');
    Route::post('partitions/{id}/rollback', [PartitionController::class, 'rollback'])->name('partitions.rollback');

    // Currency test route
    Route::get('currency-test', function() {
        $products = \App\Models\Product::take(5)->get();
        return view('admin.currency-test', compact('products'));
    })->name('currency-test');
});

Auth::routes();

Route::get('/home', [App\Http\Controllers\HomeController::class, 'index'])->name('home');
