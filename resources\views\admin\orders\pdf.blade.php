<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="utf-8">
    <title>Invoice #{{ $order->order_number }}</title>
    <style>
        @page {
            margin: 0;
        }

        body {
            font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
            font-size: 14px;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: #fff;
        }

        .container {
            width: 100%;
            padding: 0;
        }

        .invoice-header {
            background-color: #428677;
            color: white;
            padding: 30px 40px;
            position: relative;
        }

        .invoice-header-content {
            max-width: 1000px;
            margin: 0 auto;
            position: relative;
        }

        .logo {
            font-size: 32px;
            font-weight: bold;
            letter-spacing: 1px;
            margin-bottom: 5px;
        }

        .invoice-title {
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 2px;
            margin-bottom: 15px;
            opacity: 0.9;
        }

        .invoice-details {
            position: absolute;
            top: 0;
            right: 0;
            text-align: right;
            padding-top: 30px;
        }

        .invoice-id {
            font-size: 22px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .invoice-date {
            font-size: 14px;
            opacity: 0.9;
        }

        .content {
            max-width: 1000px;
            margin: 0 auto;
            padding: 40px;
        }

        .section {
            margin-bottom: 40px;
        }

        .row {
            display: table;
            width: 100%;
            clear: both;
        }

        .col {
            display: table-cell;
            width: 50%;
            vertical-align: top;
            padding: 0 15px;
        }

        .section-title {
            font-size: 16px;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 1px;
            color: #428677;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid #f0f0f0;
        }

        .address-block {
            margin-bottom: 30px;
            background-color: #f9f9f9;
            padding: 20px;
            border-radius: 5px;
        }

        .address-title {
            font-weight: bold;
            margin-bottom: 10px;
            font-size: 16px;
        }

        .order-info {
            margin-bottom: 30px;
            background-color: #f9f9f9;
            padding: 20px;
            border-radius: 5px;
        }

        .info-row {
            margin-bottom: 10px;
        }

        .info-label {
            font-weight: bold;
            display: inline-block;
            width: 140px;
        }

        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-weight: bold;
            text-transform: uppercase;
            font-size: 12px;
        }

        .status-pending {
            background-color: #fff3cd;
            color: #856404;
        }

        .status-processing {
            background-color: #d1ecf1;
            color: #0c5460;
        }

        .status-completed {
            background-color: #d4edda;
            color: #155724;
        }

        .status-declined {
            background-color: #f8d7da;
            color: #721c24;
        }

        .payment-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-weight: bold;
            text-transform: uppercase;
            font-size: 12px;
        }

        .payment-paid {
            background-color: #d4edda;
            color: #155724;
        }

        .payment-unpaid {
            background-color: #fff3cd;
            color: #856404;
        }

        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }

        .items-table th {
            background-color: #f5f5f5;
            padding: 12px 15px;
            text-align: left;
            font-weight: bold;
            color: #333;
            border-bottom: 2px solid #ddd;
            text-transform: uppercase;
            font-size: 12px;
        }

        .items-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
            vertical-align: top;
        }

        .items-table .product-name {
            width: 50%;
        }

        .items-table .quantity {
            width: 15%;
        }

        .items-table .price {
            width: 15%;
        }

        .items-table .total {
            width: 20%;
            text-align: right;
        }

        .subscription-label {
            display: inline-block;
            background-color: #e3f2fd;
            color: #0d47a1;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 11px;
            margin-top: 5px;
        }

        .totals-table {
            width: 350px;
            margin-left: auto;
            border-collapse: collapse;
        }

        .totals-table td {
            padding: 8px 15px;
            border: none;
        }

        .totals-table .totals-label {
            text-align: right;
            font-weight: bold;
        }

        .totals-table .totals-value {
            text-align: right;
        }

        .totals-table th {
            font-size: 0;
            line-height: 0;
            padding: 0;
            height: 0;
            border: none;
            background: none;
            color: transparent;
        }

        .totals-table .grand-total td {
            font-size: 18px;
            font-weight: bold;
            color: #428677;
            padding-top: 15px;
            border-top: 2px solid #428677;
        }

        .notes {
            background-color: #f9f9f9;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 30px;
            font-style: italic;
        }

        .footer {
            text-align: center;
            margin-top: 50px;
            padding-top: 30px;
            border-top: 1px solid #eee;
            color: #777;
            font-size: 12px;
        }

        .footer-logo {
            font-weight: bold;
            font-size: 16px;
            color: #428677;
            margin-bottom: 10px;
        }

        .affiliate-info {
            background-color: #e8f5e9;
            padding: 15px;
            border-radius: 5px;
            margin-top: 15px;
        }

        .affiliate-row {
            margin-bottom: 5px;
        }

        .affiliate-label {
            font-weight: bold;
            display: inline-block;
            width: 100px;
        }

        .page-break {
            page-break-after: always;
        }

        /* Responsive adjustments for PDF */
        @media print {
            .invoice-header {
                padding: 20px 30px;
            }

            .content {
                padding: 30px;
            }

            .section {
                margin-bottom: 30px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="invoice-header">
            <div class="invoice-header-content">
                <div class="logo">NATRYON</div>
                <div class="invoice-title">Invoice</div>

                <div class="invoice-details">
                    <div class="invoice-id">#{{ $order->order_number }}</div>
                    <div class="invoice-date">{{ $order->created_at->format('F d, Y') }}</div>
                </div>
            </div>
        </div>

        <div class="content">
            <div class="section">
                <div class="row">
                    <div class="col" style="padding-right: 20px;">
                        <div class="address-block">
                            <div class="section-title">Billing Information</div>
                            <div class="address-title">{{ $order->billing_name }}</div>
                            <div>
                                {{ $order->billing_address }}<br>
                                {{ $order->billing_city }}, {{ $order->billing_state }} {{ $order->billing_zipcode }}<br>
                                {{ $order->billing_country }}<br>
                                <br>
                                <strong>Email:</strong> {{ $order->billing_email }}<br>
                                <strong>Phone:</strong> {{ $order->billing_phone }}
                            </div>
                        </div>
                    </div>
                    <div class="col" style="padding-left: 20px;">
                        <div class="address-block">
                            <div class="section-title">Shipping Information</div>
                            <div class="address-title">{{ $order->shipping_name ?? $order->billing_name }}</div>
                            <div>
                                {{ $order->shipping_address ?? $order->billing_address }}<br>
                                {{ $order->shipping_city ?? $order->billing_city }}, {{ $order->shipping_state ?? $order->billing_state }} {{ $order->shipping_zipcode ?? $order->billing_zipcode }}<br>
                                {{ $order->shipping_country ?? $order->billing_country }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="section">
                <div class="order-info">
                    <div class="section-title">Order Information</div>
                    <div class="info-row">
                        <span class="info-label">Order Status:</span>
                        <span class="status-badge status-{{ $order->status }}">{{ ucfirst($order->status) }}</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">Payment Method:</span> {{ ucfirst($order->payment_method) }}
                    </div>
                    <div class="info-row">
                        <span class="info-label">Payment Status:</span>
                        <span class="payment-badge payment-{{ $order->is_paid ? 'paid' : 'unpaid' }}">
                            {{ $order->is_paid ? 'Paid' : 'Unpaid' }}
                        </span>
                    </div>
                    @if($order->payment_id)
                    <div class="info-row">
                        <span class="info-label">Payment ID:</span> {{ $order->payment_id }}
                    </div>
                    @endif

                    @if(isset($affiliateUser))
                    <div class="affiliate-info">
                        <div class="affiliate-row">
                            <span class="affiliate-label">Affiliate:</span> {{ $affiliateUser->name }}
                        </div>
                        <div class="affiliate-row">
                            <span class="affiliate-label">Code:</span> {{ $order->affiliate_code }}
                        </div>
                        <div class="affiliate-row">
                            <span class="affiliate-label">Commission:</span> ${{ number_format($affiliateCommission, 2) }}
                        </div>
                    </div>
                    @endif
                </div>
            </div>

            <div class="section">
                <div class="section-title">Order Items</div>
                <table class="items-table">
                    <thead>
                        <tr>
                            <th class="product-name">Product</th>
                            <th class="price">Price</th>
                            <th class="quantity">Quantity</th>
                            <th class="total">Total</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($order->items as $item)
                        <tr>
                            <td>
                                <strong>{{ $item->product_name }}</strong>
                                @if($item->is_subscription)
                                <div class="subscription-label">Subscription</div>
                                @endif
                            </td>
                            <td>${{ number_format($item->price, 2) }}</td>
                            <td>{{ $item->quantity }}</td>
                            <td class="total">${{ number_format($item->price * $item->quantity, 2) }}</td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>

                <table class="totals-table">
                    <thead>
                        <tr>
                            <th class="totals-label">Description</th>
                            <th class="totals-value">Amount</th>
                        </tr>
                    </thead>
                    <tbody>
                    <tr>
                        <td class="totals-label">Subtotal:</td>
                        <td class="totals-value">${{ number_format($order->subtotal, 2) }}</td>
                    </tr>
                    @if($order->tax > 0)
                    <tr>
                        <td class="totals-label">Tax:</td>
                        <td class="totals-value">${{ number_format($order->tax, 2) }}</td>
                    </tr>
                    @endif
                    @if($order->shipping > 0)
                    <tr>
                        <td class="totals-label">Shipping:</td>
                        <td class="totals-value">${{ number_format($order->shipping, 2) }}</td>
                    </tr>
                    @endif
                    @if($order->discount > 0)
                    <tr>
                        <td class="totals-label">Discount:</td>
                        <td class="totals-value">-${{ number_format($order->discount, 2) }}</td>
                    </tr>
                    @endif
                    <tr class="grand-total">
                        <td class="totals-label">Total:</td>
                        <td class="totals-value">${{ number_format($order->total, 2) }}</td>
                    </tr>
                    </tbody>
                </table>
            </div>

            @if($order->notes)
            <div class="section">
                <div class="section-title">Order Notes</div>
                <div class="notes">
                    {{ $order->notes }}
                </div>
            </div>
            @endif

            <div class="footer">
                <div class="footer-logo">NATRYON</div>
                <p>Thank you for your business!</p>
                <p>&copy; {{ date('Y') }} NATRYON</p>
            </div>
        </div>
    </div>
</body>
</html>
