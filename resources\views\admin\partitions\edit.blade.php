@extends('layouts.admin')

@section('title', 'Edit Partition')

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Edit Partition: {{ $partition->name }}</h1>
        <div>
            <a href="{{ route('admin.partitions.show', $partition->id) }}" class="btn btn-secondary">
                <i class="bi bi-arrow-left"></i> Back to Partition
            </a>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold">Partition Information</h6>
        </div>
        <div class="card-body">
            <form action="{{ route('admin.partitions.update', $partition->id) }}" method="POST">
                @csrf
                @method('PUT')
                <div class="mb-3">
                    <label for="name" class="form-label">Name <span class="text-danger">*</span></label>
                    <input type="text" class="form-control @error('name') is-invalid @enderror" id="name" name="name" value="{{ old('name', $partition->name) }}" required>
                    @error('name')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                    <small class="form-text text-muted">A descriptive name for this partition.</small>
                </div>

                <div class="mb-3">
                    <label for="type" class="form-label">Type <span class="text-danger">*</span></label>
                    <select class="form-select @error('type') is-invalid @enderror" id="type" name="type" required>
                        <option value="">Select a type</option>
                        @foreach($partitionTypes as $value => $label)
                            <option value="{{ $value }}" {{ old('type', $partition->type) == $value ? 'selected' : '' }}>
                                {{ $label }}
                            </option>
                        @endforeach
                    </select>
                    @error('type')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="mb-3">
                    <label for="deployment_order" class="form-label">Deployment Order <span class="text-danger">*</span></label>
                    <input type="number" class="form-control @error('deployment_order') is-invalid @enderror" id="deployment_order" name="deployment_order" value="{{ old('deployment_order', $partition->deployment_order) }}" min="0" required>
                    @error('deployment_order')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                    <small class="form-text text-muted">The order in which this partition should be deployed (lower numbers first).</small>
                </div>

                <div class="mb-3">
                    <label for="description" class="form-label">Description</label>
                    <textarea class="form-control @error('description') is-invalid @enderror" id="description" name="description" rows="3">{{ old('description', $partition->description) }}</textarea>
                    @error('description')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                    <small class="form-text text-muted">Provide a detailed description of what this partition contains.</small>
                </div>

                <div class="mb-3">
                    <label for="file_paths" class="form-label">File Paths</label>
                    <textarea class="form-control @error('file_paths') is-invalid @enderror" id="file_paths" name="file_paths" rows="5" placeholder="Enter one file path per line">{{ old('file_paths', !empty($partition->file_paths) ? implode("\n", $partition->file_paths) : '') }}</textarea>
                    @error('file_paths')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                    <small class="form-text text-muted">Enter one file path per line that is included in this partition.</small>
                </div>

                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-save"></i> Update Partition
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection
