@extends('layouts.app')

@section('title', __('Your Cart').' - NATRYON')

@push('styles')
<style>
    /* Cart page specific styles */
    .cart-section {
        min-height: 70vh;
    }

    .cart-item-image {
        width: 90px;
        height: 90px;
        object-fit: cover;
        transition: transform 0.3s ease;
    }

    .cart-item-image:hover {
        transform: scale(1.05);
    }

    .product-name-link {
        color: #2d504b;
        text-decoration: none;
        transition: color 0.2s;
    }

    .product-name-link:hover {
        color: #428677;
        text-decoration: underline;
    }

    .subscription-badge {
        background-color: #428677;
    }

    .quantity-control {
        display: flex;
        align-items: center;
        border: 1px solid #dee2e6;
        border-radius: 4px;
        overflow: hidden;
    }

    .quantity-btn {
        background: #f8f9fa;
        border: none;
        padding: 0.375rem 0.75rem;
        cursor: pointer;
        transition: background-color 0.2s;
    }

    .quantity-btn:hover {
        background-color: #e9ecef;
    }

    .quantity-input {
        width: 40px;
        border: none;
        text-align: center;
        font-weight: 500;
    }

    .quantity-input:focus {
        outline: none;
    }

    .cart-total {
        color: #428677;
    }

    .remove-item-btn {
        color: #dc3545;
        background: transparent;
        border: none;
        transition: transform 0.2s, color 0.2s;
    }

    .remove-item-btn:hover {
        color: #b02a37;
        transform: scale(1.1);
    }

    .empty-cart-container {
        padding: 3rem 0;
    }

    .empty-cart-icon {
        font-size: 5rem;
        color: #dee2e6;
        margin-bottom: 1.5rem;
    }

    .order-summary-card {
        position: sticky;
        top: 20px;
    }

    .checkout-btn {
        background-color: #428677;
        border-color: #428677;
        transition: all 0.3s ease;
    }

    .checkout-btn:hover {
        background-color: #2d504b;
        border-color: #2d504b;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    .continue-shopping-btn {
        color: #428677;
        border-color: #428677;
    }

    .continue-shopping-btn:hover {
        background-color: #428677;
        color: white;
    }

    .clear-cart-btn {
        color: #dc3545;
        border-color: #dc3545;
    }

    .clear-cart-btn:hover {
        background-color: #dc3545;
        color: white;
    }

    /* Savings calculation */
    .savings-alert {
        background-color: #f0f9f6;
        border-left: 4px solid #428677;
    }

    /* Responsive adjustments */
    @media (max-width: 767.98px) {
        .cart-item-image {
            width: 70px;
            height: 70px;
        }

        .cart-actions {
            flex-direction: column;
            gap: 0.5rem;
        }

        .cart-actions .btn {
            width: 100%;
        }
    }
</style>
@endpush

@section('content')
    <!-- Hero Section -->
    <section class="py-4 bg-light">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8 text-center text-md-start">
                    <h1 class="display-5 fw-bold" style="color: #428677;">{{ __('Your Cart') }}</h1>
                    <p class="lead text-muted">{{ __('Review your items before checkout') }}</p>
                </div>
                <div class="col-md-4 d-none d-md-block text-end">
                    <i class="bi bi-cart4" style="font-size: 3.5rem; color: #428677;"></i>
                </div>
            </div>
        </div>
    </section>

    <!-- Cart Section -->
    <section class="py-5 cart-section">
        <div class="container">
            <div class="row">
                <!-- Cart Items -->
                <div class="col-lg-8 mb-4 mb-lg-0">
                    <div class="card border-0 shadow-sm rounded-3 mb-4">
                        <div class="card-header bg-white py-3 border-bottom border-light">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="mb-0" style="color: #428677;"><i class="bi bi-cart me-2"></i>{{ __('Cart Items') }}</h5>
                                <span class="badge bg-light text-dark">{{ count($cart) }} {{ __('items') }}</span>
                            </div>
                        </div>
                        <div class="card-body">
                        @if(count($cart) > 0)
                            <div class="table-responsive">
                                <table class="table table-hover align-middle">
                                    <thead class="table-light">
                                        <tr>
                                            <th class="ps-3">{{ __('Product') }}</th>
                                            <th>{{ __('Price') }}</th>
                                            <th>{{ __('Quantity') }}</th>
                                            <th>{{ __('Total') }}</th>
                                            <th class="text-end pe-3">{{ __('Actions') }}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($cart as $id => $item)
                                            <tr>
                                                <td class="ps-3">
                                                    <div class="d-flex align-items-center">
                                                        @if($item['image'])
                                                            <a href="{{ route('products.show', $item['slug'] ?? 'natryon') }}">
                                                                <img src="{{ asset('storage/' . $item['image']) }}" alt="{{ $item['name'] }}" class="rounded-3 me-3 cart-item-image">
                                                            </a>
                                                        @else
                                                            <div class="bg-light d-flex align-items-center justify-content-center me-3 rounded-3" style="width: 90px; height: 90px;">
                                                                <i class="bi bi-box-seam text-muted fs-4"></i>
                                                            </div>
                                                        @endif
                                                        <div>
                                                            <a href="{{ route('products.show', $item['slug'] ?? 'natryon') }}" class="product-name-link">
                                                                <h6 class="mb-1 fw-semibold">{{ $item['name'] }}</h6>
                                                            </a>
                                                            @if($item['is_subscription'])
                                                                <span class="badge subscription-badge rounded-pill">{{ __('Subscription') }}</span>
                                                            @endif
                                                        </div>
                                                    </div>
                                                </td>
                                                <td class="fw-medium">
                                                    ${{ number_format($item['price'], 2) }}
                                                    @if($item['is_subscription'])
                                                        <small class="d-block text-muted">{{ __('per month') }}</small>
                                                    @endif
                                                </td>
                                                <td>
                                                    <form action="{{ route('cart.update') }}" method="POST" class="quantity-form">
                                                        @csrf
                                                        <input type="hidden" name="cart_item_id" value="{{ $id }}">
                                                        <div class="quantity-control">
                                                            <button type="button" class="quantity-btn decrease-btn">-</button>
                                                            <input type="number" name="quantity" value="{{ $item['quantity'] }}" min="1" max="10" class="quantity-input" readonly>
                                                            <button type="button" class="quantity-btn increase-btn">+</button>
                                                        </div>
                                                    </form>
                                                </td>
                                                <td class="fw-semibold cart-total">${{ number_format($item['price'] * $item['quantity'], 2) }}</td>
                                                <td class="text-end pe-3">
                                                    <form action="{{ route('cart.remove') }}" method="POST">
                                                        @csrf
                                                        <input type="hidden" name="cart_item_id" value="{{ $id }}">
                                                        <button type="submit" class="remove-item-btn" title="{{ __('Remove item') }}">
                                                            <i class="bi bi-trash fs-5"></i>
                                                        </button>
                                                    </form>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>

                            <!-- Estimated Delivery -->
                            <div class="alert alert-light border-0 mt-4">
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-truck me-3 fs-4" style="color: #428677;"></i>
                                    <div>
                                        <h6 class="mb-1">{{ __('Estimated Delivery') }}</h6>
                                        <p class="mb-0 text-muted">{{ __('Standard Shipping: 3-5 business days') }}</p>
                                    </div>
                                </div>
                            </div>

                            <div class="d-flex justify-content-between mt-4 pt-3 border-top cart-actions">
                                <a href="{{ route('products.index') }}" class="btn btn-outline-primary continue-shopping-btn rounded-3 px-4">
                                    <i class="bi bi-arrow-left me-2"></i> {{ __('Continue Shopping') }}
                                </a>
                                <form action="{{ route('cart.clear') }}" method="POST">
                                    @csrf
                                    <button type="submit" class="btn btn-outline-danger clear-cart-btn rounded-3 px-4">
                                        <i class="bi bi-trash me-2"></i> {{ __('Clear Cart') }}
                                    </button>
                                </form>
                            </div>
                        @else
                            <div class="text-center empty-cart-container">
                                <i class="bi bi-cart-x empty-cart-icon"></i>
                                <h3 class="mt-4 mb-3">{{ __('Your cart is empty') }}</h3>
                                <p class="text-muted mb-4">{{ __('Add some products to your cart and come back here to checkout.') }}</p>
                                <a href="{{ route('products.index') }}" class="btn btn-primary btn-lg px-4 py-2 checkout-btn">
                                    <i class="bi bi-arrow-left me-2"></i> {{ __('Browse Products') }}
                                </a>
                            </div>
                        @endif
                        </div>
                    </div>

                    <!-- Recommended Products -->
                    @if(isset($recommendedProducts) && $recommendedProducts->count() > 0)
                    <div class="card border-0 shadow-sm rounded-3">
                        <div class="card-header bg-white py-3 border-bottom border-light">
                            <h5 class="mb-0" style="color: #428677;"><i class="bi bi-stars me-2"></i>{{ __('You Might Also Like') }}</h5>
                        </div>
                        <div class="card-body">
                            <div class="row g-3">
                                @foreach($recommendedProducts as $product)
                                <div class="col-md-4">
                                    <div class="card h-100 border-0 shadow-sm product-card">
                                        @if($product->image)
                                            <img src="{{ asset('storage/' . $product->image) }}" class="card-img-top" alt="{{ $product->name }}" style="height: 200px; object-fit: cover;">
                                        @else
                                            <img src="https://via.placeholder.com/300x200?text=NATRYON" class="card-img-top" alt="{{ $product->name }}">
                                        @endif
                                        <div class="card-body">
                                            <h6 class="card-title">{{ $product->name }}</h6>
                                            <p class="card-text text-muted small">{{ Str::limit(strip_tags($product->description), 60) }}</p>
                                            <div class="d-flex justify-content-between align-items-center">
                                                <span class="fw-bold" style="color: #428677;">${{ number_format($product->price, 2) }}</span>
                                                <a href="{{ route('products.show', $product->slug) }}" class="btn btn-sm btn-outline-primary">{{ __('View') }}</a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                    @endif
                </div>

                <!-- Order Summary -->
                <div class="col-lg-4">
                    <div class="card border-0 shadow-sm rounded-3 order-summary-card">
                        <div class="card-header bg-white py-3 border-bottom border-light">
                            <h5 class="mb-0" style="color: #428677;"><i class="bi bi-receipt me-2"></i>{{ __('Order Summary') }}</h5>
                        </div>
                        <div class="card-body p-4">
                            <div class="d-flex justify-content-between mb-3">
                                <span class="text-muted">{{ __('Subtotal:') }}</span>
                                <span class="fw-medium">${{ number_format($subtotal, 2) }}</span>
                            </div>

                            @if($subtotal > 0)
                                <!-- Subscription Savings -->
                                @php
                                    $hasSubscription = false;
                                    foreach($cart as $item) {
                                        if($item['is_subscription']) {
                                            $hasSubscription = true;
                                            break;
                                        }
                                    }

                                    // Calculate estimated savings
                                    $savings = 0;
                                    if($hasSubscription) {
                                        $savings = $subtotal * 0.15; // 15% savings estimate
                                    }
                                @endphp

                                @if($hasSubscription && $savings > 0)
                                <div class="alert savings-alert mb-3 py-2 px-3">
                                    <div class="d-flex align-items-center">
                                        <i class="bi bi-piggy-bank me-2" style="color: #428677;"></i>
                                        <div>
                                            <small class="fw-medium" style="color: #428677;">{{ __('Subscription Savings:') }}</small>
                                            <small class="d-block text-success">-${{ number_format($savings, 2) }}</small>
                                        </div>
                                    </div>
                                </div>
                                @endif
                            @endif

                            <div class="d-flex justify-content-between mb-3">
                                <span class="text-muted">{{ __('Shipping:') }}</span>
                                <span class="text-success fw-medium">{{ __('Free') }}</span>
                            </div>

                            <hr class="my-4">
                            <div class="d-flex justify-content-between mb-4">
                                <span class="fw-bold fs-5">{{ __('Total:') }}</span>
                                <span class="fw-bold fs-5" style="color: #428677;">${{ number_format($total, 2) }}</span>
                            </div>

                            <!-- Affiliate Code Info -->
                            @if($affiliateCode)
                                <div class="alert alert-success d-flex align-items-center mb-4 rounded-3 border-0">
                                    <i class="bi bi-check-circle-fill me-2 fs-5"></i>
                                    <div>
                                        {{ __('Affiliate code') }} <strong>{{ $affiliateCode }}</strong> {{ __('applied!') }}
                                        <small class="d-block text-muted">{{ __('The affiliate will receive a 10% commission on your purchase.') }}</small>
                                    </div>
                                </div>
                            @endif

                            <!-- Secure Checkout Notice -->
                            <div class="d-flex align-items-center mb-4 mt-2">
                                <i class="bi bi-shield-lock me-2" style="color: #428677;"></i>
                                <small class="text-muted">{{ __('Secure checkout') }}</small>
                            </div>

                            @if(count($cart) > 0)
                                <div class="d-grid mt-2">
                                    <a href="{{ route('checkout') }}" class="btn btn-primary btn-lg py-3 rounded-3 checkout-btn">
                                        <i class="bi bi-credit-card me-2"></i> {{ __('Proceed to Checkout') }}
                                    </a>
                                </div>

                                <!-- Payment Methods -->
                                <div class="text-center mt-3">
                                    <small class="text-muted">{{ __('We accept:') }}</small>
                                    <div class="mt-2">
                                        <i class="bi bi-credit-card me-2" style="font-size: 1.5rem; color: #6c757d;"></i>
                                        <i class="bi bi-paypal me-2" style="font-size: 1.5rem; color: #6c757d;"></i>
                                        <i class="bi bi-cash-coin" style="font-size: 1.5rem; color: #6c757d;"></i>
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Quantity controls
        const quantityForms = document.querySelectorAll('.quantity-form');

        quantityForms.forEach(form => {
            const decreaseBtn = form.querySelector('.decrease-btn');
            const increaseBtn = form.querySelector('.increase-btn');
            const quantityInput = form.querySelector('.quantity-input');

            decreaseBtn.addEventListener('click', function() {
                let currentValue = parseInt(quantityInput.value);
                if (currentValue > 1) {
                    quantityInput.value = currentValue - 1;
                    form.submit();
                }
            });

            increaseBtn.addEventListener('click', function() {
                let currentValue = parseInt(quantityInput.value);
                if (currentValue < 10) {
                    quantityInput.value = currentValue + 1;
                    form.submit();
                }
            });
        });
    });
</script>
@endpush
