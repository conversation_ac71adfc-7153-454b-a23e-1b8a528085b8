@extends('layouts.admin')

@section('title', $user->name)

@section('content')
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-natryon-primary">
            <i class="bi bi-person-badge me-2"></i>User Profile
        </h1>
        <div>
            <a href="{{ route('admin.users.edit', $user->id) }}" class="btn btn-natryon-primary me-2">
                <i class="bi bi-pencil me-1"></i> Edit
            </a>
            <a href="{{ route('admin.users.index') }}" class="btn btn-outline-natryon-primary">
                <i class="bi bi-arrow-left me-1"></i> Back to Users
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-4">
            <div class="card card-natryon mb-4">
                <div class="card-header bg-natryon-primary text-white py-3">
                    <h5 class="mb-0 fw-bold"><i class="bi bi-person-fill me-2"></i>User Information</h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <div class="avatar-natryon mx-auto mb-3" style="width: 100px; height: 100px; font-size: 2.5rem;">
                            {{ strtoupper(substr($user->name, 0, 1)) }}
                        </div>
                        <h4 class="text-natryon-primary mb-1">{{ $user->name }}</h4>
                        <p class="text-natryon-grey mb-2">{{ $user->email }}</p>
                        <div class="mt-2">
                            @if($user->hasRole('admin'))
                                <span class="badge badge-natryon-blue rounded-pill px-3 py-2">
                                    <i class="bi bi-shield-lock-fill me-1"></i>Admin
                                </span>
                            @elseif($user->hasRole('client'))
                                <span class="badge badge-natryon-secondary rounded-pill px-3 py-2">
                                    <i class="bi bi-person-fill me-1"></i>Client
                                </span>
                            @endif
                        </div>
                    </div>

                    <div class="mb-4">
                        <h6 class="fw-bold text-natryon-primary mb-3"><i class="bi bi-telephone me-2"></i>Contact Information</h6>
                        <div class="list-group list-group-flush">
                            <div class="list-group-item bg-transparent px-0 py-2 d-flex align-items-center">
                                <span class="text-natryon-grey me-3" style="width: 100px;">Email:</span>
                                <span class="fw-medium">{{ $user->email }}</span>
                            </div>
                            <div class="list-group-item bg-transparent px-0 py-2 d-flex align-items-center">
                                <span class="text-natryon-grey me-3" style="width: 100px;">Phone:</span>
                                <span class="fw-medium">{{ $user->phone ?? 'Not provided' }}</span>
                            </div>
                        </div>
                    </div>

                    <div class="mb-4">
                        <h6 class="fw-bold text-natryon-primary mb-3"><i class="bi bi-geo-alt me-2"></i>Address</h6>
                        @if($user->address)
                            <address class="mb-0">
                                {{ $user->address }}<br>
                                {{ $user->city }}@if($user->state), {{ $user->state }}@endif @if($user->zip_code) {{ $user->zip_code }}@endif<br>
                                {{ $user->country }}
                            </address>
                        @else
                            <p class="text-natryon-grey mb-0">No address provided</p>
                        @endif
                    </div>

                    <div class="mb-4">
                        <h6 class="fw-bold text-natryon-primary mb-3"><i class="bi bi-info-circle me-2"></i>Account Details</h6>
                        <div class="list-group list-group-flush">
                            <div class="list-group-item bg-transparent px-0 py-2 d-flex align-items-center">
                                <span class="text-natryon-grey me-3" style="width: 150px;">User ID:</span>
                                <span class="badge badge-natryon-grey rounded-pill">{{ $user->id }}</span>
                            </div>
                            <div class="list-group-item bg-transparent px-0 py-2 d-flex align-items-center">
                                <span class="text-natryon-grey me-3" style="width: 150px;">Registered:</span>
                                <span class="fw-medium">{{ $user->created_at->format('M d, Y H:i') }}</span>
                            </div>
                            <div class="list-group-item bg-transparent px-0 py-2 d-flex align-items-center">
                                <span class="text-natryon-grey me-3" style="width: 150px;">Last Updated:</span>
                                <span class="fw-medium">{{ $user->updated_at->format('M d, Y H:i') }}</span>
                            </div>
                            @if($user->email_verified_at)
                                <div class="list-group-item bg-transparent px-0 py-2 d-flex align-items-center">
                                    <span class="text-natryon-grey me-3" style="width: 150px;">Email Verified:</span>
                                    <span class="fw-medium">{{ $user->email_verified_at->format('M d, Y H:i') }}</span>
                                </div>
                            @endif
                        </div>
                    </div>

                    <div class="d-grid gap-2">
                        <a href="{{ route('admin.users.edit', $user->id) }}" class="btn btn-natryon-primary">
                            <i class="bi bi-pencil me-1"></i> Edit User
                        </a>
                        <button type="button" class="btn btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteModal">
                            <i class="bi bi-trash me-1"></i> Delete User
                        </button>
                    </div>
                </div>
            </div>

            <div class="card card-natryon mb-4">
                <div class="card-header bg-natryon-secondary text-white py-3">
                    <h5 class="mb-0 fw-bold"><i class="bi bi-graph-up me-2"></i>User Statistics</h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6 mb-3">
                            <div class="p-3 rounded" style="background-color: rgba(45, 80, 75, 0.1);">
                                <div class="d-inline-flex align-items-center justify-content-center rounded-circle mb-2" style="width: 50px; height: 50px; background-color: rgba(45, 80, 75, 0.2);">
                                    <i class="bi bi-cart-fill fs-4 text-natryon-primary"></i>
                                </div>
                                <h6 class="text-natryon-grey mb-2">Orders</h6>
                                <h3 class="mb-0 text-natryon-primary">{{ $user->orders->count() }}</h3>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="p-3 rounded" style="background-color: rgba(66, 134, 119, 0.1);">
                                <div class="d-inline-flex align-items-center justify-content-center rounded-circle mb-2" style="width: 50px; height: 50px; background-color: rgba(66, 134, 119, 0.2);">
                                    <i class="bi bi-cash-stack fs-4 text-natryon-secondary"></i>
                                </div>
                                <h6 class="text-natryon-grey mb-2">Total Spent</h6>
                                <h3 class="mb-0 text-natryon-secondary">${{ number_format($user->orders->sum('total'), 2) }}</h3>
                            </div>
                        </div>
                        @if($user->affiliateCodes->count() > 0)
                            <div class="col-6 mb-3">
                                <div class="p-3 rounded" style="background-color: rgba(122, 134, 141, 0.1);">
                                    <div class="d-inline-flex align-items-center justify-content-center rounded-circle mb-2" style="width: 50px; height: 50px; background-color: rgba(122, 134, 141, 0.2);">
                                        <i class="bi bi-people-fill fs-4 text-natryon-grey"></i>
                                    </div>
                                    <h6 class="text-natryon-grey mb-2">Referrals</h6>
                                    <h3 class="mb-0 text-natryon-grey">{{ $user->referral_count ?? 0 }}</h3>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="p-3 rounded" style="background-color: rgba(36, 68, 90, 0.1);">
                                    <div class="d-inline-flex align-items-center justify-content-center rounded-circle mb-2" style="width: 50px; height: 50px; background-color: rgba(36, 68, 90, 0.2);">
                                        <i class="bi bi-cash-coin fs-4 text-natryon-blue"></i>
                                    </div>
                                    <h6 class="text-natryon-grey mb-2">Affiliate Earnings</h6>
                                    <h3 class="mb-0 text-natryon-blue">${{ number_format($user->affiliate_earnings ?? 0, 2) }}</h3>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-8">
            <div class="card card-natryon mb-4">
                <div class="card-header bg-natryon-primary text-white py-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0 fw-bold"><i class="bi bi-cart-fill me-2"></i>Recent Orders</h5>
                        <a href="{{ route('admin.orders.index', ['user' => $user->id]) }}" class="btn btn-sm btn-light">
                            <i class="bi bi-list-ul me-1"></i>View All
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    @if($user->orders->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-natryon table-hover align-middle">
                                <thead>
                                    <tr>
                                        <th>Order #</th>
                                        <th>Date</th>
                                        <th>Status</th>
                                        <th>Total</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($user->orders->take(5) as $order)
                                        <tr>
                                            <td>
                                                <span class="fw-bold text-natryon-primary">{{ $order->order_number }}</span>
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-calendar-date me-2 text-natryon-grey"></i>
                                                    {{ $order->created_at->format('M d, Y') }}
                                                </div>
                                            </td>
                                            <td>
                                                @if($order->status == 'pending')
                                                    <span class="badge badge-natryon-grey rounded-pill px-3 py-2">
                                                        <i class="bi bi-hourglass me-1"></i>Pending
                                                    </span>
                                                @elseif($order->status == 'processing')
                                                    <span class="badge badge-natryon-blue rounded-pill px-3 py-2">
                                                        <i class="bi bi-gear me-1"></i>Processing
                                                    </span>
                                                @elseif($order->status == 'completed')
                                                    <span class="badge badge-natryon-secondary rounded-pill px-3 py-2">
                                                        <i class="bi bi-check-circle me-1"></i>Completed
                                                    </span>
                                                @elseif($order->status == 'declined')
                                                    <span class="badge bg-danger rounded-pill px-3 py-2">
                                                        <i class="bi bi-x-circle me-1"></i>Declined
                                                    </span>
                                                @endif
                                            </td>
                                            <td>
                                                <span class="fw-bold">${{ number_format($order->total, 2) }}</span>
                                            </td>
                                            <td>
                                                <a href="{{ route('admin.orders.show', $order->id) }}" class="btn btn-sm btn-outline-natryon-blue rounded-circle" title="View Order">
                                                    <i class="bi bi-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <div class="mb-3">
                                <span class="d-inline-block p-3 bg-natryon-grey-1 bg-opacity-25 rounded-circle">
                                    <i class="bi bi-cart-x text-natryon-grey" style="font-size: 2rem;"></i>
                                </span>
                            </div>
                            <p class="text-natryon-grey mb-0">No orders yet for this user.</p>
                        </div>
                    @endif
                </div>
            </div>

            @if($user->affiliateCodes->count() > 0)
                <div class="card card-natryon mb-4">
                    <div class="card-header bg-natryon-secondary text-white py-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0 fw-bold"><i class="bi bi-diagram-3 me-2"></i>Affiliate Information</h5>
                            <a href="{{ route('admin.affiliates.show', $user->id) }}" class="btn btn-sm btn-light">
                                <i class="bi bi-list-ul me-1"></i>View Details
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="mb-4">
                            <h6 class="fw-bold text-natryon-primary mb-3"><i class="bi bi-tag me-2"></i>Affiliate Codes</h6>
                            <div class="table-responsive">
                                <table class="table table-natryon table-hover align-middle">
                                    <thead>
                                        <tr>
                                            <th>Code</th>
                                            <th>Commission Rate</th>
                                            <th>Created</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($user->affiliateCodes as $code)
                                            <tr>
                                                <td>
                                                    <span class="badge badge-natryon-blue rounded-pill px-3 py-2">
                                                        <i class="bi bi-tag-fill me-1"></i>{{ $code->code }}
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="badge badge-natryon-secondary rounded-pill px-3 py-2">
                                                        <i class="bi bi-percent me-1"></i>{{ $code->commission_rate }}%
                                                    </span>
                                                </td>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <i class="bi bi-calendar-date me-2 text-natryon-grey"></i>
                                                        {{ $code->created_at->format('M d, Y') }}
                                                    </div>
                                                </td>
                                                <td>
                                                    @if($code->is_active)
                                                        <span class="badge badge-natryon-secondary rounded-pill px-3 py-2">
                                                            <i class="bi bi-check-circle-fill me-1"></i>Active
                                                        </span>
                                                    @else
                                                        <span class="badge bg-danger rounded-pill px-3 py-2">
                                                            <i class="bi bi-x-circle-fill me-1"></i>Inactive
                                                        </span>
                                                    @endif
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <div class="mb-4">
                            <h6 class="fw-bold text-natryon-primary mb-3"><i class="bi bi-cash-coin me-2"></i>Recent Earnings</h6>
                            @if($user->affiliateEarnings->count() > 0)
                                <div class="table-responsive">
                                    <table class="table table-natryon table-hover align-middle">
                                        <thead>
                                            <tr>
                                                <th>Order</th>
                                                <th>Amount</th>
                                                <th>Commission</th>
                                                <th>Date</th>
                                                <th>Status</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($user->affiliateEarnings->take(5) as $earning)
                                                <tr>
                                                    <td>
                                                        <span class="badge badge-natryon-grey rounded-pill px-3 py-2">
                                                            <i class="bi bi-receipt me-1"></i>{{ $earning->order->order_number }}
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <span class="fw-medium">${{ number_format($earning->order_amount, 2) }}</span>
                                                    </td>
                                                    <td>
                                                        <span class="fw-bold text-natryon-secondary">${{ number_format($earning->commission_amount, 2) }}</span>
                                                    </td>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <i class="bi bi-calendar-date me-2 text-natryon-grey"></i>
                                                            {{ $earning->created_at->format('M d, Y') }}
                                                        </div>
                                                    </td>
                                                    <td>
                                                        @if($earning->status == 'pending')
                                                            <span class="badge badge-natryon-grey rounded-pill px-3 py-2">
                                                                <i class="bi bi-hourglass me-1"></i>Pending
                                                            </span>
                                                        @elseif($earning->status == 'approved')
                                                            <span class="badge badge-natryon-secondary rounded-pill px-3 py-2">
                                                                <i class="bi bi-check-circle me-1"></i>Approved
                                                            </span>
                                                        @elseif($earning->status == 'paid')
                                                            <span class="badge badge-natryon-blue rounded-pill px-3 py-2">
                                                                <i class="bi bi-cash-coin me-1"></i>Paid
                                                            </span>
                                                        @elseif($earning->status == 'rejected')
                                                            <span class="badge bg-danger rounded-pill px-3 py-2">
                                                                <i class="bi bi-x-circle me-1"></i>Rejected
                                                            </span>
                                                        @endif
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            @else
                                <div class="text-center py-4">
                                    <div class="mb-3">
                                        <span class="d-inline-block p-3 bg-natryon-grey-1 bg-opacity-25 rounded-circle">
                                            <i class="bi bi-cash text-natryon-grey" style="font-size: 2rem;"></i>
                                        </span>
                                    </div>
                                    <p class="text-natryon-grey mb-0">No affiliate earnings yet for this user.</p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>

    <!-- Delete Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-natryon-primary text-white">
                    <h5 class="modal-title" id="deleteModalLabel">
                        <i class="bi bi-exclamation-triangle-fill me-2"></i>Confirm Delete
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p class="mb-0">Are you sure you want to delete <strong class="text-natryon-primary">{{ $user->name }}</strong>?</p>
                    <p class="text-danger mb-0"><small><i class="bi bi-exclamation-circle me-1"></i>This action cannot be undone and will remove all associated data including orders, affiliate codes, and earnings.</small></p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                        <i class="bi bi-x-circle me-1"></i>Cancel
                    </button>
                    <form action="{{ route('admin.users.destroy', $user->id) }}" method="POST">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-danger delete-confirm" data-name="{{ $user->name }}">
                            <i class="bi bi-trash me-1"></i>Delete
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection
