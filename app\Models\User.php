<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Cashier\Billable;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, Billable, HasRoles;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'address',
        'city',
        'state',
        'zip_code',
        'country',
        'phone',
        'referral_count',
        'affiliate_earnings',
        'affiliate_code',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'affiliate_earnings' => 'decimal:2',
        ];
    }

    /**
     * Get the orders for the user.
     */
    public function orders(): HasMany
    {
        return $this->hasMany(Order::class);
    }

    /**
     * Get the products created by the user (admin).
     */
    public function products(): HasMany
    {
        return $this->hasMany(Product::class);
    }

    /**
     * Get the affiliate codes for the user.
     */
    public function affiliateCodes(): HasMany
    {
        return $this->hasMany(AffiliateCode::class);
    }

    /**
     * Get the affiliate earnings for the user.
     */
    public function affiliateEarnings(): HasMany
    {
        return $this->hasMany(AffiliateEarning::class);
    }

    /**
     * Get the subscriptions for the user.
     */
    public function subscriptions(): HasMany
    {
        return $this->hasMany(Subscription::class);
    }

    /**
     * Check if user is an admin.
     */
    public function isAdmin(): bool
    {
        return $this->hasRole('admin');
    }

    /**
     * Generate a unique affiliate code for the user.
     */
    public function generateAffiliateCode(?int $parentUserId = null): AffiliateCode
    {
        $code = strtoupper(substr($this->name, 0, 3) . substr(md5($this->email . time()), 0, 7));

        return AffiliateCode::create([
            'user_id' => $this->id,
            'code' => $code,
            'parent_user_id' => $parentUserId,
            'commission_rate' => 10.00, // 10% default
        ]);
    }
}
