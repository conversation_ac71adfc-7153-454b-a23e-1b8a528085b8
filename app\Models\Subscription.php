<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Subscription extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'type',
        'stripe_id',
        'stripe_status',
        'stripe_price',
        'quantity',
        'trial_ends_at',
        'ends_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'trial_ends_at' => 'datetime',
        'ends_at' => 'datetime',
    ];

    /**
     * Get the user that owns the subscription.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the subscription items for the subscription.
     */
    public function items(): HasMany
    {
        return $this->hasMany(SubscriptionItem::class);
    }

    /**
     * Determine if the subscription is active.
     *
     * @return bool
     */
    public function isActive(): bool
    {
        return $this->stripe_status === 'active' || $this->stripe_status === 'trialing';
    }

    /**
     * Determine if the subscription is canceled.
     *
     * @return bool
     */
    public function isCanceled(): bool
    {
        return $this->stripe_status === 'canceled' || $this->ends_at !== null;
    }

    /**
     * Determine if the subscription is on trial.
     *
     * @return bool
     */
    public function onTrial(): bool
    {
        return $this->stripe_status === 'trialing';
    }

    /**
     * Determine if the subscription is past due.
     *
     * @return bool
     */
    public function pastDue(): bool
    {
        return $this->stripe_status === 'past_due';
    }

    /**
     * Determine if the subscription is unpaid.
     *
     * @return bool
     */
    public function unpaid(): bool
    {
        return $this->stripe_status === 'unpaid';
    }

    /**
     * Cancel the subscription.
     *
     * @return $this
     */
    public function cancel()
    {
        $this->stripe_status = 'canceled';
        $this->ends_at = now();
        $this->save();

        return $this;
    }
}
