<?php

namespace Database\Seeders;

use App\Models\Currency;
use Illuminate\Database\Seeder;

class CurrencySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $currencies = [
            [
                'code' => 'EUR',
                'name' => 'Euro',
                'symbol' => '€',
                'exchange_rate' => 1.000000, // Base currency
                'is_active' => true,
                'is_default' => true,
            ],
            [
                'code' => 'USD',
                'name' => 'US Dollar',
                'symbol' => '$',
                'exchange_rate' => 1.100000, // 1 EUR = 1.10 USD (approximate)
                'is_active' => true,
                'is_default' => false,
            ],
            [
                'code' => 'XOF',
                'name' => 'CFA Franc BCEAO',
                'symbol' => 'CFA',
                'exchange_rate' => 655.957000, // 1 EUR = 655.957 XOF (fixed rate)
                'is_active' => true,
                'is_default' => false,
            ],
            [
                'code' => 'CNY',
                'name' => 'Chinese Yuan Renminbi',
                'symbol' => '¥',
                'exchange_rate' => 7.800000, // 1 EUR = 7.80 CNY (approximate)
                'is_active' => true,
                'is_default' => false,
            ],
        ];

        foreach ($currencies as $currency) {
            Currency::updateOrCreate(
                ['code' => $currency['code']],
                $currency
            );
        }
    }
}
