@extends('layouts.app')

@section('title', $product->name . ' - NatRyon')

@section('content')
    <!-- Hero Section -->
    <section class="py-5">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h1 class="display-4 fw-bold mb-4">{{ $product->name }}</h1>
                <div class="mb-4">
                    <span class="h2 fw-bold text-primary">${{ number_format($product->price, 2) }}</span>
                    @if($product->allow_subscription && $product->subscription_price)
                        <span class="text-muted ms-2">or ${{ number_format($product->subscription_price, 2) }}/month with subscription</span>
                    @endif
                </div>
                <p class="lead mb-4">{!! nl2br(e($product->description)) !!}</p>
                <form action="{{ route('cart.add') }}" method="POST">
                    @csrf
                    <input type="hidden" name="product_id" value="{{ $product->id }}">
                    <div class="row g-3 align-items-center mb-4">
                        <div class="col-auto">
                            <label for="quantity" class="col-form-label">Quantity:</label>
                        </div>
                        <div class="col-auto">
                            <select name="quantity" id="quantity" class="form-select">
                                @for($i = 1; $i <= 10; $i++)
                                    <option value="{{ $i }}">{{ $i }}</option>
                                @endfor
                            </select>
                        </div>
                        @if($product->allow_subscription && $product->subscription_price)
                            <div class="col-auto">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="is_subscription" id="is_subscription" value="1">
                                    <label class="form-check-label" for="is_subscription">
                                        Subscribe & Save
                                    </label>
                                </div>
                            </div>
                        @endif
                    </div>
                    <div class="d-grid gap-2 d-md-flex">
                        <button type="submit" class="btn btn-primary btn-lg">Add to Cart</button>
                        <a href="{{ route('products.index') }}" class="btn btn-outline-secondary btn-lg">Browse More Products</a>
                    </div>
                </form>
            </div>
            <div class="col-lg-6">
                @if($product->image)
                    <img src="{{ asset('storage/' . $product->image) }}" alt="{{ $product->name }}" class="img-fluid rounded shadow">
                @else
                    <img src="https://via.placeholder.com/600x400?text=NatRyon+{{ $product->name }}" alt="{{ $product->name }}" class="img-fluid rounded shadow">
                @endif
            </div>
        </div>
    </section>

    <!-- Product Details Section -->
    <section class="py-5">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <ul class="nav nav-tabs" id="productTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="description-tab" data-bs-toggle="tab" data-bs-target="#description" type="button" role="tab" aria-controls="description" aria-selected="true">Description</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="ingredients-tab" data-bs-toggle="tab" data-bs-target="#ingredients" type="button" role="tab" aria-controls="ingredients" aria-selected="false">Ingredients</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="benefits-tab" data-bs-toggle="tab" data-bs-target="#benefits" type="button" role="tab" aria-controls="benefits" aria-selected="false">Benefits</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="how-to-use-tab" data-bs-toggle="tab" data-bs-target="#how-to-use" type="button" role="tab" aria-controls="how-to-use" aria-selected="false">How to Use</button>
                    </li>
                </ul>
                <div class="tab-content p-4 border border-top-0 rounded-bottom" id="productTabsContent">
                    <div class="tab-pane fade show active" id="description" role="tabpanel" aria-labelledby="description-tab">
                        {!! nl2br(e($product->description)) !!}
                    </div>
                    <div class="tab-pane fade" id="ingredients" role="tabpanel" aria-labelledby="ingredients-tab">
                        @if($product->ingredients)
                            {!! nl2br(e($product->ingredients)) !!}
                        @else
                            <p>Ingredient information not available.</p>
                        @endif
                    </div>
                    <div class="tab-pane fade" id="benefits" role="tabpanel" aria-labelledby="benefits-tab">
                        @if($product->benefits)
                            {!! nl2br(e($product->benefits)) !!}
                        @else
                            <p>Benefits information not available.</p>
                        @endif
                    </div>
                    <div class="tab-pane fade" id="how-to-use" role="tabpanel" aria-labelledby="how-to-use-tab">
                        @if($product->how_to_use)
                            {!! nl2br(e($product->how_to_use)) !!}
                        @else
                            <p>Usage information not available.</p>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Benefits Section -->
    <section class="py-5 bg-light">
        <div class="text-center mb-5">
            <h2 class="fw-bold">Key Benefits</h2>
            <p class="lead">Experience the difference with {{ $product->name }}</p>
        </div>
        <div class="row g-4">
            <div class="col-md-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body text-center">
                        <div class="mb-3">
                            <i class="bi bi-lightning-charge text-primary" style="font-size: 2.5rem;"></i>
                        </div>
                        <h5 class="card-title">Increased Energy</h5>
                        <p class="card-text">Feel more energetic throughout the day with our nutrient-rich formula designed to combat fatigue.</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body text-center">
                        <div class="mb-3">
                            <i class="bi bi-shield-plus text-primary" style="font-size: 2.5rem;"></i>
                        </div>
                        <h5 class="card-title">Immune Support</h5>
                        <p class="card-text">Strengthen your immune system with essential vitamins, minerals, and antioxidants.</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body text-center">
                        <div class="mb-3">
                            <i class="bi bi-heart-pulse text-primary" style="font-size: 2.5rem;"></i>
                        </div>
                        <h5 class="card-title">Digestive Health</h5>
                        <p class="card-text">Support optimal digestion and gut health with our blend of probiotics and digestive enzymes.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section class="py-5">
        <div class="text-center mb-5">
            <h2 class="fw-bold">Customer Reviews</h2>
            <p class="lead">See what others are saying about {{ $product->name }}</p>
        </div>
        <div class="row g-4">
            <div class="col-md-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body">
                        <div class="mb-3 text-warning">
                            <i class="bi bi-star-fill"></i>
                            <i class="bi bi-star-fill"></i>
                            <i class="bi bi-star-fill"></i>
                            <i class="bi bi-star-fill"></i>
                            <i class="bi bi-star-fill"></i>
                        </div>
                        <p class="card-text">"I've been using {{ $product->name }} for 3 months now and I've noticed a significant improvement in my energy levels and overall health. It's become an essential part of my morning routine!"</p>
                        <div class="d-flex align-items-center mt-3">
                            <div class="flex-shrink-0">
                                <img src="https://via.placeholder.com/50x50" class="rounded-circle" alt="Customer">
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-0">Sarah Johnson</h6>
                                <small class="text-muted">Verified Buyer</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body">
                        <div class="mb-3 text-warning">
                            <i class="bi bi-star-fill"></i>
                            <i class="bi bi-star-fill"></i>
                            <i class="bi bi-star-fill"></i>
                            <i class="bi bi-star-fill"></i>
                            <i class="bi bi-star-fill"></i>
                        </div>
                        <p class="card-text">"As a busy professional, I don't always have time for proper meals. {{ $product->name }} helps me ensure I'm getting the nutrients I need even on my busiest days. The taste is great too!"</p>
                        <div class="d-flex align-items-center mt-3">
                            <div class="flex-shrink-0">
                                <img src="https://via.placeholder.com/50x50" class="rounded-circle" alt="Customer">
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-0">Michael Chen</h6>
                                <small class="text-muted">Verified Buyer</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body">
                        <div class="mb-3 text-warning">
                            <i class="bi bi-star-fill"></i>
                            <i class="bi bi-star-fill"></i>
                            <i class="bi bi-star-fill"></i>
                            <i class="bi bi-star-fill"></i>
                            <i class="bi bi-star-fill"></i>
                        </div>
                        <p class="card-text">"I've tried many greens powders over the years, but {{ $product->name }} stands out for its quality and effectiveness. My digestion has improved and I feel more energetic throughout the day."</p>
                        <div class="d-flex align-items-center mt-3">
                            <div class="flex-shrink-0">
                                <img src="https://via.placeholder.com/50x50" class="rounded-circle" alt="Customer">
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-0">Emily Rodriguez</h6>
                                <small class="text-muted">Verified Buyer</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="py-5 bg-primary text-white">
        <div class="text-center">
            <h2 class="fw-bold mb-3">Ready to Transform Your Health?</h2>
            <p class="lead mb-4">Join thousands of satisfied customers who have made {{ $product->name }} part of their daily routine.</p>
            <a href="#" class="btn btn-light btn-lg" onclick="event.preventDefault(); document.getElementById('add-to-cart-form').submit();">Get {{ $product->name }} Now</a>
            <form id="add-to-cart-form" action="{{ route('cart.add') }}" method="POST" class="d-none">
                @csrf
                <input type="hidden" name="product_id" value="{{ $product->id }}">
                <input type="hidden" name="quantity" value="1">
            </form>
        </div>
    </section>
@endsection
