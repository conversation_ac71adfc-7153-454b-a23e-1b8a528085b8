@extends('layouts.app')

@section('title', 'My Subscriptions - NATRYON')

@section('content')
    <!-- Hero Section -->
    <section class="py-4">
        <div class="container">
            <div class="d-flex flex-column flex-md-row justify-content-between align-items-md-center mb-4 gap-3">
                <div>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-2">
                            <li class="breadcrumb-item"><a href="{{ route('home') }}" class="text-decoration-none">Home</a></li>
                            <li class="breadcrumb-item active" aria-current="page">My Subscriptions</li>
                        </ol>
                    </nav>
                    <h1 class="h3 mb-0 text-primary fw-bold">My Subscriptions</h1>
                </div>
            </div>

            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    {{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            @endif

            @if(session('error'))
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    {{ session('error') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            @endif

            @if(count($subscriptions) > 0)
                <div class="row g-4">
                    @foreach($subscriptions as $subscription)
                        <div class="col-md-6 col-lg-4">
                            <div class="card border-0 shadow-sm h-100">
                                <div class="card-header bg-white py-3 d-flex justify-content-between align-items-center">
                                    <h5 class="mb-0 text-primary">
                                        <i class="bi bi-arrow-repeat me-2"></i>{{ $subscription->type }}
                                    </h5>
                                    <span class="badge {{ $subscription->stripe_status === 'active' ? 'bg-success' : ($subscription->stripe_status === 'canceled' ? 'bg-danger' : 'bg-warning text-dark') }} rounded-pill">
                                        {{ ucfirst($subscription->stripe_status) }}
                                    </span>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <small class="text-muted d-block">Subscription ID</small>
                                        <p class="mb-0 fw-medium">{{ $subscription->stripe_id }}</p>
                                    </div>

                                    <div class="mb-3">
                                        <small class="text-muted d-block">Started On</small>
                                        <p class="mb-0">{{ $subscription->created_at->format('M d, Y') }}</p>
                                    </div>

                                    @if($subscription->ends_at)
                                        <div class="mb-3">
                                            <small class="text-muted d-block">Ends On</small>
                                            <p class="mb-0">{{ $subscription->ends_at->format('M d, Y') }}</p>
                                        </div>
                                    @endif

                                    @if($subscription->stripe_status === 'active')
                                        <div class="d-grid gap-2 mt-4">
                                            <button type="button" class="btn btn-outline-danger"
                                                   onclick="confirmCancelSubscription('{{ $subscription->id }}', '{{ $subscription->type }}')">
                                                <i class="bi bi-x-circle me-2"></i>Cancel Subscription
                                            </button>

                                            <form id="cancel-subscription-form-{{ $subscription->id }}"
                                                  action="{{ route('subscriptions.cancel', $subscription->id) }}"
                                                  method="POST"
                                                  style="display: none;">
                                                @csrf
                                            </form>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            @else
                <div class="card border-0 shadow-sm">
                    <div class="card-body text-center py-5">
                        <div class="mb-4">
                            <i class="bi bi-arrow-repeat text-muted" style="font-size: 3rem;"></i>
                        </div>
                        <h4>No Active Subscriptions</h4>
                        <p class="text-muted mb-4">You don't have any active subscriptions at the moment.</p>
                        <a href="{{ route('products.index') }}" class="btn btn-primary rounded-pill">
                            <i class="bi bi-bag me-2"></i>Browse Products
                        </a>
                    </div>
                </div>
            @endif
        </div>
    </section>
@endsection

@push('styles')
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css">
<style>
    .badge {
        font-weight: 500;
        padding: 0.5em 0.8em;
    }

    .btn-primary {
        background-color: #428677;
        border-color: #428677;
    }

    .btn-primary:hover, .btn-primary:focus {
        background-color: #2d504b;
        border-color: #2d504b;
    }

    .btn-outline-danger {
        transition: all 0.3s ease;
    }

    .btn-outline-danger:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    .text-primary {
        color: #428677 !important;
    }

    .bg-primary {
        background-color: #428677 !important;
    }

    .breadcrumb-item a {
        color: #428677;
    }

    .breadcrumb-item.active {
        color: #7a868d;
    }

    .breadcrumb-item + .breadcrumb-item::before {
        color: #b1b7b8;
    }

    /* Toast Notification Styling */
    .toastify {
        padding: 16px 20px;
        border-radius: 8px;
        font-weight: 500;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    }

    .toast-success {
        background: linear-gradient(135deg, #428677, #2d504b);
    }

    .toast-error {
        background: linear-gradient(135deg, #e74c3c, #c0392b);
    }

    .toast-info {
        background: linear-gradient(135deg, #3498db, #2980b9);
    }

    .toast-warning {
        background: linear-gradient(135deg, #f39c12, #e67e22);
    }
</style>
@endpush

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/toastify-js"></script>
<script>
    // Show notification when page loads
    document.addEventListener('DOMContentLoaded', function() {
        showNotification('Subscription management loaded successfully', 'info');
    });

    // Function to show notifications
    function showNotification(message, type = 'info') {
        const toastClass = type === 'success' ? 'toast-success' :
                          type === 'error' ? 'toast-error' :
                          type === 'warning' ? 'toast-warning' : 'toast-info';

        Toastify({
            text: message,
            duration: 5000,
            close: true,
            gravity: "top",
            position: "right",
            className: toastClass,
            stopOnFocus: true
        }).showToast();
    }

    // Function to confirm subscription cancellation
    function confirmCancelSubscription(subscriptionId, subscriptionType) {
        // Create a modal dialog for confirmation
        const modalHtml = `
            <div class="modal fade" id="cancelSubscriptionModal" tabindex="-1" aria-labelledby="cancelSubscriptionModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header bg-light">
                            <h5 class="modal-title" id="cancelSubscriptionModalLabel">Cancel Subscription</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="text-center mb-4">
                                <i class="bi bi-exclamation-triangle text-warning" style="font-size: 3rem;"></i>
                            </div>
                            <p>Are you sure you want to cancel your <strong>${subscriptionType}</strong> subscription?</p>
                            <p>This action cannot be undone. You will no longer receive regular shipments of this product.</p>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Keep Subscription</button>
                            <button type="button" class="btn btn-danger" id="confirmCancelBtn">
                                <i class="bi bi-x-circle me-1"></i>Yes, Cancel Subscription
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Add the modal to the document
        const modalContainer = document.createElement('div');
        modalContainer.innerHTML = modalHtml;
        document.body.appendChild(modalContainer);

        // Initialize the modal
        const modal = new bootstrap.Modal(document.getElementById('cancelSubscriptionModal'));
        modal.show();

        // Handle confirmation
        document.getElementById('confirmCancelBtn').addEventListener('click', function() {
            // Show processing notification
            showNotification('Processing your cancellation request...', 'info');

            // Hide the modal
            modal.hide();

            // Submit the cancellation form
            document.getElementById(`cancel-subscription-form-${subscriptionId}`).submit();
        });

        // Clean up the modal when it's hidden
        document.getElementById('cancelSubscriptionModal').addEventListener('hidden.bs.modal', function() {
            document.body.removeChild(modalContainer);
        });
    }
</script>
@endpush
