<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Checkpoint;
use App\Models\Release;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class CheckpointController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $checkpoints = Checkpoint::with(['release', 'verifier'])
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return view('admin.checkpoints.index', compact('checkpoints'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $releases = Release::where('status', 'draft')
            ->orderBy('created_at', 'desc')
            ->get();

        $checkpointTypes = [
            'unit_test' => 'Unit Test',
            'integration_test' => 'Integration Test',
            'manual_verification' => 'Manual Verification',
            'automated_test' => 'Automated Test',
            'security_check' => 'Security Check',
            'performance_test' => 'Performance Test',
        ];

        return view('admin.checkpoints.create', compact('releases', 'checkpointTypes'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'release_id' => 'required|exists:releases,id',
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'type' => 'required|in:unit_test,integration_test,manual_verification,automated_test,security_check,performance_test',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Check if the release is in draft status
        $release = Release::findOrFail($request->release_id);
        if ($release->status !== 'draft') {
            return redirect()->back()
                ->with('error', 'Checkpoints can only be added to releases in draft status.')
                ->withInput();
        }

        $checkpoint = new Checkpoint();
        $checkpoint->release_id = $request->release_id;
        $checkpoint->name = $request->name;
        $checkpoint->description = $request->description;
        $checkpoint->type = $request->type;
        $checkpoint->status = 'pending';
        $checkpoint->save();

        return redirect()->route('admin.releases.show', $checkpoint->release_id)
            ->with('success', 'Checkpoint created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $checkpoint = Checkpoint::with(['release', 'verifier'])
            ->findOrFail($id);

        return view('admin.checkpoints.show', compact('checkpoint'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $checkpoint = Checkpoint::findOrFail($id);

        // Only allow editing of checkpoints in pending status and for releases in draft status
        if ($checkpoint->status !== 'pending' || $checkpoint->release->status !== 'draft') {
            return redirect()->route('admin.checkpoints.show', $checkpoint->id)
                ->with('error', 'Only pending checkpoints for draft releases can be edited.');
        }

        $checkpointTypes = [
            'unit_test' => 'Unit Test',
            'integration_test' => 'Integration Test',
            'manual_verification' => 'Manual Verification',
            'automated_test' => 'Automated Test',
            'security_check' => 'Security Check',
            'performance_test' => 'Performance Test',
        ];

        return view('admin.checkpoints.edit', compact('checkpoint', 'checkpointTypes'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $checkpoint = Checkpoint::findOrFail($id);

        // Only allow updating of checkpoints in pending status and for releases in draft status
        if ($checkpoint->status !== 'pending' || $checkpoint->release->status !== 'draft') {
            return redirect()->route('admin.checkpoints.show', $checkpoint->id)
                ->with('error', 'Only pending checkpoints for draft releases can be updated.');
        }

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'type' => 'required|in:unit_test,integration_test,manual_verification,automated_test,security_check,performance_test',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $checkpoint->name = $request->name;
        $checkpoint->description = $request->description;
        $checkpoint->type = $request->type;
        $checkpoint->save();

        return redirect()->route('admin.checkpoints.show', $checkpoint->id)
            ->with('success', 'Checkpoint updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $checkpoint = Checkpoint::findOrFail($id);

        // Only allow deletion of checkpoints for releases in draft status
        if ($checkpoint->release->status !== 'draft') {
            return redirect()->route('admin.checkpoints.show', $checkpoint->id)
                ->with('error', 'Only checkpoints for draft releases can be deleted.');
        }

        $releaseId = $checkpoint->release_id;
        $checkpoint->delete();

        return redirect()->route('admin.releases.show', $releaseId)
            ->with('success', 'Checkpoint deleted successfully.');
    }

    /**
     * Mark the checkpoint as in progress.
     */
    public function startVerification(string $id)
    {
        $checkpoint = Checkpoint::findOrFail($id);

        // Only allow starting verification of checkpoints in pending status and for releases in testing status
        if ($checkpoint->status !== 'pending' || $checkpoint->release->status !== 'testing') {
            return redirect()->route('admin.checkpoints.show', $checkpoint->id)
                ->with('error', 'Only pending checkpoints for releases in testing status can be started.');
        }

        $checkpoint->markAsInProgress();

        return redirect()->route('admin.checkpoints.show', $checkpoint->id)
            ->with('success', 'Checkpoint verification started.');
    }

    /**
     * Mark the checkpoint as passed.
     */
    public function pass(Request $request, string $id)
    {
        $checkpoint = Checkpoint::findOrFail($id);

        // Only allow passing of checkpoints for releases in testing status
        if ($checkpoint->release->status !== 'testing') {
            return redirect()->route('admin.checkpoints.show', $checkpoint->id)
                ->with('error', 'Only checkpoints for releases in testing status can be passed.');
        }

        $validator = Validator::make($request->all(), [
            'verification_notes' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $checkpoint->markAsPassed($request->verification_notes, Auth::id());

        return redirect()->route('admin.releases.show', $checkpoint->release_id)
            ->with('success', 'Checkpoint marked as passed.');
    }

    /**
     * Mark the checkpoint as failed.
     */
    public function fail(Request $request, string $id)
    {
        $checkpoint = Checkpoint::findOrFail($id);

        // Only allow failing of checkpoints for releases in testing status
        if ($checkpoint->release->status !== 'testing') {
            return redirect()->route('admin.checkpoints.show', $checkpoint->id)
                ->with('error', 'Only checkpoints for releases in testing status can be failed.');
        }

        $validator = Validator::make($request->all(), [
            'verification_notes' => 'required|string',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $checkpoint->markAsFailed($request->verification_notes, Auth::id());

        return redirect()->route('admin.releases.show', $checkpoint->release_id)
            ->with('success', 'Checkpoint marked as failed.');
    }
}
