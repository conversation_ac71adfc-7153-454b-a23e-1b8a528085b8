<?php

namespace App\Http\Controllers;

use App\Models\AffiliateCode;
use App\Models\Category;
use App\Models\Product;
use App\Models\ShippingOption;
use Artesaos\SEOTools\Facades\SEOMeta;
use Artesaos\SEOTools\Facades\OpenGraph;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class CartController extends Controller
{
    /**
     * Display the cart.
     */
    public function index()
    {
        // Set SEO metadata
        SEOMeta::setTitle('Your Cart - NATRYON');
        SEOMeta::setDescription('Review your cart items before checkout.');
        SEOMeta::setCanonical(url('/cart'));

        OpenGraph::setTitle('Your Cart - NATRYON');
        OpenGraph::setDescription('Review your cart items before checkout.');
        OpenGraph::setUrl(url('/cart'));
        OpenGraph::addProperty('type', 'website');

        // Get the cart from the session
        $cart = session()->get('cart', []);

        // Update cart items to ensure they have slugs
        $updatedCart = [];
        foreach ($cart as $id => $item) {
            // If the item doesn't have a slug, try to fetch it from the database
            if (!isset($item['slug']) && isset($item['id'])) {
                $product = Product::find($item['id']);
                if ($product) {
                    $item['slug'] = $product->slug;
                } else {
                    $item['slug'] = 'natryon'; // Default fallback
                }
            }
            $updatedCart[$id] = $item;
        }

        // Save the updated cart back to the session
        if ($cart !== $updatedCart) {
            session()->put('cart', $updatedCart);
            $cart = $updatedCart;
        }

        // Calculate cart totals
        $subtotal = 0;
        foreach ($cart as $item) {
            $subtotal += $item['price'] * $item['quantity'];
        }

        // Get affiliate code from session or user
        $affiliateCode = session()->get('affiliate_code');

        // If user is logged in and no affiliate code in session, use their registration code
        if (!$affiliateCode && Auth::check() && Auth::user()->affiliate_code) {
            $affiliateCode = Auth::user()->affiliate_code;
            session()->put('affiliate_code', $affiliateCode);
        }

        if ($affiliateCode) {
            // Verify the affiliate code is valid
            $code = AffiliateCode::where('code', $affiliateCode)
                ->where('is_active', true)
                ->first();

            if (!$code) {
                // Invalid code, remove from session
                session()->forget('affiliate_code');
                $affiliateCode = null;
            }
        }

        $total = $subtotal;

        // Get recommended products for "You Might Also Like" section
        $recommendedProducts = $this->getRecommendedProducts($cart);

        return view('cart.index', compact('cart', 'subtotal', 'total', 'affiliateCode', 'recommendedProducts'));
    }

    /**
     * Apply an affiliate code to the cart.
     */
    public function applyAffiliateCode(Request $request)
    {
        $request->validate([
            'affiliate_code' => 'required|string',
        ]);

        // Verify the affiliate code is valid
        $code = AffiliateCode::where('code', $request->affiliate_code)
            ->where('is_active', true)
            ->first();

        if (!$code) {
            return back()->with('error', 'Invalid affiliate code.');
        }

        // Store the affiliate code in the session
        session()->put('affiliate_code', $request->affiliate_code);

        return back()->with('success', 'Affiliate code applied successfully!');
    }

    /**
     * Remove the affiliate code from the cart.
     */
    public function removeAffiliateCode()
    {
        session()->forget('affiliate_code');

        return back()->with('success', 'Affiliate code removed successfully!');
    }

    /**
     * Clear the cart.
     */
    public function clear()
    {
        session()->forget('cart');

        return back()->with('success', 'Cart cleared successfully!');
    }

    /**
     * Proceed to checkout.
     */
    public function checkout()
    {
        // Set SEO metadata
        SEOMeta::setTitle('Checkout - NATRYON');
        SEOMeta::setDescription('Complete your purchase of NATRYON products.');
        SEOMeta::setCanonical(url('/checkout'));

        OpenGraph::setTitle('Checkout - NATRYON');
        OpenGraph::setDescription('Complete your purchase of NATRYON products.');
        OpenGraph::setUrl(url('/checkout'));
        OpenGraph::addProperty('type', 'website');

        // Get the cart from the session
        $cart = session()->get('cart', []);

        // Redirect to cart if empty
        if (empty($cart)) {
            return redirect()->route('cart.index')->with('error', 'Your cart is empty.');
        }

        // Calculate cart totals
        $subtotal = 0;
        foreach ($cart as $item) {
            $subtotal += $item['price'] * $item['quantity'];
        }

        // Get affiliate code from session or user
        $affiliateCode = session()->get('affiliate_code');

        // If user is logged in and no affiliate code in session, use their registration code
        if (!$affiliateCode && Auth::check() && Auth::user()->affiliate_code) {
            $affiliateCode = Auth::user()->affiliate_code;
            session()->put('affiliate_code', $affiliateCode);
        }

        if ($affiliateCode) {
            // Verify the affiliate code is valid
            $code = AffiliateCode::where('code', $affiliateCode)
                ->where('is_active', true)
                ->first();

            if (!$code) {
                // Invalid code, remove from session
                session()->forget('affiliate_code');
                $affiliateCode = null;
            }
        }

        // Get shipping options
        $shippingOptions = ShippingOption::where('is_active', true)
            ->orderBy('display_order')
            ->get();

        // Get selected shipping option from session or use the first one
        $selectedShippingId = session()->get('selected_shipping_id');
        $selectedShipping = null;

        if ($selectedShippingId) {
            $selectedShipping = $shippingOptions->firstWhere('id', $selectedShippingId);
        }

        // If no shipping option is selected or the selected one is not available, use the first one
        if (!$selectedShipping && $shippingOptions->isNotEmpty()) {
            $selectedShipping = $shippingOptions->first();
            session()->put('selected_shipping_id', $selectedShipping->id);
        }

        // Calculate shipping cost
        $shippingCost = $selectedShipping ? $selectedShipping->cost : 0;

        // Calculate total
        $total = $subtotal + $shippingCost;

        return view('cart.checkout', compact(
            'cart',
            'subtotal',
            'shippingOptions',
            'selectedShipping',
            'shippingCost',
            'total',
            'affiliateCode'
        ));
    }

    /**
     * Update the shipping option.
     */
    public function updateShipping(Request $request)
    {
        $request->validate([
            'shipping_id' => 'required|exists:shipping_options,id',
        ]);

        // Store the selected shipping option in the session
        session()->put('selected_shipping_id', $request->shipping_id);

        return back()->with('success', 'Shipping option updated successfully!');
    }

    /**
     * Get recommended products based on cart items.
     *
     * @param array $cart The cart items
     * @return \Illuminate\Database\Eloquent\Collection
     */
    private function getRecommendedProducts(array $cart)
    {
        // If cart is empty, return featured products
        if (empty($cart)) {
            return Product::where('is_active', true)
                ->where('is_featured', true)
                ->take(3)
                ->get();
        }

        // Get product IDs from cart
        $productIds = [];
        $categoryIds = [];

        foreach ($cart as $item) {
            if (isset($item['id'])) {
                $productIds[] = $item['id'];

                // Get product category
                $product = Product::find($item['id']);
                if ($product && $product->category_id) {
                    $categoryIds[] = $product->category_id;
                }
            }
        }

        // Get products from the same categories but not in cart
        $recommendedProducts = Product::where('is_active', true)
            ->whereNotIn('id', $productIds)
            ->where(function($query) use ($categoryIds) {
                if (!empty($categoryIds)) {
                    $query->whereIn('category_id', $categoryIds);
                }
            })
            ->inRandomOrder()
            ->take(3)
            ->get();

        // If we don't have enough products, add some featured products
        if ($recommendedProducts->count() < 3) {
            $additionalProducts = Product::where('is_active', true)
                ->where('is_featured', true)
                ->whereNotIn('id', $productIds)
                ->whereNotIn('id', $recommendedProducts->pluck('id')->toArray())
                ->inRandomOrder()
                ->take(3 - $recommendedProducts->count())
                ->get();

            $recommendedProducts = $recommendedProducts->concat($additionalProducts);
        }

        // If we still don't have enough, just get random active products
        if ($recommendedProducts->count() < 3) {
            $moreProducts = Product::where('is_active', true)
                ->whereNotIn('id', $productIds)
                ->whereNotIn('id', $recommendedProducts->pluck('id')->toArray())
                ->inRandomOrder()
                ->take(3 - $recommendedProducts->count())
                ->get();

            $recommendedProducts = $recommendedProducts->concat($moreProducts);
        }

        return $recommendedProducts;
    }
}
