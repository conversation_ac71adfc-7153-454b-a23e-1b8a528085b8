@extends('layouts.admin')

@section('title', 'Order #' . $order->order_number)

@push('styles')
<style>
    :root {
        --natryon-cream: #F6E8D2;       /* Light cream color */
        --natryon-cream-dark: #E8D7B9;  /* Darker cream for hover states */
        --natryon-cream-light: #FAF2E5; /* Lighter cream for backgrounds */
        --natryon-green: #024C3D;       /* Deep green color */
        --natryon-green-dark: #013C30;  /* Darker green for hover states */
        --natryon-green-light: #0A6A56; /* Lighter green for accents */
        --natryon-green-pale: #E8F4F1;  /* Very light green for backgrounds */
        --natryon-text: #1A2E29;        /* Dark text color */
        --natryon-text-light: #4D6661;  /* Light text color */
        --natryon-border: #E0D5C0;      /* Border color */
    }

    /* General Styles */
    body {
        color: var(--natryon-text);
        background-color: #FAFAFA;
    }

    .text-primary {
        color: var(--natryon-green) !important;
    }

    .bg-primary {
        background-color: var(--natryon-green) !important;
    }

    .text-secondary {
        color: var(--natryon-cream) !important;
    }

    .bg-secondary {
        background-color: var(--natryon-cream) !important;
    }

    .text-success {
        color: var(--natryon-green-light) !important;
    }

    .bg-success {
        background-color: var(--natryon-green-light) !important;
    }

    .text-info {
        color: var(--natryon-text) !important;
    }

    .bg-info {
        background-color: var(--natryon-text) !important;
    }

    .text-warning {
        color: #d9a84e !important;
    }

    .text-muted {
        color: var(--natryon-text-light) !important;
    }

    .bg-light {
        background-color: var(--natryon-cream-light) !important;
    }

    /* Button Styles */
    .btn {
        font-weight: 500;
        letter-spacing: 0.3px;
        padding: 0.6rem 1.5rem;
        border-radius: 0.5rem;
        transition: all 0.25s ease;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
    }

    .btn-sm {
        padding: 0.4rem 1rem;
        font-size: 0.875rem;
    }

    .btn-lg {
        padding: 0.8rem 2rem;
        font-size: 1.1rem;
    }

    .btn-primary {
        background-color: var(--natryon-green);
        border-color: var(--natryon-green);
        color: white;
    }

    .btn-primary:hover, .btn-primary:focus {
        background-color: var(--natryon-green-dark);
        border-color: var(--natryon-green-dark);
        color: white;
        box-shadow: 0 4px 10px rgba(2, 76, 61, 0.2);
        transform: translateY(-1px);
    }

    .btn-secondary {
        background-color: var(--natryon-cream);
        border-color: var(--natryon-cream);
        color: var(--natryon-text);
    }

    .btn-secondary:hover, .btn-secondary:focus {
        background-color: var(--natryon-cream-dark);
        border-color: var(--natryon-cream-dark);
        color: var(--natryon-text);
        box-shadow: 0 4px 10px rgba(230, 217, 190, 0.3);
        transform: translateY(-1px);
    }

    .btn-outline-primary {
        color: var(--natryon-green);
        border-color: var(--natryon-green);
        background-color: transparent;
    }

    .btn-outline-primary:hover, .btn-outline-primary:focus {
        background-color: var(--natryon-green);
        border-color: var(--natryon-green);
        color: white;
        box-shadow: 0 4px 10px rgba(2, 76, 61, 0.15);
        transform: translateY(-1px);
    }

    .btn-outline-secondary {
        color: var(--natryon-text);
        border-color: var(--natryon-cream-dark);
        background-color: transparent;
    }

    .btn-outline-secondary:hover, .btn-outline-secondary:focus {
        background-color: var(--natryon-cream);
        border-color: var(--natryon-cream);
        color: var(--natryon-text);
        box-shadow: 0 4px 10px rgba(230, 217, 190, 0.2);
        transform: translateY(-1px);
    }

    .btn-danger {
        background-color: #dc3545;
        border-color: #dc3545;
    }

    .btn-danger:hover, .btn-danger:focus {
        background-color: #bb2d3b;
        border-color: #bb2d3b;
        box-shadow: 0 4px 10px rgba(220, 53, 69, 0.2);
        transform: translateY(-1px);
    }

    .btn-outline-danger {
        color: #dc3545;
        border-color: #dc3545;
        background-color: transparent;
    }

    .btn-outline-danger:hover, .btn-outline-danger:focus {
        background-color: #dc3545;
        border-color: #dc3545;
        color: white;
        box-shadow: 0 4px 10px rgba(220, 53, 69, 0.15);
        transform: translateY(-1px);
    }

    /* Card Styles */
    .card {
        border-radius: 0.75rem;
        border: none;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
        transition: all 0.3s ease;
        overflow: hidden;
        background-color: white;
    }

    .card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.07);
    }

    .card-header {
        padding: 1.25rem 1.5rem;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        background-color: white;
    }

    .card-body {
        padding: 1.5rem;
    }

    .rounded-4 {
        border-radius: 0.75rem !important;
    }

    /* Avatar Styles */
    .avatar-circle {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        font-size: 16px;
        background-color: var(--natryon-green-pale);
        color: var(--natryon-green);
        box-shadow: 0 2px 8px rgba(2, 76, 61, 0.08);
    }

    .avatar-circle-lg {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        font-size: 32px;
        background-color: var(--natryon-green-pale);
        color: var(--natryon-green);
        box-shadow: 0 4px 16px rgba(2, 76, 61, 0.1);
        margin-bottom: 1.25rem;
    }

    /* Status Badge Styles */
    .status-badge {
        padding: 0.4rem 1rem;
        border-radius: 0.5rem;
        font-weight: 500;
        font-size: 0.85rem;
        display: inline-flex;
        align-items: center;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.06);
        letter-spacing: 0.3px;
    }

    .status-badge i {
        margin-right: 0.5rem;
        font-size: 0.9rem;
    }

    .status-badge.pending {
        background-color: rgba(255, 193, 7, 0.15);
        color: #997404;
        border: 1px solid rgba(255, 193, 7, 0.3);
    }

    .status-badge.processing {
        background-color: rgba(13, 110, 253, 0.15);
        color: #0a58ca;
        border: 1px solid rgba(13, 110, 253, 0.3);
    }

    .status-badge.completed {
        background-color: rgba(2, 76, 61, 0.15);
        color: var(--natryon-green);
        border: 1px solid rgba(2, 76, 61, 0.3);
    }

    .status-badge.declined {
        background-color: rgba(220, 53, 69, 0.15);
        color: #b02a37;
        border: 1px solid rgba(220, 53, 69, 0.3);
    }

    .status-badge.paid {
        background-color: rgba(2, 76, 61, 0.15);
        color: var(--natryon-green);
        border: 1px solid rgba(2, 76, 61, 0.3);
    }

    .status-badge.unpaid {
        background-color: rgba(255, 193, 7, 0.15);
        color: #997404;
        border: 1px solid rgba(255, 193, 7, 0.3);
    }

    /* Progress Bar Styles */
    .progress-container {
        height: 6px;
        background-color: #f1f1f1;
        border-radius: 3px;
        margin-bottom: 1rem;
        overflow: hidden;
        box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
    }

    .progress-bar {
        height: 100%;
        border-radius: 3px;
        transition: width 0.5s ease;
    }

    .progress-bar.pending {
        background: linear-gradient(to right, #ffd166, #ffb347);
        width: 25%;
    }

    .progress-bar.processing {
        background: linear-gradient(to right, #4dabf7, #228be6);
        width: 50%;
    }

    .progress-bar.completed {
        background: linear-gradient(to right, #0A6A56, #024C3D);
        width: 100%;
    }

    .progress-bar.declined {
        background: linear-gradient(to right, #fa5252, #e03131);
        width: 100%;
    }

    .progress-bar.paid {
        background: linear-gradient(to right, #0A6A56, #024C3D);
        width: 100%;
    }

    .progress-bar.unpaid {
        background: linear-gradient(to right, #ffd166, #ffb347);
        width: 25%;
    }

    /* Status Steps Styles */
    .status-steps {
        margin-top: 0.75rem;
        margin-bottom: 1.5rem;
    }

    .status-step {
        font-size: 0.75rem;
        color: var(--natryon-text-light);
        position: relative;
        font-weight: 500;
    }

    .status-step.active {
        color: var(--natryon-green);
        font-weight: 600;
    }

    /* Payment Info Styles */
    .payment-info {
        background-color: white;
        border-radius: 0.75rem;
        padding: 1.25rem;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        border: 1px solid var(--natryon-border);
    }

    .payment-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: var(--natryon-green-pale);
        color: var(--natryon-green);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.1rem;
        box-shadow: 0 2px 6px rgba(2, 76, 61, 0.08);
    }

    .payment-method {
        font-weight: 600;
        color: var(--natryon-text);
        font-size: 0.95rem;
    }

    .payment-date {
        font-size: 0.8rem;
        color: var(--natryon-text-light);
        margin-top: 0.25rem;
    }

    /* Premium Card Styles */
    .premium-card {
        background-color: white;
        border-radius: 0.75rem;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
        overflow: hidden;
        transition: all 0.25s ease;
        border: 1px solid rgba(0, 0, 0, 0.05);
        margin-bottom: 1.5rem;
    }

    .premium-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.07);
    }

    .premium-card-header {
        padding: 1.25rem 1.5rem;
        background: var(--natryon-green);
        color: white;
        position: relative;
        overflow: hidden;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .premium-card-header::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='rgba(255,255,255,0.05)' fill-rule='evenodd'/%3E%3C/svg%3E");
        opacity: 0.2;
    }

    .premium-card-body {
        padding: 1.5rem;
    }

    .premium-card-header h5 {
        font-size: 1.1rem;
        font-weight: 600;
        margin: 0;
        letter-spacing: 0.3px;
    }

    /* Info Card Styles */
    .info-card {
        background-color: var(--natryon-cream-light);
        border-radius: 0.75rem;
        padding: 1.25rem;
        height: 100%;
        transition: all 0.25s ease;
        border: 1px solid var(--natryon-border);
    }

    .info-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.05);
    }

    .info-card-title {
        font-weight: 600;
        margin-bottom: 1.25rem;
        color: var(--natryon-text);
        display: flex;
        align-items: center;
        font-size: 1rem;
        letter-spacing: 0.3px;
    }

    .info-card-title i {
        margin-right: 0.75rem;
        font-size: 1.1rem;
        color: var(--natryon-green);
    }

    .info-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .info-list-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem 0;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .info-list-item:last-child {
        border-bottom: none;
    }

    .info-list-label {
        color: var(--natryon-text-light);
        font-weight: 500;
        font-size: 0.9rem;
    }

    .info-list-value {
        font-weight: 600;
        color: var(--natryon-text);
        font-size: 0.95rem;
    }

    /* Address Card Styles */
    .address-card {
        background-color: white;
        border-radius: 0.75rem;
        padding: 1.25rem;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        height: 100%;
        border: 1px solid rgba(0, 0, 0, 0.05);
    }

    .address-header {
        display: flex;
        align-items: center;
        margin-bottom: 1.25rem;
    }

    .address-details {
        line-height: 1.8;
        color: var(--natryon-text);
        font-size: 0.95rem;
    }

    .address-details i {
        width: 20px;
        color: var(--natryon-green);
        margin-right: 0.5rem;
        font-size: 0.9rem;
        opacity: 0.8;
    }

    /* Order Items Styles */
    .order-item {
        background-color: white;
        border-radius: 0.75rem;
        padding: 1.25rem;
        margin-bottom: 1rem;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
        transition: all 0.25s ease;
        border: 1px solid rgba(0, 0, 0, 0.05);
    }

    .order-item:hover {
        transform: translateY(-3px);
        box-shadow: 0 6px 16px rgba(0, 0, 0, 0.06);
    }

    .order-item-image {
        width: 70px;
        height: 70px;
        border-radius: 0.5rem;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        border: 1px solid rgba(0, 0, 0, 0.05);
    }

    .order-item-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .order-item-details {
        flex: 1;
    }

    .order-item-name {
        font-weight: 600;
        color: var(--natryon-text);
        margin-bottom: 0.5rem;
        font-size: 1rem;
        letter-spacing: 0.2px;
    }

    .order-item-price {
        font-weight: 700;
        color: var(--natryon-green);
        font-size: 1.1rem;
    }

    .order-item-meta {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
    }

    .order-item-meta-badge {
        background-color: var(--natryon-cream-light);
        color: var(--natryon-text);
        padding: 0.25rem 0.75rem;
        border-radius: 0.5rem;
        font-size: 0.8rem;
        display: inline-flex;
        align-items: center;
        border: 1px solid var(--natryon-border);
        font-weight: 500;
    }

    .order-item-meta-badge i {
        margin-right: 0.25rem;
        font-size: 0.75rem;
        color: var(--natryon-green);
    }

    /* Order Summary Styles */
    .order-summary {
        background-color: var(--natryon-cream-light);
        border-radius: 0.75rem;
        padding: 1.5rem;
        border: 1px solid var(--natryon-border);
    }

    .summary-item {
        display: flex;
        justify-content: space-between;
        padding: 0.75rem 0;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .summary-item:last-child {
        border-bottom: none;
    }

    .summary-label {
        color: var(--natryon-text-light);
        font-size: 0.9rem;
    }

    .summary-value {
        font-weight: 600;
        color: var(--natryon-text);
        font-size: 0.95rem;
    }

    .summary-total {
        font-size: 1.2rem;
        font-weight: 700;
        color: var(--natryon-green);
    }

    /* Action Button Styles */
    .action-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0.6rem 1.25rem;
        border-radius: 0.5rem;
        font-weight: 500;
        transition: all 0.25s ease;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
        letter-spacing: 0.3px;
    }

    .action-btn i {
        margin-right: 0.5rem;
        font-size: 1rem;
    }

    .action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.08);
    }

    /* Customer Stats Styles */
    .customer-stat-card {
        background-color: var(--natryon-cream-light);
        border-radius: 0.75rem;
        padding: 1.25rem;
        text-align: center;
        height: 100%;
        transition: all 0.25s ease;
        border: 1px solid var(--natryon-border);
    }

    .customer-stat-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.05);
    }

    .customer-stat-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background-color: var(--natryon-green-pale);
        color: var(--natryon-green);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 0.75rem;
        font-size: 1.25rem;
        box-shadow: 0 2px 8px rgba(2, 76, 61, 0.08);
    }

    .customer-stat-value {
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--natryon-text);
        margin-bottom: 0.25rem;
        line-height: 1.2;
    }

    .customer-stat-label {
        color: var(--natryon-text-light);
        font-size: 0.85rem;
        font-weight: 500;
    }

    /* Order Stat Card Styles */
    .order-stat-card {
        background-color: white;
        border-radius: 0.75rem;
        padding: 1.25rem;
        text-align: center;
        height: 100%;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        transition: all 0.25s ease;
        border: 1px solid rgba(0, 0, 0, 0.05);
    }

    .order-stat-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.07);
    }

    .order-stat-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background-color: var(--natryon-green-pale);
        color: var(--natryon-green);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 0.75rem;
        font-size: 1.25rem;
        box-shadow: 0 2px 8px rgba(2, 76, 61, 0.08);
    }

    .order-stat-label {
        color: var(--natryon-text-light);
        font-size: 0.85rem;
        margin-bottom: 0.5rem;
        font-weight: 500;
    }

    .order-stat-value {
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--natryon-text);
    }

    /* Subscription Badge */
    .subscription-badge {
        background-color: rgba(2, 76, 61, 0.1);
        color: var(--natryon-green);
        padding: 0.25rem 0.75rem;
        border-radius: 0.5rem;
        font-size: 0.8rem;
        font-weight: 500;
        display: inline-flex;
        align-items: center;
        border: 1px solid rgba(2, 76, 61, 0.2);
        letter-spacing: 0.2px;
    }

    .subscription-badge i {
        margin-right: 0.25rem;
        font-size: 0.75rem;
    }
</style>
@endpush

@section('content')
    <div class="premium-card mb-4">
        <div class="premium-card-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 fw-bold">Order #{{ $order->order_number }}</h1>
                    <p class="mb-0 opacity-75">{{ $order->created_at->format('F d, Y h:i A') }}</p>
                </div>
                <div class="d-flex gap-2">
                    <a href="{{ route('admin.orders.pdf', $order->id) }}" class="btn btn-light action-btn" target="_blank">
                        <i class="bi bi-file-pdf"></i> Export Invoice
                    </a>
                    <a href="{{ route('admin.orders.email', $order->id) }}" class="btn btn-light action-btn send-email-confirm" data-name="Order #{{ $order->order_number }}">
                        <i class="bi bi-envelope"></i> Send Email
                    </a>
                    <a href="{{ route('admin.orders.edit', $order->id) }}" class="btn btn-light action-btn">
                        <i class="bi bi-pencil"></i> Edit
                    </a>
                    <a href="{{ route('admin.orders.index') }}" class="btn btn-outline-light action-btn">
                        <i class="bi bi-arrow-left"></i> Back
                    </a>
                </div>
            </div>
        </div>
        <div class="premium-card-body p-0">
            <div class="p-4 bg-light">
                <div class="row g-4">
                    <div class="col-md-3">
                        <div class="order-stat-card">
                            <div class="order-stat-icon">
                                @if($order->status == 'pending')
                                    <i class="bi bi-hourglass"></i>
                                @elseif($order->status == 'processing')
                                    <i class="bi bi-gear"></i>
                                @elseif($order->status == 'completed')
                                    <i class="bi bi-check-circle"></i>
                                @elseif($order->status == 'declined')
                                    <i class="bi bi-x-circle"></i>
                                @endif
                            </div>
                            <div class="order-stat-label">Status</div>
                            <div class="order-stat-value">
                                <span class="status-badge {{ $order->status }}">
                                    {{ ucfirst($order->status) }}
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="order-stat-card">
                            <div class="order-stat-icon">
                                @if($order->is_paid)
                                    <i class="bi bi-credit-card-check"></i>
                                @else
                                    <i class="bi bi-credit-card"></i>
                                @endif
                            </div>
                            <div class="order-stat-label">Payment</div>
                            <div class="order-stat-value">
                                <span class="status-badge {{ $order->is_paid ? 'paid' : 'unpaid' }}">
                                    {{ $order->is_paid ? 'Paid' : 'Unpaid' }}
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="order-stat-card">
                            <div class="order-stat-icon">
                                <i class="bi bi-box-seam"></i>
                            </div>
                            <div class="order-stat-label">Items</div>
                            <div class="order-stat-value">{{ $order->items->sum('quantity') }}</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="order-stat-card">
                            <div class="order-stat-icon">
                                <i class="bi bi-currency-euro"></i>
                            </div>
                            <div class="order-stat-label">Total</div>
                            <div class="order-stat-value">€{{ number_format($order->total, 2) }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="premium-card mb-4">
                <div class="premium-card-header">
                    <h5 class="mb-0 fw-bold">
                        <i class="bi bi-sliders me-2"></i>Order Management
                    </h5>
                </div>
                <div class="premium-card-body">
                    <div class="row g-4 mb-4">
                        <div class="col-md-6">
                            <div class="info-card h-100">
                                <h6 class="info-card-title">
                                    <i class="bi bi-tag"></i>Order Status
                                </h6>
                                <div class="progress-container">
                                    <div class="progress-bar {{ $order->status }}"></div>
                                </div>
                                <div class="status-steps mb-4">
                                    <div class="d-flex justify-content-between">
                                        <span class="status-step {{ $order->status == 'pending' || $order->status == 'processing' || $order->status == 'completed' ? 'active' : '' }}">Pending</span>
                                        <span class="status-step {{ $order->status == 'processing' || $order->status == 'completed' ? 'active' : '' }}">Processing</span>
                                        <span class="status-step {{ $order->status == 'completed' ? 'active' : '' }}">Completed</span>
                                    </div>
                                </div>
                                <div class="dropdown w-100">
                                    <button class="btn dropdown-toggle w-100 action-btn
                                        @if($order->status == 'pending') btn-secondary
                                        @elseif($order->status == 'processing') btn-info
                                        @elseif($order->status == 'completed') btn-primary
                                        @elseif($order->status == 'declined') btn-danger
                                        @endif"
                                        type="button" id="statusDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="bi
                                            @if($order->status == 'pending') bi-hourglass
                                            @elseif($order->status == 'processing') bi-gear
                                            @elseif($order->status == 'completed') bi-check-circle
                                            @elseif($order->status == 'declined') bi-x-circle
                                            @endif"></i>
                                        {{ ucfirst($order->status) }}
                                    </button>
                                    <ul class="dropdown-menu w-100 shadow-lg border-0 rounded-3 py-3" aria-labelledby="statusDropdown">
                                        <li>
                                            <form id="status-pending-form" action="{{ route('admin.orders.update', $order->id) }}" method="POST">
                                                @csrf
                                                @method('PUT')
                                                <input type="hidden" name="status" value="pending">
                                                <button type="button" class="dropdown-item status-confirm py-2 px-3"
                                                    data-status="pending" data-name="Order #{{ $order->order_number }}">
                                                    <i class="bi bi-hourglass me-2 text-warning"></i>Pending
                                                </button>
                                            </form>
                                        </li>
                                        <li>
                                            <form id="status-processing-form" action="{{ route('admin.orders.update', $order->id) }}" method="POST">
                                                @csrf
                                                @method('PUT')
                                                <input type="hidden" name="status" value="processing">
                                                <button type="button" class="dropdown-item status-confirm py-2 px-3"
                                                    data-status="processing" data-name="Order #{{ $order->order_number }}">
                                                    <i class="bi bi-gear me-2 text-info"></i>Processing
                                                </button>
                                            </form>
                                        </li>
                                        <li>
                                            <form id="status-completed-form" action="{{ route('admin.orders.update', $order->id) }}" method="POST">
                                                @csrf
                                                @method('PUT')
                                                <input type="hidden" name="status" value="completed">
                                                <button type="button" class="dropdown-item status-confirm py-2 px-3"
                                                    data-status="completed" data-name="Order #{{ $order->order_number }}">
                                                    <i class="bi bi-check-circle me-2 text-success"></i>Completed
                                                </button>
                                            </form>
                                        </li>
                                        <li>
                                            <form id="status-declined-form" action="{{ route('admin.orders.update', $order->id) }}" method="POST">
                                                @csrf
                                                @method('PUT')
                                                <input type="hidden" name="status" value="declined">
                                                <button type="button" class="dropdown-item status-confirm py-2 px-3"
                                                    data-status="declined" data-name="Order #{{ $order->order_number }}">
                                                    <i class="bi bi-x-circle me-2 text-danger"></i>Declined
                                                </button>
                                            </form>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="info-card h-100">
                                <h6 class="info-card-title">
                                    <i class="bi bi-credit-card"></i>Payment Status
                                </h6>
                                <div class="progress-container">
                                    <div class="progress-bar {{ $order->is_paid ? 'paid' : 'unpaid' }}"></div>
                                </div>
                                <div class="payment-info mb-4">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="payment-icon me-3">
                                            <i class="bi {{ $order->payment_method == 'card' ? 'bi-credit-card' : 'bi-cash-coin' }}"></i>
                                        </div>
                                        <div>
                                            <div class="payment-method">{{ ucfirst($order->payment_method) }}</div>
                                            @if($order->is_paid && $order->paid_at)
                                                <div class="payment-date">Paid on {{ $order->paid_at->format('M d, Y') }}</div>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                                <div class="dropdown w-100">
                                    <button class="btn dropdown-toggle w-100 action-btn {{ $order->is_paid ? 'btn-primary' : 'btn-secondary' }}"
                                        type="button" id="paymentDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="bi {{ $order->is_paid ? 'bi-check-circle' : 'bi-hourglass' }}"></i>
                                        {{ $order->is_paid ? 'Paid' : 'Unpaid' }}
                                    </button>
                                    <ul class="dropdown-menu w-100 shadow-lg border-0 rounded-3 py-3" aria-labelledby="paymentDropdown">
                                        <li>
                                            <form id="payment-paid-form" action="{{ route('admin.orders.update', $order->id) }}" method="POST">
                                                @csrf
                                                @method('PUT')
                                                <input type="hidden" name="is_paid" value="1">
                                                <button type="button" class="dropdown-item status-confirm py-2 px-3"
                                                    data-status="paid" data-name="Order #{{ $order->order_number }}">
                                                    <i class="bi bi-check-circle me-2 text-success"></i>Mark as Paid
                                                </button>
                                            </form>
                                        </li>
                                        <li>
                                            <form id="payment-unpaid-form" action="{{ route('admin.orders.update', $order->id) }}" method="POST">
                                                @csrf
                                                @method('PUT')
                                                <input type="hidden" name="is_paid" value="0">
                                                <button type="button" class="dropdown-item status-confirm py-2 px-3"
                                                    data-status="unpaid" data-name="Order #{{ $order->order_number }}">
                                                    <i class="bi bi-x-circle me-2 text-danger"></i>Mark as Unpaid
                                                </button>
                                            </form>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row g-4">
                        <div class="col-md-6">
                            <div class="info-card h-100">
                                <h6 class="info-card-title">
                                    <i class="bi bi-receipt"></i>Order Information
                                </h6>
                                <ul class="info-list">
                                    <li class="info-list-item">
                                        <span class="info-list-label">Order Number:</span>
                                        <span class="info-list-value">{{ $order->order_number }}</span>
                                    </li>
                                    <li class="info-list-item">
                                        <span class="info-list-label">Date:</span>
                                        <span class="info-list-value">{{ $order->created_at->format('M d, Y H:i') }}</span>
                                    </li>
                                    <li class="info-list-item">
                                        <span class="info-list-label">Payment Method:</span>
                                        <span class="info-list-value">
                                            <span class="badge rounded-pill" style="background-color: var(--natryon-sand); color: var(--natryon-brown);">
                                                <i class="bi {{ $order->payment_method == 'card' ? 'bi-credit-card' : 'bi-cash-coin' }} me-1"></i>
                                                {{ ucfirst($order->payment_method) }}
                                            </span>
                                        </span>
                                    </li>
                                    @if($order->payment_id)
                                    <li class="info-list-item">
                                        <span class="info-list-label">Payment ID:</span>
                                        <span class="info-list-value">
                                            <span class="badge bg-light text-dark">{{ $order->payment_id }}</span>
                                        </span>
                                    </li>
                                    @endif
                                    @if($order->notes)
                                    <li class="info-list-item">
                                        <span class="info-list-label">Notes:</span>
                                        <span class="info-list-value">{{ Str::limit($order->notes, 30) }}</span>
                                    </li>
                                    @endif
                                </ul>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="info-card h-100">
                                <h6 class="info-card-title">
                                    <i class="bi bi-person"></i>Customer Information
                                </h6>
                                <div class="customer-profile mb-3">
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-circle me-3">
                                            {{ strtoupper(substr($order->billing_name, 0, 1)) }}
                                        </div>
                                        <div>
                                            <h6 class="mb-0 fw-bold">
                                                @if($order->user)
                                                    <a href="{{ route('admin.users.show', $order->user->id) }}" class="text-decoration-none" style="color: var(--natryon-brown);">
                                                        {{ $order->billing_name }}
                                                    </a>
                                                @else
                                                    {{ $order->billing_name }}
                                                @endif
                                            </h6>
                                            <p class="mb-0 small text-muted">{{ $order->billing_email }}</p>
                                        </div>
                                    </div>
                                </div>
                                <ul class="info-list">
                                    <li class="info-list-item">
                                        <span class="info-list-label">Phone:</span>
                                        <span class="info-list-value">{{ $order->billing_phone }}</span>
                                    </li>
                                    @if($order->user)
                                    <li class="info-list-item">
                                        <span class="info-list-label">Account:</span>
                                        <span class="info-list-value">
                                            <span class="badge rounded-pill" style="background-color: var(--natryon-olive); color: white;">
                                                <i class="bi bi-person-check me-1"></i>
                                                Registered User
                                            </span>
                                        </span>
                                    </li>
                                    @endif
                                    @if($order->affiliate_code)
                                    <li class="info-list-item">
                                        <span class="info-list-label">Affiliate Code:</span>
                                        <span class="info-list-value">
                                            <span class="badge rounded-pill" style="background-color: var(--natryon-light-olive); color: var(--natryon-brown);">
                                                <i class="bi bi-diagram-3 me-1"></i>
                                                {{ $order->affiliate_code }}
                                            </span>
                                        </span>
                                    </li>
                                    @endif
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="row g-4 mt-4">
                        <div class="col-md-6">
                            <div class="info-card h-100">
                                <h6 class="info-card-title">
                                    <i class="bi bi-geo-alt"></i>Billing Address
                                </h6>
                                <div class="address-card">
                                    <div class="address-header">
                                        <div class="avatar-circle me-3">
                                            {{ strtoupper(substr($order->billing_name, 0, 1)) }}
                                        </div>
                                        <div>
                                            <h6 class="mb-0 fw-bold" style="color: var(--natryon-brown);">{{ $order->billing_name }}</h6>
                                            <p class="mb-0 small text-muted">{{ $order->billing_email }}</p>
                                        </div>
                                    </div>
                                    <hr class="my-3" style="background-color: rgba(0,0,0,0.05);">
                                    <div class="address-details">
                                        <div class="mb-2"><i class="bi bi-house-door"></i>{{ $order->billing_address }}</div>
                                        <div class="mb-2"><i class="bi bi-geo"></i>{{ $order->billing_city }}, {{ $order->billing_state }} {{ $order->billing_zipcode }}</div>
                                        <div class="mb-2"><i class="bi bi-globe"></i>{{ $order->billing_country }}</div>
                                        <div><i class="bi bi-telephone"></i>{{ $order->billing_phone }}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-card h-100">
                                <h6 class="info-card-title">
                                    <i class="bi bi-truck"></i>Shipping Address
                                </h6>
                                <div class="address-card">
                                    <div class="address-header">
                                        <div class="avatar-circle me-3">
                                            {{ strtoupper(substr($order->shipping_name ?? $order->billing_name, 0, 1)) }}
                                        </div>
                                        <div>
                                            <h6 class="mb-0 fw-bold" style="color: var(--natryon-brown);">{{ $order->shipping_name ?? $order->billing_name }}</h6>
                                            <p class="mb-0 small text-muted">{{ $order->billing_email }}</p>
                                        </div>
                                    </div>
                                    <hr class="my-3" style="background-color: rgba(0,0,0,0.05);">
                                    <div class="address-details">
                                        <div class="mb-2"><i class="bi bi-house-door"></i>{{ $order->shipping_address ?? $order->billing_address }}</div>
                                        <div class="mb-2"><i class="bi bi-geo"></i>{{ $order->shipping_city ?? $order->billing_city }}, {{ $order->shipping_state ?? $order->billing_state }} {{ $order->shipping_zipcode ?? $order->billing_zipcode }}</div>
                                        <div><i class="bi bi-globe"></i>{{ $order->shipping_country ?? $order->billing_country }}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="premium-card mt-4">
                        <div class="premium-card-header">
                            <h5 class="mb-0 fw-bold">
                                <i class="bi bi-box-seam me-2"></i>Order Items
                            </h5>
                        </div>
                        <div class="premium-card-body">
                            <div class="order-items-container">
                                @foreach($order->items as $item)
                                    <div class="order-item">
                                        <div class="d-flex">
                                            <div class="order-item-image">
                                                @if($item->product && $item->product->image)
                                                    <img src="{{ asset('storage/' . $item->product->image) }}" alt="{{ $item->product_name }}">
                                                @else
                                                    <div class="d-flex align-items-center justify-content-center h-100 bg-light">
                                                        <i class="bi bi-box-seam fs-4" style="color: var(--natryon-light-brown);"></i>
                                                    </div>
                                                @endif
                                            </div>
                                            <div class="order-item-details ms-3">
                                                <div class="d-flex justify-content-between align-items-start mb-2">
                                                    <div>
                                                        <h6 class="order-item-name">
                                                            @if($item->product)
                                                                <a href="{{ route('admin.products.show', $item->product->id) }}" class="text-decoration-none" style="color: var(--natryon-brown);">{{ $item->product_name }}</a>
                                                            @else
                                                                {{ $item->product_name }}
                                                            @endif
                                                            @if($item->is_subscription)
                                                                <span class="subscription-badge ms-2">
                                                                    <i class="bi bi-arrow-repeat"></i>Subscription
                                                                </span>
                                                            @endif
                                                        </h6>
                                                        <div class="order-item-meta mt-2">
                                                            <span class="order-item-meta-badge">
                                                                <i class="bi bi-tag"></i>€{{ number_format($item->price, 2) }}
                                                            </span>
                                                            <span class="order-item-meta-badge">
                                                                <i class="bi bi-123"></i>{{ $item->quantity }} {{ $item->quantity > 1 ? 'units' : 'unit' }}
                                                            </span>
                                                        </div>
                                                    </div>
                                                    <div class="order-item-price">€{{ number_format($item->price * $item->quantity, 2) }}</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>

                            <div class="order-summary mt-4">
                                <div class="row">
                                    <div class="col-md-6 offset-md-6">
                                        <div class="summary-item">
                                            <span class="summary-label">Subtotal:</span>
                                            <span class="summary-value">€{{ number_format($order->subtotal, 2) }}</span>
                                        </div>
                                        @if($order->tax > 0)
                                        <div class="summary-item">
                                            <span class="summary-label">Tax:</span>
                                            <span class="summary-value">€{{ number_format($order->tax, 2) }}</span>
                                        </div>
                                        @endif
                                        @if($order->shipping > 0)
                                        <div class="summary-item">
                                            <span class="summary-label">Shipping:</span>
                                            <span class="summary-value">€{{ number_format($order->shipping, 2) }}</span>
                                        </div>
                                        @endif
                                        @if($order->discount > 0)
                                        <div class="summary-item">
                                            <span class="summary-label">Discount:</span>
                                            <span class="summary-value" style="color: #dc3545;">-€{{ number_format($order->discount, 2) }}</span>
                                        </div>
                                        @endif
                                        <div class="summary-item pt-3" style="border-top: 2px solid rgba(0,0,0,0.05);">
                                            <span class="summary-label" style="font-weight: 700; color: var(--natryon-brown);">Total:</span>
                                            <span class="summary-total">€{{ number_format($order->total, 2) }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="premium-card mb-4">
                <div class="premium-card-header">
                    <h5 class="mb-0 fw-bold">
                        <i class="bi bi-gear me-2"></i>Actions
                    </h5>
                </div>
                <div class="premium-card-body">
                    <div class="d-grid gap-3">
                        <a href="{{ route('admin.orders.pdf', $order->id) }}" class="btn btn-primary action-btn" target="_blank">
                            <i class="bi bi-file-pdf"></i> Download Invoice
                        </a>
                        <a href="{{ route('admin.orders.email', $order->id) }}" class="btn btn-secondary action-btn send-email-confirm" data-name="Order #{{ $order->order_number }}">
                            <i class="bi bi-envelope"></i> Send Invoice Email
                        </a>
                        <a href="{{ route('admin.orders.edit', $order->id) }}" class="btn btn-outline-primary action-btn">
                            <i class="bi bi-pencil"></i> Edit Order
                        </a>
                        <form action="{{ route('admin.orders.destroy', $order->id) }}" method="POST" class="d-inline">
                            @csrf
                            @method('DELETE')
                            <button type="button" class="btn btn-outline-danger w-100 action-btn delete-confirm" data-name="Order #{{ $order->order_number }}">
                                <i class="bi bi-trash"></i> Delete Order
                            </button>
                        </form>
                        <a href="{{ route('admin.orders.index') }}" class="btn btn-outline-secondary action-btn">
                            <i class="bi bi-arrow-left"></i> Back to Orders
                        </a>
                    </div>
                </div>
            </div>

            @if($order->user)
                <div class="premium-card mb-4">
                    <div class="premium-card-header">
                        <h5 class="mb-0 fw-bold">
                            <i class="bi bi-person me-2"></i>Customer Details
                        </h5>
                    </div>
                    <div class="premium-card-body">
                        <div class="text-center mb-4">
                            <div class="avatar-circle-lg mx-auto mb-3">
                                {{ strtoupper(substr($order->user->name, 0, 1)) }}
                            </div>
                            <h5 class="mb-1" style="color: var(--natryon-brown);">{{ $order->user->name }}</h5>
                            <p class="text-muted mb-0">{{ $order->user->email }}</p>
                        </div>

                        <div class="row g-3 mb-4">
                            <div class="col-6">
                                <div class="customer-stat-card">
                                    <div class="customer-stat-icon">
                                        <i class="bi bi-cart"></i>
                                    </div>
                                    <div class="customer-stat-value">{{ $order->user->orders->count() }}</div>
                                    <div class="customer-stat-label">Orders</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="customer-stat-card">
                                    <div class="customer-stat-icon">
                                        <i class="bi bi-currency-euro"></i>
                                    </div>
                                    <div class="customer-stat-value">€{{ number_format($order->user->orders->sum('total'), 2) }}</div>
                                    <div class="customer-stat-label">Total Spent</div>
                                </div>
                            </div>
                        </div>

                        <div class="d-grid">
                            <a href="{{ route('admin.users.show', $order->user->id) }}" class="btn btn-outline-primary action-btn">
                                <i class="bi bi-person"></i> View Customer Profile
                            </a>
                        </div>
                    </div>
                </div>
            @endif

            @if($order->affiliate_code)
                <div class="premium-card mb-4">
                    <div class="premium-card-header">
                        <h5 class="mb-0 fw-bold">
                            <i class="bi bi-diagram-3 me-2"></i>Affiliate Information
                        </h5>
                    </div>
                    <div class="premium-card-body">
                        <div class="info-card mb-4">
                            <ul class="info-list">
                                <li class="info-list-item">
                                    <span class="info-list-label">Affiliate Code:</span>
                                    <span class="info-list-value">
                                        <span class="badge rounded-pill" style="background-color: var(--natryon-light-olive); color: var(--natryon-brown);">
                                            {{ $order->affiliate_code }}
                                        </span>
                                    </span>
                                </li>

                                @if($affiliateUser)
                                <li class="info-list-item">
                                    <span class="info-list-label">Affiliate:</span>
                                    <span class="info-list-value">
                                        <a href="{{ route('admin.users.show', $affiliateUser->id) }}" class="text-decoration-none" style="color: var(--natryon-olive);">
                                            {{ $affiliateUser->name }}
                                        </a>
                                    </span>
                                </li>

                                <li class="info-list-item">
                                    <span class="info-list-label">Commission:</span>
                                    <span class="info-list-value" style="color: var(--natryon-olive); font-weight: 700;">
                                        €{{ number_format($affiliateCommission, 2) }}
                                    </span>
                                </li>
                                @endif
                            </ul>
                        </div>

                        @if($affiliateUser)
                            <div class="d-grid">
                                <a href="{{ route('admin.affiliates.show', $order->affiliate_code) }}" class="btn btn-outline-primary action-btn">
                                    <i class="bi bi-diagram-3"></i> View Affiliate Details
                                </a>
                            </div>
                        @endif
                    </div>
                </div>
            @endif
        </div>
    </div>


@endsection

@push('scripts')
<script>
    // Delete confirmation
    document.querySelectorAll('.delete-confirm').forEach(button => {
        button.addEventListener('click', function() {
            const name = this.getAttribute('data-name');

            Swal.fire({
                title: 'Are you sure?',
                text: `You are about to delete ${name}. This action cannot be undone!`,
                icon: 'warning',
                iconColor: '#d33',
                showCancelButton: true,
                confirmButtonColor: '#024C3D',
                cancelButtonColor: '#F6E8D2',
                confirmButtonText: 'Yes, delete it!',
                cancelButtonText: 'Cancel',
                customClass: {
                    confirmButton: 'btn btn-danger',
                    cancelButton: 'btn btn-secondary',
                    title: 'swal-title',
                    popup: 'swal-popup'
                },
                buttonsStyling: true,
                background: '#fff',
                backdrop: `rgba(0,0,0,0.4)`
            }).then((result) => {
                if (result.isConfirmed) {
                    this.closest('form').submit();
                }
            });
        });
    });

    // Status update confirmation
    document.querySelectorAll('.status-confirm').forEach(button => {
        button.addEventListener('click', function() {
            const name = this.getAttribute('data-name');
            const status = this.getAttribute('data-status');

            let statusText = '';
            let statusIcon = '';
            let iconColor = '';

            if (status === 'pending') {
                statusText = 'pending';
                statusIcon = 'warning';
                iconColor = '#F6E8D2';
            } else if (status === 'processing') {
                statusText = 'processing';
                statusIcon = 'info';
                iconColor = '#4dabf7';
            } else if (status === 'completed') {
                statusText = 'completed';
                statusIcon = 'success';
                iconColor = '#024C3D';
            } else if (status === 'declined') {
                statusText = 'declined';
                statusIcon = 'error';
                iconColor = '#d33';
            } else if (status === 'paid') {
                statusText = 'paid';
                statusIcon = 'success';
                iconColor = '#024C3D';
            } else if (status === 'unpaid') {
                statusText = 'unpaid';
                statusIcon = 'warning';
                iconColor = '#F6E8D2';
            }

            Swal.fire({
                title: 'Update Status',
                text: `Are you sure you want to mark ${name} as ${statusText}?`,
                icon: statusIcon,
                iconColor: iconColor,
                showCancelButton: true,
                confirmButtonColor: '#024C3D',
                cancelButtonColor: '#F6E8D2',
                confirmButtonText: 'Yes, update it!',
                cancelButtonText: 'Cancel',
                customClass: {
                    confirmButton: 'btn btn-primary',
                    cancelButton: 'btn btn-secondary',
                    title: 'swal-title',
                    popup: 'swal-popup'
                },
                buttonsStyling: true,
                background: '#fff',
                backdrop: `rgba(0,0,0,0.4)`
            }).then((result) => {
                if (result.isConfirmed) {
                    this.closest('form').submit();
                }
            });
        });
    });

    // Email confirmation
    document.querySelectorAll('.send-email-confirm').forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const name = this.getAttribute('data-name');
            const href = this.getAttribute('href');

            Swal.fire({
                title: 'Send Email',
                text: `Are you sure you want to send an email for ${name}?`,
                icon: 'question',
                iconColor: '#024C3D',
                showCancelButton: true,
                confirmButtonColor: '#024C3D',
                cancelButtonColor: '#F6E8D2',
                confirmButtonText: 'Yes, send it!',
                cancelButtonText: 'Cancel',
                customClass: {
                    confirmButton: 'btn btn-primary',
                    cancelButton: 'btn btn-secondary',
                    title: 'swal-title',
                    popup: 'swal-popup'
                },
                buttonsStyling: true,
                background: '#fff',
                backdrop: `rgba(0,0,0,0.4)`
            }).then((result) => {
                if (result.isConfirmed) {
                    window.location.href = href;
                }
            });
        });
    });

    // Add custom styles for SweetAlert
    const style = document.createElement('style');
    style.textContent = `
        .swal-popup {
            border-radius: 0.75rem;
            padding: 1.5rem;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
        }
        .swal-title {
            color: #1A2E29;
            font-weight: 600;
            font-size: 1.25rem;
            letter-spacing: 0.3px;
        }
        .swal2-html-container {
            color: #4D6661;
            font-size: 0.95rem;
        }
        .swal2-icon {
            border-width: 2px;
        }
        .swal2-styled.swal2-confirm, .swal2-styled.swal2-cancel {
            border-radius: 0.5rem;
            padding: 0.6rem 1.25rem;
            font-weight: 500;
            letter-spacing: 0.3px;
        }
    `;
    document.head.appendChild(style);
</script>
@endpush
