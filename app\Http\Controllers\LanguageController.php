<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;

class LanguageController extends Controller
{
    /**
     * Switch the application language.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $locale
     * @return \Illuminate\Http\RedirectResponse
     */
    public function switch(Request $request, $locale)
    {
        // Check if the locale is valid
        if (!in_array($locale, config('app.available_locales', ['en', 'fr']))) {
            $locale = config('app.locale');
        }

        // Store the locale in the session
        Session::put('locale', $locale);

        // Set the application locale immediately
        app()->setLocale($locale);

        // Add debugging
        \Illuminate\Support\Facades\Log::info('Language switched to: ' . $locale);
        \Illuminate\Support\Facades\Log::info('Available locales: ' . implode(', ', config('app.available_locales', ['en', 'fr'])));
        \Illuminate\Support\Facades\Log::info('App locale: ' . app()->getLocale());
        \Illuminate\Support\Facades\Log::info('Session locale: ' . Session::get('locale'));
        \Illuminate\Support\Facades\Log::info('Config locale: ' . config('app.locale'));

        // Redirect back to the previous page
        return redirect()->back();
    }
}
