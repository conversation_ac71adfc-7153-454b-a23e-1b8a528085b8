<?php

namespace App\Http\Controllers;

use App\Models\Category;
use App\Models\Product;
use Artesaos\SEOTools\Facades\SEOMeta;
use Artesaos\SEOTools\Facades\OpenGraph;
use Illuminate\Http\Request;

class HomeController extends Controller
{
    /**
     * Display the home page.
     */
    public function index()
    {
        // Get the product (you can replace this with the actual product ID or slug)
        $product = Product::where('is_active', true)->first();

        if (!$product) {
            // Create a dummy product if none exists
            $product = new Product();
            $product->id = 1; // Add an ID for the form submission
            $product->name = 'NATRYON Pack';
            $product->price = 87.00;
            $product->subscription_price = 79.00;
            $product->description = 'The NATRYON Pack is a complete nutritional supplement formula designed to improve your health and daily well-being.';
            $product->ingredients = 'Vitamin complex, essential minerals, probiotics, digestive enzymes, plant extracts, antioxidants, amino acids, and more.';
            $product->benefits = 'Increases energy, strengthens the immune system, improves digestion, supports cognitive health, and promotes overall well-being.';
            $product->how_to_use = 'Mix one scoop with 8-10 oz of water or your favorite beverage. Consume once daily, preferably in the morning.';
            $product->allow_subscription = true;
            $product->subscription_only = true; // Make it subscription only
        }

        // Set SEO metadata
        SEOMeta::setTitle('NATRYON Pack - Premium Nutritional Supplement');
        SEOMeta::setDescription('The NATRYON Pack offers a complete nutritional supplement formula for optimal health and daily wellness.');
        SEOMeta::setCanonical(url('/'));

        OpenGraph::setTitle('NATRYON Pack - Premium Nutritional Supplement');
        OpenGraph::setDescription('The NATRYON Pack offers a complete nutritional supplement formula for optimal health and daily wellness.');
        OpenGraph::setUrl(url('/'));
        OpenGraph::addProperty('type', 'product');
        OpenGraph::addImage(asset('images/pack/pack_natryon.jpeg'));

        // Get featured products
        $featuredProducts = Product::where('is_featured', true)
            ->where('is_active', true)
            ->take(4)
            ->get();

        return view('home_new', compact('product', 'featuredProducts'));
    }

    /**
     * Display the landing page for a specific product.
     */
    public function landingPage($slug)
    {
        $product = Product::where('slug', $slug)
            ->where('is_active', true)
            ->firstOrFail();

        // Set SEO metadata
        SEOMeta::setTitle($product->name . ' - NatRyon');
        SEOMeta::setDescription(substr(strip_tags($product->description), 0, 160));
        SEOMeta::setCanonical(url('/products/' . $product->slug));

        OpenGraph::setTitle($product->name . ' - NatRyon');
        OpenGraph::setDescription(substr(strip_tags($product->description), 0, 160));
        OpenGraph::setUrl(url('/products/' . $product->slug));
        OpenGraph::addProperty('type', 'product');
        if ($product->image) {
            OpenGraph::addImage(asset('storage/' . $product->image));
        }

        return view('landing', compact('product'));
    }

    /**
     * Display the about page.
     */
    public function about()
    {
        // Set SEO metadata
        SEOMeta::setTitle('About Us - NatRyon');
        SEOMeta::setDescription('Learn about NatRyon and our mission to provide premium greens powder supplements.');
        SEOMeta::setCanonical(url('/about'));

        OpenGraph::setTitle('About Us - NatRyon');
        OpenGraph::setDescription('Learn about NatRyon and our mission to provide premium greens powder supplements.');
        OpenGraph::setUrl(url('/about'));
        OpenGraph::addProperty('type', 'website');

        return view('about');
    }

    /**
     * Display the contact page.
     */
    public function contact()
    {
        // Set SEO metadata
        SEOMeta::setTitle('Contact Us - NatRyon');
        SEOMeta::setDescription('Get in touch with NatRyon for any questions about our premium greens powder supplements.');
        SEOMeta::setCanonical(url('/contact'));

        OpenGraph::setTitle('Contact Us - NatRyon');
        OpenGraph::setDescription('Get in touch with NatRyon for any questions about our premium greens powder supplements.');
        OpenGraph::setUrl(url('/contact'));
        OpenGraph::addProperty('type', 'website');

        return view('contact');
    }

    /**
     * Process the contact form submission.
     */
    public function contactSubmit(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'subject' => 'required|string|max:255',
            'message' => 'required|string',
        ]);

        // Here you would typically send an email with the contact form data
        // For now, we'll just redirect with a success message

        return redirect()->route('contact')->with('success', 'Your message has been sent. We will get back to you soon!');
    }

    /**
     * Display the French landing page for the NATRYON product pack.
     */
    public function natryonPackLanding()
    {
        // Get the product (you can replace this with the actual product ID or slug)
        $product = Product::where('is_active', true)->first();

        if (!$product) {
            // Create a dummy product if none exists
            $product = new Product();
            $product->id = 1; // Add an ID for the form submission
            $product->name = 'NATRYON Pack';
            $product->price = 79.99;
            $product->subscription_price = 67.99;
            $product->description = 'The NATRYON Pack is a complete nutritional supplement formula designed to improve your health and daily well-being.';
            $product->ingredients = 'Vitamin complex, essential minerals, probiotics, digestive enzymes, plant extracts, antioxidants, amino acids, and more.';
            $product->benefits = 'Increases energy, strengthens the immune system, improves digestion, supports cognitive health, and promotes overall well-being.';
            $product->how_to_use = 'Mix one scoop with 8-10 oz of water or your favorite beverage. Consume once daily, preferably in the morning.';
            $product->allow_subscription = true;
            $product->subscription_only = true; // Make it subscription only
        }

        // Set SEO metadata
        SEOMeta::setTitle('NATRYON Pack - Premium Nutritional Supplement');
        SEOMeta::setDescription('The NATRYON Pack offers a complete nutritional supplement formula for optimal health and daily wellness.');
        SEOMeta::setCanonical(url('/natryon-pack'));

        OpenGraph::setTitle('NATRYON Pack - Premium Nutritional Supplement');
        OpenGraph::setDescription('The NATRYON Pack offers a complete nutritional supplement formula for optimal health and daily wellness.');
        OpenGraph::setUrl(url('/natryon-pack'));
        OpenGraph::addProperty('type', 'product');
        OpenGraph::addImage(asset('images/pack/pack_natryon.jpeg'));

        return view('natryon-pack', compact('product'));
    }
}
