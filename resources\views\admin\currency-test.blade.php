@extends('layouts.admin')

@section('title', 'Multi-Currency Test')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Multi-Currency System Test</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Available Currencies</h6>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Code</th>
                                            <th>Name</th>
                                            <th>Symbol</th>
                                            <th>Exchange Rate</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach(\App\Models\Currency::all() as $currency)
                                        <tr>
                                            <td><strong>{{ $currency->code }}</strong></td>
                                            <td>{{ $currency->name }}</td>
                                            <td>{{ $currency->symbol }}</td>
                                            <td>{{ $currency->exchange_rate }}</td>
                                            <td>
                                                @if($currency->is_active)
                                                    <span class="badge bg-success">Active</span>
                                                @else
                                                    <span class="badge bg-secondary">Inactive</span>
                                                @endif
                                                @if($currency->is_default)
                                                    <span class="badge bg-primary">Default</span>
                                                @endif
                                            </td>
                                        </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <h6>Currency Conversion Test</h6>
                            <div class="mb-3">
                                <label class="form-label">Amount</label>
                                <input type="number" class="form-control" id="testAmount" value="100" step="0.01">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">From Currency</label>
                                <select class="form-select" id="fromCurrency">
                                    @foreach(\App\Models\Currency::active()->get() as $currency)
                                        <option value="{{ $currency->code }}">{{ $currency->code }} - {{ $currency->name }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">To Currency</label>
                                <select class="form-select" id="toCurrency">
                                    @foreach(\App\Models\Currency::active()->get() as $currency)
                                        <option value="{{ $currency->code }}" {{ $currency->code === 'USD' ? 'selected' : '' }}>{{ $currency->code }} - {{ $currency->name }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <button type="button" class="btn btn-primary" onclick="testConversion()">Convert</button>
                            <div id="conversionResult" class="mt-3"></div>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="row">
                        <div class="col-12">
                            <h6>Current User Currency</h6>
                            <p>Current Currency: <strong>{{ \App\Services\CurrencyService::getUserCurrency() }}</strong></p>
                            <p>Current Symbol: <strong>{{ \App\Services\CurrencyService::getSymbol() }}</strong></p>
                            
                            <h6 class="mt-4">Sample Product Prices</h6>
                            @if($products ?? false)
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Product</th>
                                                <th>Original Price</th>
                                                <th>Currency</th>
                                                <th>In Current Currency</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($products as $product)
                                            <tr>
                                                <td>{{ $product->name }}</td>
                                                <td>{{ $product->formatted_price }}</td>
                                                <td>{{ $product->currency }}</td>
                                                <td>{{ $product->getFormattedPriceInCurrency(\App\Services\CurrencyService::getUserCurrency()) }}</td>
                                            </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            @else
                                <p class="text-muted">No products available for testing.</p>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function testConversion() {
    const amount = document.getElementById('testAmount').value;
    const fromCurrency = document.getElementById('fromCurrency').value;
    const toCurrency = document.getElementById('toCurrency').value;
    const resultDiv = document.getElementById('conversionResult');
    
    if (!amount || !fromCurrency || !toCurrency) {
        resultDiv.innerHTML = '<div class="alert alert-warning">Please fill all fields</div>';
        return;
    }
    
    fetch('/api/currency/convert', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            amount: parseFloat(amount),
            from: fromCurrency,
            to: toCurrency
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            resultDiv.innerHTML = `
                <div class="alert alert-success">
                    <strong>Conversion Result:</strong><br>
                    ${data.original_amount} ${data.from_currency} = ${data.formatted}
                </div>
            `;
        } else {
            resultDiv.innerHTML = '<div class="alert alert-danger">Conversion failed</div>';
        }
    })
    .catch(error => {
        console.error('Error:', error);
        resultDiv.innerHTML = '<div class="alert alert-danger">An error occurred</div>';
    });
}
</script>
@endsection
