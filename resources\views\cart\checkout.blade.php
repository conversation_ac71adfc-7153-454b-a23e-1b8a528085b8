@extends('layouts.app')

@section('title', 'Checkout - NATRYON')

@section('content')
    <!-- Hero Section -->
    <section class="py-4 bg-light">
        <div class="container">
            <div class="text-center mb-5">
                <h1 class="display-5 fw-bold" style="color: #428677;">Checkout</h1>
                <p class="lead">Complete your order</p>
            </div>
        </div>
    </section>

    <!-- Checkout Section -->
    <section class="py-5">
        <div class="container">
            <form action="{{ route('orders.store') }}" method="POST" id="checkout-form">
            @csrf
            <div class="row">
                <!-- Checkout Form -->
                <div class="col-lg-8 mb-4 mb-lg-0">
                    <div class="card border-0 shadow-sm mb-4">
                        <div class="card-header bg-white py-3">
                            <h5 class="mb-0" style="color: #428677;">Billing Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="billing_name" class="form-label">Full Name</label>
                                    <input type="text" class="form-control @error('billing_name') is-invalid @enderror" id="billing_name" name="billing_name" value="{{ old('billing_name', auth()->user()->name ?? '') }}" required>
                                    @error('billing_name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="col-md-6">
                                    <label for="billing_email" class="form-label">Email Address</label>
                                    <input type="email" class="form-control @error('billing_email') is-invalid @enderror" id="billing_email" name="billing_email" value="{{ old('billing_email', auth()->user()->email ?? '') }}" required>
                                    @error('billing_email')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="col-md-6">
                                    <label for="billing_phone" class="form-label">Phone Number</label>
                                    <input type="text" class="form-control @error('billing_phone') is-invalid @enderror" id="billing_phone" name="billing_phone" value="{{ old('billing_phone', auth()->user()->phone ?? '') }}" required>
                                    @error('billing_phone')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="col-md-6">
                                    <label for="billing_country" class="form-label">Country</label>
                                    <select class="form-select @error('billing_country') is-invalid @enderror" id="billing_country" name="billing_country" required>
                                        <option value="">Select Country</option>

                                        <!-- Africa -->
                                        <optgroup label="Africa">
                                            <option value="MA" {{ old('billing_country', auth()->user()->country ?? '') == 'MA' ? 'selected' : '' }}>Morocco</option>
                                        </optgroup>

                                        <!-- Americas -->
                                        <optgroup label="Americas">
                                            <option value="AR" {{ old('billing_country', auth()->user()->country ?? '') == 'AR' ? 'selected' : '' }}>Argentina</option>
                                            <option value="BS" {{ old('billing_country', auth()->user()->country ?? '') == 'BS' ? 'selected' : '' }}>Bahamas</option>
                                            <option value="BR" {{ old('billing_country', auth()->user()->country ?? '') == 'BR' ? 'selected' : '' }}>Brazil</option>
                                            <option value="CA" {{ old('billing_country', auth()->user()->country ?? '') == 'CA' ? 'selected' : '' }}>Canada</option>
                                            <option value="CL" {{ old('billing_country', auth()->user()->country ?? '') == 'CL' ? 'selected' : '' }}>Chile</option>
                                            <option value="CO" {{ old('billing_country', auth()->user()->country ?? '') == 'CO' ? 'selected' : '' }}>Colombia</option>
                                            <option value="CR" {{ old('billing_country', auth()->user()->country ?? '') == 'CR' ? 'selected' : '' }}>Costa Rica</option>
                                            <option value="DO" {{ old('billing_country', auth()->user()->country ?? '') == 'DO' ? 'selected' : '' }}>Dominican Republic</option>
                                            <option value="EC" {{ old('billing_country', auth()->user()->country ?? '') == 'EC' ? 'selected' : '' }}>Ecuador</option>
                                            <option value="SV" {{ old('billing_country', auth()->user()->country ?? '') == 'SV' ? 'selected' : '' }}>El Salvador</option>
                                            <option value="GT" {{ old('billing_country', auth()->user()->country ?? '') == 'GT' ? 'selected' : '' }}>Guatemala</option>
                                            <option value="HN" {{ old('billing_country', auth()->user()->country ?? '') == 'HN' ? 'selected' : '' }}>Honduras</option>
                                            <option value="JM" {{ old('billing_country', auth()->user()->country ?? '') == 'JM' ? 'selected' : '' }}>Jamaica</option>
                                            <option value="MX" {{ old('billing_country', auth()->user()->country ?? '') == 'MX' ? 'selected' : '' }}>Mexico</option>
                                            <option value="NI" {{ old('billing_country', auth()->user()->country ?? '') == 'NI' ? 'selected' : '' }}>Nicaragua</option>
                                            <option value="PA" {{ old('billing_country', auth()->user()->country ?? '') == 'PA' ? 'selected' : '' }}>Panama</option>
                                            <option value="PY" {{ old('billing_country', auth()->user()->country ?? '') == 'PY' ? 'selected' : '' }}>Paraguay</option>
                                            <option value="PE" {{ old('billing_country', auth()->user()->country ?? '') == 'PE' ? 'selected' : '' }}>Peru</option>
                                            <option value="PR" {{ old('billing_country', auth()->user()->country ?? '') == 'PR' ? 'selected' : '' }}>Puerto Rico</option>
                                            <option value="TT" {{ old('billing_country', auth()->user()->country ?? '') == 'TT' ? 'selected' : '' }}>Trinidad and Tobago</option>
                                            <option value="US" {{ old('billing_country', auth()->user()->country ?? '') == 'US' ? 'selected' : '' }}>United States</option>
                                            <option value="UY" {{ old('billing_country', auth()->user()->country ?? '') == 'UY' ? 'selected' : '' }}>Uruguay</option>
                                            <option value="VE" {{ old('billing_country', auth()->user()->country ?? '') == 'VE' ? 'selected' : '' }}>Venezuela</option>
                                        </optgroup>

                                        <!-- Asia -->
                                        <optgroup label="Asia">
                                            <option value="BD" {{ old('billing_country', auth()->user()->country ?? '') == 'BD' ? 'selected' : '' }}>Bangladesh</option>
                                            <option value="CN" {{ old('billing_country', auth()->user()->country ?? '') == 'CN' ? 'selected' : '' }}>China</option>
                                            <option value="HK" {{ old('billing_country', auth()->user()->country ?? '') == 'HK' ? 'selected' : '' }}>Hong Kong</option>
                                            <option value="IN" {{ old('billing_country', auth()->user()->country ?? '') == 'IN' ? 'selected' : '' }}>India</option>
                                            <option value="ID" {{ old('billing_country', auth()->user()->country ?? '') == 'ID' ? 'selected' : '' }}>Indonesia</option>
                                            <option value="IL" {{ old('billing_country', auth()->user()->country ?? '') == 'IL' ? 'selected' : '' }}>Israel</option>
                                            <option value="JP" {{ old('billing_country', auth()->user()->country ?? '') == 'JP' ? 'selected' : '' }}>Japan</option>
                                            <option value="JO" {{ old('billing_country', auth()->user()->country ?? '') == 'JO' ? 'selected' : '' }}>Jordan</option>
                                            <option value="KW" {{ old('billing_country', auth()->user()->country ?? '') == 'KW' ? 'selected' : '' }}>Kuwait</option>
                                            <option value="LB" {{ old('billing_country', auth()->user()->country ?? '') == 'LB' ? 'selected' : '' }}>Lebanon</option>
                                            <option value="MY" {{ old('billing_country', auth()->user()->country ?? '') == 'MY' ? 'selected' : '' }}>Malaysia</option>
                                            <option value="PK" {{ old('billing_country', auth()->user()->country ?? '') == 'PK' ? 'selected' : '' }}>Pakistan</option>
                                            <option value="PH" {{ old('billing_country', auth()->user()->country ?? '') == 'PH' ? 'selected' : '' }}>Philippines</option>
                                            <option value="QA" {{ old('billing_country', auth()->user()->country ?? '') == 'QA' ? 'selected' : '' }}>Qatar</option>
                                            <option value="SA" {{ old('billing_country', auth()->user()->country ?? '') == 'SA' ? 'selected' : '' }}>Saudi Arabia</option>
                                            <option value="SG" {{ old('billing_country', auth()->user()->country ?? '') == 'SG' ? 'selected' : '' }}>Singapore</option>
                                            <option value="KR" {{ old('billing_country', auth()->user()->country ?? '') == 'KR' ? 'selected' : '' }}>South Korea</option>
                                            <option value="LK" {{ old('billing_country', auth()->user()->country ?? '') == 'LK' ? 'selected' : '' }}>Sri Lanka</option>
                                            <option value="TW" {{ old('billing_country', auth()->user()->country ?? '') == 'TW' ? 'selected' : '' }}>Taiwan</option>
                                            <option value="TH" {{ old('billing_country', auth()->user()->country ?? '') == 'TH' ? 'selected' : '' }}>Thailand</option>
                                            <option value="TR" {{ old('billing_country', auth()->user()->country ?? '') == 'TR' ? 'selected' : '' }}>Turkey</option>
                                            <option value="AE" {{ old('billing_country', auth()->user()->country ?? '') == 'AE' ? 'selected' : '' }}>United Arab Emirates</option>
                                            <option value="VN" {{ old('billing_country', auth()->user()->country ?? '') == 'VN' ? 'selected' : '' }}>Vietnam</option>
                                        </optgroup>

                                        <!-- Europe -->
                                        <optgroup label="Europe">
                                            <option value="AT" {{ old('billing_country', auth()->user()->country ?? '') == 'AT' ? 'selected' : '' }}>Austria</option>
                                            <option value="BE" {{ old('billing_country', auth()->user()->country ?? '') == 'BE' ? 'selected' : '' }}>Belgium</option>
                                            <option value="BG" {{ old('billing_country', auth()->user()->country ?? '') == 'BG' ? 'selected' : '' }}>Bulgaria</option>
                                            <option value="HR" {{ old('billing_country', auth()->user()->country ?? '') == 'HR' ? 'selected' : '' }}>Croatia</option>
                                            <option value="CY" {{ old('billing_country', auth()->user()->country ?? '') == 'CY' ? 'selected' : '' }}>Cyprus</option>
                                            <option value="CZ" {{ old('billing_country', auth()->user()->country ?? '') == 'CZ' ? 'selected' : '' }}>Czech Republic</option>
                                            <option value="DK" {{ old('billing_country', auth()->user()->country ?? '') == 'DK' ? 'selected' : '' }}>Denmark</option>
                                            <option value="EE" {{ old('billing_country', auth()->user()->country ?? '') == 'EE' ? 'selected' : '' }}>Estonia</option>
                                            <option value="FI" {{ old('billing_country', auth()->user()->country ?? '') == 'FI' ? 'selected' : '' }}>Finland</option>
                                            <option value="FR" {{ old('billing_country', auth()->user()->country ?? '') == 'FR' ? 'selected' : '' }}>France</option>
                                            <option value="DE" {{ old('billing_country', auth()->user()->country ?? '') == 'DE' ? 'selected' : '' }}>Germany</option>
                                            <option value="GR" {{ old('billing_country', auth()->user()->country ?? '') == 'GR' ? 'selected' : '' }}>Greece</option>
                                            <option value="HU" {{ old('billing_country', auth()->user()->country ?? '') == 'HU' ? 'selected' : '' }}>Hungary</option>
                                            <option value="IS" {{ old('billing_country', auth()->user()->country ?? '') == 'IS' ? 'selected' : '' }}>Iceland</option>
                                            <option value="IE" {{ old('billing_country', auth()->user()->country ?? '') == 'IE' ? 'selected' : '' }}>Ireland</option>
                                            <option value="IT" {{ old('billing_country', auth()->user()->country ?? '') == 'IT' ? 'selected' : '' }}>Italy</option>
                                            <option value="LV" {{ old('billing_country', auth()->user()->country ?? '') == 'LV' ? 'selected' : '' }}>Latvia</option>
                                            <option value="LT" {{ old('billing_country', auth()->user()->country ?? '') == 'LT' ? 'selected' : '' }}>Lithuania</option>
                                            <option value="LU" {{ old('billing_country', auth()->user()->country ?? '') == 'LU' ? 'selected' : '' }}>Luxembourg</option>
                                            <option value="MT" {{ old('billing_country', auth()->user()->country ?? '') == 'MT' ? 'selected' : '' }}>Malta</option>
                                            <option value="NL" {{ old('billing_country', auth()->user()->country ?? '') == 'NL' ? 'selected' : '' }}>Netherlands</option>
                                            <option value="NO" {{ old('billing_country', auth()->user()->country ?? '') == 'NO' ? 'selected' : '' }}>Norway</option>
                                            <option value="PL" {{ old('billing_country', auth()->user()->country ?? '') == 'PL' ? 'selected' : '' }}>Poland</option>
                                            <option value="PT" {{ old('billing_country', auth()->user()->country ?? '') == 'PT' ? 'selected' : '' }}>Portugal</option>
                                            <option value="RO" {{ old('billing_country', auth()->user()->country ?? '') == 'RO' ? 'selected' : '' }}>Romania</option>
                                            <option value="RU" {{ old('billing_country', auth()->user()->country ?? '') == 'RU' ? 'selected' : '' }}>Russia</option>
                                            <option value="RS" {{ old('billing_country', auth()->user()->country ?? '') == 'RS' ? 'selected' : '' }}>Serbia</option>
                                            <option value="SK" {{ old('billing_country', auth()->user()->country ?? '') == 'SK' ? 'selected' : '' }}>Slovakia</option>
                                            <option value="SI" {{ old('billing_country', auth()->user()->country ?? '') == 'SI' ? 'selected' : '' }}>Slovenia</option>
                                            <option value="ES" {{ old('billing_country', auth()->user()->country ?? '') == 'ES' ? 'selected' : '' }}>Spain</option>
                                            <option value="SE" {{ old('billing_country', auth()->user()->country ?? '') == 'SE' ? 'selected' : '' }}>Sweden</option>
                                            <option value="CH" {{ old('billing_country', auth()->user()->country ?? '') == 'CH' ? 'selected' : '' }}>Switzerland</option>
                                            <option value="UA" {{ old('billing_country', auth()->user()->country ?? '') == 'UA' ? 'selected' : '' }}>Ukraine</option>
                                            <option value="GB" {{ old('billing_country', auth()->user()->country ?? '') == 'GB' ? 'selected' : '' }}>United Kingdom</option>
                                        </optgroup>

                                        <!-- Oceania -->
                                        <optgroup label="Oceania">
                                            <option value="AU" {{ old('billing_country', auth()->user()->country ?? '') == 'AU' ? 'selected' : '' }}>Australia</option>
                                            <option value="NZ" {{ old('billing_country', auth()->user()->country ?? '') == 'NZ' ? 'selected' : '' }}>New Zealand</option>
                                        </optgroup>
                                    </select>
                                    @error('billing_country')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">Select your billing country</div>
                                </div>
                                <div class="col-12">
                                    <label for="billing_address" class="form-label">Address</label>
                                    <input type="text" class="form-control @error('billing_address') is-invalid @enderror" id="billing_address" name="billing_address" value="{{ old('billing_address', auth()->user()->address ?? '') }}" required>
                                    @error('billing_address')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="col-md-6">
                                    <label for="billing_city" class="form-label">City</label>
                                    <input type="text" class="form-control @error('billing_city') is-invalid @enderror" id="billing_city" name="billing_city" value="{{ old('billing_city', auth()->user()->city ?? '') }}" required>
                                    @error('billing_city')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="col-md-4">
                                    <label for="billing_state" class="form-label">State/Province</label>
                                    <input type="text" class="form-control @error('billing_state') is-invalid @enderror" id="billing_state" name="billing_state" value="{{ old('billing_state', auth()->user()->state ?? '') }}">
                                    @error('billing_state')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="col-md-2">
                                    <label for="billing_zipcode" class="form-label">Zip/Postal</label>
                                    <input type="text" class="form-control @error('billing_zipcode') is-invalid @enderror" id="billing_zipcode" name="billing_zipcode" value="{{ old('billing_zipcode', auth()->user()->zip_code ?? '') }}" required>
                                    @error('billing_zipcode')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="invalid-feedback" id="zipcode-error">Please enter a valid zip/postal code.</div>
                                    <div class="form-text">Required for order processing</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card border-0 shadow-sm mb-4">
                        <div class="card-header bg-white py-3">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="mb-0" style="color: #428677;">Shipping Information</h5>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="same-as-billing" checked>
                                    <label class="form-check-label" for="same-as-billing">
                                        Same as billing
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="card-body" id="shipping-fields" style="display: none;">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="shipping_name" class="form-label">Full Name</label>
                                    <input type="text" class="form-control @error('shipping_name') is-invalid @enderror" id="shipping_name" name="shipping_name" value="{{ old('shipping_name') }}">
                                    @error('shipping_name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="col-md-6">
                                    <label for="shipping_country" class="form-label">Country</label>
                                    <select class="form-select @error('shipping_country') is-invalid @enderror" id="shipping_country" name="shipping_country">
                                        <option value="">Select Country</option>

                                        <!-- Africa -->
                                        <optgroup label="Africa">
                                            <option value="MA" {{ old('shipping_country') == 'MA' ? 'selected' : '' }}>Morocco</option>
                                        </optgroup>

                                        <!-- Americas -->
                                        <optgroup label="Americas">
                                            <option value="AR" {{ old('shipping_country') == 'AR' ? 'selected' : '' }}>Argentina</option>
                                            <option value="BS" {{ old('shipping_country') == 'BS' ? 'selected' : '' }}>Bahamas</option>
                                            <option value="BR" {{ old('shipping_country') == 'BR' ? 'selected' : '' }}>Brazil</option>
                                            <option value="CA" {{ old('shipping_country') == 'CA' ? 'selected' : '' }}>Canada</option>
                                            <option value="CL" {{ old('shipping_country') == 'CL' ? 'selected' : '' }}>Chile</option>
                                            <option value="CO" {{ old('shipping_country') == 'CO' ? 'selected' : '' }}>Colombia</option>
                                            <option value="CR" {{ old('shipping_country') == 'CR' ? 'selected' : '' }}>Costa Rica</option>
                                            <option value="DO" {{ old('shipping_country') == 'DO' ? 'selected' : '' }}>Dominican Republic</option>
                                            <option value="EC" {{ old('shipping_country') == 'EC' ? 'selected' : '' }}>Ecuador</option>
                                            <option value="SV" {{ old('shipping_country') == 'SV' ? 'selected' : '' }}>El Salvador</option>
                                            <option value="GT" {{ old('shipping_country') == 'GT' ? 'selected' : '' }}>Guatemala</option>
                                            <option value="HN" {{ old('shipping_country') == 'HN' ? 'selected' : '' }}>Honduras</option>
                                            <option value="JM" {{ old('shipping_country') == 'JM' ? 'selected' : '' }}>Jamaica</option>
                                            <option value="MX" {{ old('shipping_country') == 'MX' ? 'selected' : '' }}>Mexico</option>
                                            <option value="NI" {{ old('shipping_country') == 'NI' ? 'selected' : '' }}>Nicaragua</option>
                                            <option value="PA" {{ old('shipping_country') == 'PA' ? 'selected' : '' }}>Panama</option>
                                            <option value="PY" {{ old('shipping_country') == 'PY' ? 'selected' : '' }}>Paraguay</option>
                                            <option value="PE" {{ old('shipping_country') == 'PE' ? 'selected' : '' }}>Peru</option>
                                            <option value="PR" {{ old('shipping_country') == 'PR' ? 'selected' : '' }}>Puerto Rico</option>
                                            <option value="TT" {{ old('shipping_country') == 'TT' ? 'selected' : '' }}>Trinidad and Tobago</option>
                                            <option value="US" {{ old('shipping_country') == 'US' ? 'selected' : '' }}>United States</option>
                                            <option value="UY" {{ old('shipping_country') == 'UY' ? 'selected' : '' }}>Uruguay</option>
                                            <option value="VE" {{ old('shipping_country') == 'VE' ? 'selected' : '' }}>Venezuela</option>
                                        </optgroup>

                                        <!-- Asia -->
                                        <optgroup label="Asia">
                                            <option value="BD" {{ old('shipping_country') == 'BD' ? 'selected' : '' }}>Bangladesh</option>
                                            <option value="CN" {{ old('shipping_country') == 'CN' ? 'selected' : '' }}>China</option>
                                            <option value="HK" {{ old('shipping_country') == 'HK' ? 'selected' : '' }}>Hong Kong</option>
                                            <option value="IN" {{ old('shipping_country') == 'IN' ? 'selected' : '' }}>India</option>
                                            <option value="ID" {{ old('shipping_country') == 'ID' ? 'selected' : '' }}>Indonesia</option>
                                            <option value="IL" {{ old('shipping_country') == 'IL' ? 'selected' : '' }}>Israel</option>
                                            <option value="JP" {{ old('shipping_country') == 'JP' ? 'selected' : '' }}>Japan</option>
                                            <option value="JO" {{ old('shipping_country') == 'JO' ? 'selected' : '' }}>Jordan</option>
                                            <option value="KW" {{ old('shipping_country') == 'KW' ? 'selected' : '' }}>Kuwait</option>
                                            <option value="LB" {{ old('shipping_country') == 'LB' ? 'selected' : '' }}>Lebanon</option>
                                            <option value="MY" {{ old('shipping_country') == 'MY' ? 'selected' : '' }}>Malaysia</option>
                                            <option value="PK" {{ old('shipping_country') == 'PK' ? 'selected' : '' }}>Pakistan</option>
                                            <option value="PH" {{ old('shipping_country') == 'PH' ? 'selected' : '' }}>Philippines</option>
                                            <option value="QA" {{ old('shipping_country') == 'QA' ? 'selected' : '' }}>Qatar</option>
                                            <option value="SA" {{ old('shipping_country') == 'SA' ? 'selected' : '' }}>Saudi Arabia</option>
                                            <option value="SG" {{ old('shipping_country') == 'SG' ? 'selected' : '' }}>Singapore</option>
                                            <option value="KR" {{ old('shipping_country') == 'KR' ? 'selected' : '' }}>South Korea</option>
                                            <option value="LK" {{ old('shipping_country') == 'LK' ? 'selected' : '' }}>Sri Lanka</option>
                                            <option value="TW" {{ old('shipping_country') == 'TW' ? 'selected' : '' }}>Taiwan</option>
                                            <option value="TH" {{ old('shipping_country') == 'TH' ? 'selected' : '' }}>Thailand</option>
                                            <option value="TR" {{ old('shipping_country') == 'TR' ? 'selected' : '' }}>Turkey</option>
                                            <option value="AE" {{ old('shipping_country') == 'AE' ? 'selected' : '' }}>United Arab Emirates</option>
                                            <option value="VN" {{ old('shipping_country') == 'VN' ? 'selected' : '' }}>Vietnam</option>
                                        </optgroup>

                                        <!-- Europe -->
                                        <optgroup label="Europe">
                                            <option value="AT" {{ old('shipping_country') == 'AT' ? 'selected' : '' }}>Austria</option>
                                            <option value="BE" {{ old('shipping_country') == 'BE' ? 'selected' : '' }}>Belgium</option>
                                            <option value="BG" {{ old('shipping_country') == 'BG' ? 'selected' : '' }}>Bulgaria</option>
                                            <option value="HR" {{ old('shipping_country') == 'HR' ? 'selected' : '' }}>Croatia</option>
                                            <option value="CY" {{ old('shipping_country') == 'CY' ? 'selected' : '' }}>Cyprus</option>
                                            <option value="CZ" {{ old('shipping_country') == 'CZ' ? 'selected' : '' }}>Czech Republic</option>
                                            <option value="DK" {{ old('shipping_country') == 'DK' ? 'selected' : '' }}>Denmark</option>
                                            <option value="EE" {{ old('shipping_country') == 'EE' ? 'selected' : '' }}>Estonia</option>
                                            <option value="FI" {{ old('shipping_country') == 'FI' ? 'selected' : '' }}>Finland</option>
                                            <option value="FR" {{ old('shipping_country') == 'FR' ? 'selected' : '' }}>France</option>
                                            <option value="DE" {{ old('shipping_country') == 'DE' ? 'selected' : '' }}>Germany</option>
                                            <option value="GR" {{ old('shipping_country') == 'GR' ? 'selected' : '' }}>Greece</option>
                                            <option value="HU" {{ old('shipping_country') == 'HU' ? 'selected' : '' }}>Hungary</option>
                                            <option value="IS" {{ old('shipping_country') == 'IS' ? 'selected' : '' }}>Iceland</option>
                                            <option value="IE" {{ old('shipping_country') == 'IE' ? 'selected' : '' }}>Ireland</option>
                                            <option value="IT" {{ old('shipping_country') == 'IT' ? 'selected' : '' }}>Italy</option>
                                            <option value="LV" {{ old('shipping_country') == 'LV' ? 'selected' : '' }}>Latvia</option>
                                            <option value="LT" {{ old('shipping_country') == 'LT' ? 'selected' : '' }}>Lithuania</option>
                                            <option value="LU" {{ old('shipping_country') == 'LU' ? 'selected' : '' }}>Luxembourg</option>
                                            <option value="MT" {{ old('shipping_country') == 'MT' ? 'selected' : '' }}>Malta</option>
                                            <option value="NL" {{ old('shipping_country') == 'NL' ? 'selected' : '' }}>Netherlands</option>
                                            <option value="NO" {{ old('shipping_country') == 'NO' ? 'selected' : '' }}>Norway</option>
                                            <option value="PL" {{ old('shipping_country') == 'PL' ? 'selected' : '' }}>Poland</option>
                                            <option value="PT" {{ old('shipping_country') == 'PT' ? 'selected' : '' }}>Portugal</option>
                                            <option value="RO" {{ old('shipping_country') == 'RO' ? 'selected' : '' }}>Romania</option>
                                            <option value="RU" {{ old('shipping_country') == 'RU' ? 'selected' : '' }}>Russia</option>
                                            <option value="RS" {{ old('shipping_country') == 'RS' ? 'selected' : '' }}>Serbia</option>
                                            <option value="SK" {{ old('shipping_country') == 'SK' ? 'selected' : '' }}>Slovakia</option>
                                            <option value="SI" {{ old('shipping_country') == 'SI' ? 'selected' : '' }}>Slovenia</option>
                                            <option value="ES" {{ old('shipping_country') == 'ES' ? 'selected' : '' }}>Spain</option>
                                            <option value="SE" {{ old('shipping_country') == 'SE' ? 'selected' : '' }}>Sweden</option>
                                            <option value="CH" {{ old('shipping_country') == 'CH' ? 'selected' : '' }}>Switzerland</option>
                                            <option value="UA" {{ old('shipping_country') == 'UA' ? 'selected' : '' }}>Ukraine</option>
                                            <option value="GB" {{ old('shipping_country') == 'GB' ? 'selected' : '' }}>United Kingdom</option>
                                        </optgroup>

                                        <!-- Oceania -->
                                        <optgroup label="Oceania">
                                            <option value="AU" {{ old('shipping_country') == 'AU' ? 'selected' : '' }}>Australia</option>
                                            <option value="NZ" {{ old('shipping_country') == 'NZ' ? 'selected' : '' }}>New Zealand</option>
                                        </optgroup>
                                    </select>
                                    @error('shipping_country')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">Select your shipping country</div>
                                </div>
                                <div class="col-12">
                                    <label for="shipping_address" class="form-label">Address</label>
                                    <input type="text" class="form-control @error('shipping_address') is-invalid @enderror" id="shipping_address" name="shipping_address" value="{{ old('shipping_address') }}">
                                    @error('shipping_address')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="col-md-6">
                                    <label for="shipping_city" class="form-label">City</label>
                                    <input type="text" class="form-control @error('shipping_city') is-invalid @enderror" id="shipping_city" name="shipping_city" value="{{ old('shipping_city') }}">
                                    @error('shipping_city')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="col-md-4">
                                    <label for="shipping_state" class="form-label">State/Province</label>
                                    <input type="text" class="form-control @error('shipping_state') is-invalid @enderror" id="shipping_state" name="shipping_state" value="{{ old('shipping_state') }}">
                                    @error('shipping_state')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="col-md-2">
                                    <label for="shipping_zipcode" class="form-label">Zip/Postal</label>
                                    <input type="text" class="form-control @error('shipping_zipcode') is-invalid @enderror" id="shipping_zipcode" name="shipping_zipcode" value="{{ old('shipping_zipcode') }}">
                                    @error('shipping_zipcode')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-white py-3">
                            <h5 class="mb-0" style="color: #428677;"><i class="bi bi-credit-card me-2"></i>Payment Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-4">
                                <div class="form-label fw-bold mb-2">Payment Method</div>
                                <div class="d-flex flex-wrap gap-3 mt-2">
                                    <div class="payment-method-option active">
                                        <input class="form-check-input visually-hidden" type="radio" name="payment_method" id="payment_method_stripe" value="stripe" checked>
                                        <label class="payment-method-label d-flex align-items-center" for="payment_method_stripe">
                                            <i class="bi bi-credit-card-2-front fs-4 me-2"></i>
                                            <span>Credit Card</span>
                                        </label>
                                    </div>

                                    @php
                                        $hasSubscription = false;
                                        foreach($cart as $item) {
                                            if(isset($item['is_subscription']) && $item['is_subscription']) {
                                                $hasSubscription = true;
                                                break;
                                            }
                                        }
                                    @endphp

                                    <div class="payment-method-option {{ $hasSubscription ? 'disabled' : '' }}">
                                        <input class="form-check-input visually-hidden" type="radio" name="payment_method" id="payment_method_cod" value="cod" {{ $hasSubscription ? 'disabled' : '' }}>
                                        <label class="payment-method-label d-flex align-items-center {{ $hasSubscription ? 'disabled-label' : '' }}" for="payment_method_cod">
                                            <i class="bi bi-cash-coin fs-4 me-2"></i>
                                            <span>Cash on Delivery</span>
                                            @if($hasSubscription)
                                            <span class="ms-2 badge bg-warning">Not available with subscriptions</span>
                                            @endif
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div id="stripe-payment-element" class="mb-4">
                                <!-- Stripe Elements Placeholder -->
                                <div class="mb-3">
                                    <label class="form-label d-flex align-items-center">
                                        <i class="bi bi-credit-card me-2"></i>{{ __('Card Information') }}
                                    </label>
                                    <div id="card-element" class="form-control" style="height: auto; padding: 10px;"></div>
                                    <div id="card-errors" class="text-danger mt-2" role="alert"></div>
                                    <div class="form-text mt-2">
                                        <div class="d-flex align-items-center">
                                            <i class="bi bi-shield-lock me-2"></i>
                                            <span>{{ __('Your payment information is secure and encrypted.') }}</span>
                                        </div>
                                    </div>
                                </div>
                                <!-- Payment token will be added dynamically by JavaScript -->
                            </div>

                            <!-- Cash on Delivery Information -->
                            <div id="cod-payment-info" class="mb-4" style="display: none;">
                                <div class="alert alert-info">
                                    <div class="d-flex">
                                        <i class="bi bi-cash-coin fs-4 me-3"></i>
                                        <div>
                                            <h5 class="mb-1">Cash on Delivery</h5>
                                            <p class="mb-0">Pay with cash when your order is delivered to your doorstep.</p>
                                            <ul class="mt-2 mb-0">
                                                <li>Have the exact amount ready</li>
                                                <li>Our delivery person will provide a receipt</li>
                                                <li>Available for all shipping addresses</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="notes" class="form-label d-flex align-items-center">
                                    <i class="bi bi-pencil-square me-2"></i>Order Notes (Optional)
                                </label>
                                <textarea class="form-control" id="notes" name="notes" rows="3">{{ old('notes') }}</textarea>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Order Summary -->
                <div class="col-lg-4">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-white py-3">
                            <h5 class="mb-0" style="color: #428677;">{{ __('Order Summary') }}</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive mb-3">
                                <table class="table table-sm">
                                    <thead class="visually-hidden">
                                        <tr>
                                            <th scope="col">Product</th>
                                            <th scope="col">Price</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($cart as $item)
                                            <tr>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <div>
                                                            <span>{{ $item['name'] }}</span>
                                                            <small class="d-block text-muted">
                                                                Qty: {{ $item['quantity'] }}
                                                                @if($item['is_subscription'])
                                                                    <span class="badge bg-primary">Subscription</span>
                                                                @endif
                                                            </small>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td class="text-end">${{ number_format($item['price'] * $item['quantity'], 2) }}</td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>

                            <div class="d-flex justify-content-between mb-2">
                                <span>Subtotal:</span>
                                <span>${{ number_format($subtotal, 2) }}</span>
                            </div>
                            <div class="mb-3">
                                <label for="shipping_option" class="form-label">Shipping Method:</label>
                                <form action="{{ route('checkout.update-shipping') }}" method="POST" id="shipping-form">
                                    @csrf
                                    <select class="form-select" id="shipping_option" name="shipping_id" onchange="document.getElementById('shipping-form').submit();">
                                        @foreach($shippingOptions as $option)
                                            <option value="{{ $option->id }}" {{ $selectedShipping && $selectedShipping->id == $option->id ? 'selected' : '' }}>
                                                {{ $option->name }} - {{ $option->cost > 0 ? '$'.number_format($option->cost, 2) : 'Free' }}
                                                @if($option->delivery_time)
                                                    ({{ $option->delivery_time }})
                                                @endif
                                            </option>
                                        @endforeach
                                    </select>
                                </form>
                                @if($selectedShipping && $selectedShipping->description)
                                    <small class="text-muted">{{ $selectedShipping->description }}</small>
                                @endif
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>Shipping:</span>
                                <span>{{ $shippingCost > 0 ? '$'.number_format($shippingCost, 2) : 'Free' }}</span>
                            </div>
                            <hr>
                            <div class="d-flex justify-content-between mb-4">
                                <span class="fw-bold">Total:</span>
                                <span class="fw-bold">${{ number_format($total, 2) }}</span>
                            </div>

                            @if($affiliateCode)
                                <div class="alert alert-success d-flex align-items-center mb-4">
                                    <i class="bi bi-check-circle-fill me-2"></i>
                                    <div>
                                        Affiliate code <strong>{{ $affiliateCode }}</strong> applied!
                                        <small class="d-block text-muted">The affiliate will receive a 10% commission on your purchase.</small>
                                    </div>
                                </div>
                            @endif

                            <div class="d-grid">
                                <button type="submit" class="btn btn-lg" style="background-color: #428677; color: white;">
                                    Place Order
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
        </div>
    </section>
@endsection

@push('styles')
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css">
<style>
    /* Select2 Styles */
    .select2-container--default .select2-selection--single {
        height: 38px;
        padding: 0.375rem 0.75rem;
        font-size: 1rem;
        border: 1px solid #ced4da;
        border-radius: 0.25rem;
    }

    .select2-container--default .select2-selection--single .select2-selection__rendered {
        line-height: 1.5;
        padding-left: 0;
        color: #212529;
    }

    .select2-container--default .select2-selection--single .select2-selection__arrow {
        height: 36px;
    }

    .select2-container--default .select2-results__option--highlighted[aria-selected] {
        background-color: #428677;
    }

    .select2-dropdown {
        border: 1px solid #ced4da;
    }

    .select2-search--dropdown .select2-search__field {
        padding: 0.375rem 0.75rem;
        border: 1px solid #ced4da;
        border-radius: 0.25rem;
    }

    /* Stripe Elements Styles */
    #card-element {
        background-color: white;
        padding: 12px;
        border-radius: 4px;
        border: 1px solid #ced4da;
        box-shadow: 0 1px 3px 0 #e6ebf1;
        -webkit-transition: box-shadow 150ms ease;
        transition: box-shadow 150ms ease;
    }

    #card-element--focus {
        box-shadow: 0 1px 3px 0 #cfd7df;
    }

    #card-element--invalid {
        border-color: #fa755a;
    }

    #card-errors {
        color: #fa755a;
        font-size: 14px;
        line-height: 20px;
        margin-top: 8px;
        text-align: left;
    }

    /* Payment method styles */
    .payment-method-option {
        position: relative;
    }

    .payment-method-label {
        padding: 10px 15px;
        border: 1px solid #ced4da;
        border-radius: 5px;
        cursor: pointer;
        transition: all 0.2s ease;
        background-color: #f8f9fa;
    }

    .payment-method-option.active .payment-method-label,
    .payment-method-label:hover {
        background-color: #e9ecef;
        border-color: #adb5bd;
    }

    .payment-method-option input:checked + .payment-method-label {
        border-color: #428677;
        background-color: rgba(66, 134, 119, 0.1);
    }

    /* Disabled payment method styling */
    .payment-method-option.disabled {
        opacity: 0.7;
    }

    .payment-method-option.disabled .payment-method-label {
        cursor: not-allowed;
        background-color: #f8f9fa;
        border-color: #e9ecef;
    }

    .payment-method-option.disabled .payment-method-label:hover {
        background-color: #f8f9fa;
        border-color: #e9ecef;
    }

    .disabled-label {
        cursor: not-allowed !important;
    }

    /* Payment Processing Overlay */
    .payment-processing-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.7);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.3s ease, visibility 0.3s ease;
    }

    .payment-processing-overlay.active {
        opacity: 1;
        visibility: visible;
    }

    .payment-processing-content {
        background-color: white;
        padding: 30px;
        border-radius: 10px;
        text-align: center;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        max-width: 400px;
        width: 90%;
        transform: translateY(20px);
        opacity: 0;
        transition: transform 0.5s ease, opacity 0.5s ease;
    }

    .payment-processing-overlay.active .payment-processing-content {
        transform: translateY(0);
        opacity: 1;
    }

    .spinner {
        width: 70px;
        height: 70px;
        margin: 0 auto 20px;
        border: 5px solid rgba(66, 134, 119, 0.2);
        border-radius: 50%;
        border-top-color: #428677;
        animation: spin 1s ease-in-out infinite;
    }

    @keyframes spin {
        to { transform: rotate(360deg); }
    }

    /* Toast Notification Styling */
    .toastify {
        padding: 16px 20px;
        border-radius: 8px;
        font-weight: 500;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    }

    .toast-success {
        background: linear-gradient(135deg, #428677, #2d504b);
    }

    .toast-error {
        background: linear-gradient(135deg, #e74c3c, #c0392b);
    }

    .toast-info {
        background: linear-gradient(135deg, #3498db, #2980b9);
    }
</style>
@endpush

@push('scripts')
<script src="https://js.stripe.com/v3/"></script>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/toastify-js"></script>
<script>
    // Create payment processing overlay
    const paymentOverlay = document.createElement('div');
    paymentOverlay.className = 'payment-processing-overlay';
    paymentOverlay.innerHTML = `
        <div class="payment-processing-content">
            <div class="spinner"></div>
            <h4 style="color: #428677; margin-bottom: 10px;">{{ __('Processing payment...') }}</h4>
            <p class="text-muted">{{ __('Please wait while we process your payment') }}</p>
        </div>
    `;
    document.body.appendChild(paymentOverlay);

    // Notification functions
    function showNotification(message, type = 'info') {
        const toastClass = type === 'success' ? 'toast-success' :
                          type === 'error' ? 'toast-error' : 'toast-info';

        Toastify({
            text: message,
            duration: 5000,
            close: true,
            gravity: "top",
            position: "right",
            className: toastClass,
            stopOnFocus: true
        }).showToast();
    }

    function showPaymentProcessing(show = true) {
        if (show) {
            paymentOverlay.classList.add('active');
        } else {
            paymentOverlay.classList.remove('active');
        }
    }

    document.addEventListener('DOMContentLoaded', function() {
        // Show a welcome notification
        showNotification('{{ __("Welcome to checkout") }}', 'info');

        // Initialize Select2 for country dropdowns
        $(document).ready(function() {
            $('#billing_country').select2({
                placeholder: 'Select a country',
                allowClear: true,
                width: '100%'
            });

            $('#shipping_country').select2({
                placeholder: 'Select a country',
                allowClear: true,
                width: '100%'
            });
        });
        const sameAsBillingCheckbox = document.getElementById('same-as-billing');
        const shippingFields = document.getElementById('shipping-fields');

        sameAsBillingCheckbox.addEventListener('change', function() {
            if (this.checked) {
                shippingFields.style.display = 'none';
            } else {
                shippingFields.style.display = 'block';
            }
        });

        // Initialize Stripe with your publishable key
        const stripe = Stripe('{{ env('STRIPE_KEY') }}');
        const elements = stripe.elements();

        // Create card elements with improved styling
        const cardElement = elements.create('card', {
            style: {
                base: {
                    color: '#32325d',
                    fontFamily: '"Helvetica Neue", Helvetica, sans-serif',
                    fontSmoothing: 'antialiased',
                    fontSize: '16px',
                    '::placeholder': {
                        color: '#aab7c4'
                    },
                    iconColor: '#428677'
                },
                invalid: {
                    color: '#fa755a',
                    iconColor: '#fa755a'
                }
            },
            hidePostalCode: false
        });

        // Mount the card element
        cardElement.mount('#card-element');

        // Handle real-time validation errors from the card Element
        cardElement.on('change', function(event) {
            const displayError = document.getElementById('card-errors');
            if (event.error) {
                displayError.textContent = event.error.message;
                console.log('Card validation error:', event.error.message);
            } else {
                displayError.textContent = '';
            }

            // Add visual feedback when card details are complete
            if (event.complete) {
                console.log('Card validation complete');
                // Add a subtle animation to indicate completion
                const cardElement = document.getElementById('card-element');
                cardElement.style.transition = 'all 0.3s ease';
                cardElement.style.boxShadow = '0 0 0 2px rgba(66, 134, 119, 0.5)';
                setTimeout(() => {
                    cardElement.style.boxShadow = '0 1px 3px 0 #e6ebf1';
                }, 1000);
            }
        });

        // Handle form submission
        document.getElementById('checkout-form').addEventListener('submit', function(event) {
            event.preventDefault();

            console.log('Form submission started');

            // Validate all required fields
            const requiredFields = document.querySelectorAll('[required]');
            let missingFields = [];

            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    field.classList.add('is-invalid');
                    missingFields.push(field.id || field.name);
                }
            });

            if (missingFields.length > 0) {
                console.log('Missing required fields:', missingFields);
                showNotification('{{ __("Please fill in all required fields before proceeding.") }}', 'error');

                // Re-enable the submit button if it was disabled
                const submitButton = this.querySelector('button[type="submit"]');
                if (submitButton.disabled) {
                    submitButton.disabled = false;
                    submitButton.innerHTML = '{{ __("Place Order") }}';
                }

                // Scroll to the first missing field
                if (missingFields.length > 0) {
                    const firstMissingField = document.getElementById(missingFields[0]);
                    if (firstMissingField) {
                        firstMissingField.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        firstMissingField.focus();
                    }
                }

                return;
            }

            // Validate zip code specifically
            const zipCode = document.getElementById('billing_zipcode').value.trim();
            const country = document.getElementById('billing_country').value;

            if (!zipCode) {
                console.log('Missing zip code');
                document.getElementById('billing_zipcode').classList.add('is-invalid');

                // Create error message if it doesn't exist
                let zipCodeError = document.getElementById('zipcode-error');
                if (!zipCodeError) {
                    zipCodeError = document.createElement('div');
                    zipCodeError.id = 'zipcode-error';
                    zipCodeError.className = 'invalid-feedback';
                    document.getElementById('billing_zipcode').parentNode.appendChild(zipCodeError);
                }
                zipCodeError.textContent = '{{ __("Please enter a valid zip/postal code.") }}';

                // Show notification
                showNotification('{{ __("Please enter a valid zip/postal code.") }}', 'error');

                // Scroll to the zip code field
                document.getElementById('billing_zipcode').scrollIntoView({ behavior: 'smooth', block: 'center' });
                document.getElementById('billing_zipcode').focus();

                // Re-enable the submit button if it was disabled
                const submitButton = this.querySelector('button[type="submit"]');
                if (submitButton.disabled) {
                    submitButton.disabled = false;
                    submitButton.innerHTML = '{{ __("Place Order") }}';
                }

                return;
            }

            // Basic zip code format validation based on country
            let zipCodeValid = true;
            let zipCodeError = '';

            if (country === 'US' && !/^\d{5}(-\d{4})?$/.test(zipCode)) {
                zipCodeValid = false;
                zipCodeError = '{{ __("US zip codes must be 5 digits or 5+4 digits (e.g., 12345 or 12345-6789)") }}';
            } else if (country === 'CA' && !/^[A-Za-z]\d[A-Za-z][ -]?\d[A-Za-z]\d$/.test(zipCode)) {
                zipCodeValid = false;
                zipCodeError = '{{ __("Canadian postal codes must be in format A1A 1A1") }}';
            } else if (country === 'GB' && !/^[A-Za-z]{1,2}\d[A-Za-z\d]? \d[A-Za-z]{2}$/.test(zipCode)) {
                zipCodeValid = false;
                zipCodeError = '{{ __("UK postcodes must be in a valid format (e.g., SW1A 1AA)") }}';
            }

            if (!zipCodeValid) {
                console.log('Invalid zip code format:', { country, zipCode, error: zipCodeError });
                document.getElementById('billing_zipcode').classList.add('is-invalid');

                // Create error message if it doesn't exist
                let zipCodeErrorElement = document.getElementById('zipcode-error');
                if (!zipCodeErrorElement) {
                    zipCodeErrorElement = document.createElement('div');
                    zipCodeErrorElement.id = 'zipcode-error';
                    zipCodeErrorElement.className = 'invalid-feedback';
                    document.getElementById('billing_zipcode').parentNode.appendChild(zipCodeErrorElement);
                }
                zipCodeErrorElement.textContent = zipCodeError;

                // Show notification
                showNotification(zipCodeError, 'error');

                // Scroll to the zip code field
                document.getElementById('billing_zipcode').scrollIntoView({ behavior: 'smooth', block: 'center' });
                document.getElementById('billing_zipcode').focus();

                // Re-enable the submit button if it was disabled
                const submitButton = this.querySelector('button[type="submit"]');
                if (submitButton.disabled) {
                    submitButton.disabled = false;
                    submitButton.innerHTML = '{{ __("Place Order") }}';
                }

                return;
            }

            // If we get here, the zip code is valid
            document.getElementById('billing_zipcode').classList.remove('is-invalid');
            document.getElementById('billing_zipcode').classList.add('is-valid');

            // Get the selected payment method
            const selectedMethod = document.querySelector('input[name="payment_method"]:checked').value;
            console.log('Payment method selected:', selectedMethod);

            // Show the payment processing overlay
            showPaymentProcessing(true);

            // Disable the submit button to prevent repeated clicks
            const submitButton = this.querySelector('button[type="submit"]');
            submitButton.disabled = true;
            submitButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> {{ __("Processing...") }}';

            // If Cash on Delivery is selected, submit the form directly
            if (selectedMethod === 'cod') {
                console.log('COD payment selected');

                try {
                    // Add a dummy payment token for COD
                    const hiddenInput = document.createElement('input');
                    hiddenInput.setAttribute('type', 'hidden');
                    hiddenInput.setAttribute('name', 'payment_token');
                    hiddenInput.setAttribute('value', 'cod_' + Date.now());
                    event.target.appendChild(hiddenInput);

                    console.log('COD token added');

                    // Show success notification
                    showNotification('{{ __("Cash on Delivery selected. Processing your order...") }}', 'info');

                    // Small delay to ensure the animation is visible
                    setTimeout(() => {
                        console.log('Submitting form with COD payment');
                        // Submit the form
                        event.target.submit();
                    }, 1500);

                    return;
                } catch (error) {
                    console.error('Error in COD submission:', error);

                    // Hide the payment processing overlay
                    showPaymentProcessing(false);

                    // Re-enable the submit button
                    submitButton.disabled = false;
                    submitButton.innerHTML = '{{ __("Place Order") }}';

                    // Show error notification
                    showNotification('{{ __("An error occurred while processing your order. Please try again.") }}', 'error');
                    return;
                }
            }

            // For credit card payments, we'll use Stripe Elements directly
            // No need for manual validation as Stripe Elements handles this

            // Get billing details for better Stripe data
            const billingDetails = {
                name: document.getElementById('billing_name').value,
                email: document.getElementById('billing_email').value,
                address: {
                    line1: document.getElementById('billing_address').value,
                    city: document.getElementById('billing_city').value,
                    state: document.getElementById('billing_state').value,
                    postal_code: document.getElementById('billing_zipcode').value,
                    country: document.getElementById('billing_country').value
                }
            };

            // Create a payment method using the card element
            console.log('Starting Stripe payment processing');

            stripe.createPaymentMethod({
                type: 'card',
                card: cardElement,
                billing_details: billingDetails
            }).then(function(result) {
                if (result.error) {
                    console.error('Stripe payment error:', result.error);

                    // Hide the payment processing overlay
                    showPaymentProcessing(false);

                    // Display error
                    const errorElement = document.getElementById('card-errors');
                    errorElement.textContent = result.error.message;
                    errorElement.style.visibility = 'visible';
                    errorElement.style.position = 'static';

                    // Show error notification
                    showNotification(result.error.message, 'error');

                    // Re-enable the submit button
                    submitButton.disabled = false;
                    submitButton.innerHTML = '{{ __("Place Order") }}';
                } else {
                    console.log('Stripe payment success:', result.paymentMethod.id);

                    // Add payment method ID to form
                    const hiddenInput = document.createElement('input');
                    hiddenInput.setAttribute('type', 'hidden');
                    hiddenInput.setAttribute('name', 'payment_token');
                    hiddenInput.setAttribute('value', result.paymentMethod.id);
                    event.target.appendChild(hiddenInput);

                    // Add total amount to form for verification
                    const amountInput = document.createElement('input');
                    amountInput.setAttribute('type', 'hidden');
                    amountInput.setAttribute('name', 'payment_amount');
                    amountInput.setAttribute('value', '{{ $total }}');
                    event.target.appendChild(amountInput);

                    console.log('Form inputs added');

                    // Show success notification
                    showNotification('{{ __("Payment method verified. Processing your order...") }}', 'success');

                    // Submit the form after a short delay to show the animation
                    setTimeout(() => {
                        console.log('Submitting form with Stripe payment');
                        try {
                            event.target.submit();
                        } catch (error) {
                            console.error('Error submitting form:', error);

                            // Hide the payment processing overlay
                            showPaymentProcessing(false);

                            // Show error notification
                            showNotification('{{ __("An error occurred while submitting the form. Please try again.") }}', 'error');

                            // Re-enable the submit button
                            submitButton.disabled = false;
                            submitButton.innerHTML = '{{ __("Place Order") }}';
                        }
                    }, 1500); // Small delay to show the animation
                }
            }).catch(function(error) {
                console.error('Stripe.js error:', error);

                // Hide the payment processing overlay
                showPaymentProcessing(false);

                // Re-enable the submit button
                submitButton.disabled = false;
                submitButton.innerHTML = '{{ __("Place Order") }}';

                // Show error notification
                showNotification('{{ __("An error occurred while processing your payment. Please try again.") }}', 'error');
            });
        });

        // We're using Stripe Elements for validation, so no need for custom validation functions

        // Debug panel removed

        // Payment method styling is now in the CSS section

        // Handle payment method selection
        const paymentMethodOptions = document.querySelectorAll('.payment-method-option');
        const stripePaymentElement = document.getElementById('stripe-payment-element');
        const codPaymentInfo = document.getElementById('cod-payment-info');

        // Function to toggle payment method display
        function togglePaymentMethod() {
            const selectedMethod = document.querySelector('input[name="payment_method"]:checked').value;

            // Toggle payment method sections
            if (selectedMethod === 'stripe') {
                stripePaymentElement.style.display = 'block';
                codPaymentInfo.style.display = 'none';

                // We're using Stripe Elements now, so no need to set required attributes
                // for individual card fields
            } else if (selectedMethod === 'cod') {
                stripePaymentElement.style.display = 'none';
                codPaymentInfo.style.display = 'block';

                // We're using Stripe Elements now, so no need to set required attributes
                // for individual card fields
            }
        }

        // Check if there are subscription products in the cart
        const hasSubscription = {{ $hasSubscription ? 'true' : 'false' }};

        // If there are subscription products, ensure Stripe is selected
        if (hasSubscription) {
            const stripeRadio = document.getElementById('payment_method_stripe');
            if (stripeRadio) {
                stripeRadio.checked = true;

                // Show a notification explaining why COD is disabled
                showNotification('{{ __("Cash on Delivery is not available for subscription products. Credit card payment is required.") }}', 'info');
            }
        }

        // Initialize payment method display
        togglePaymentMethod();

        // Add event listeners to payment method options
        paymentMethodOptions.forEach(option => {
            const input = option.querySelector('input[type="radio"]');
            input.addEventListener('change', function() {
                // Update active class
                paymentMethodOptions.forEach(opt => {
                    opt.classList.toggle('active', opt.querySelector('input').checked);
                });

                // Toggle payment method display
                togglePaymentMethod();
            });
        });
    });
</script>
@endpush
