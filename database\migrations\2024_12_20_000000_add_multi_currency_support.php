<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add currency support to products table
        Schema::table('products', function (Blueprint $table) {
            if (!Schema::hasColumn('products', 'currency')) {
                $table->string('currency', 3)->default('EUR')->after('subscription_price');
            }
        });

        // Add currency support to orders table
        Schema::table('orders', function (Blueprint $table) {
            if (!Schema::hasColumn('orders', 'currency')) {
                $table->string('currency', 3)->default('EUR')->after('total');
            }
        });

        // Add currency support to order_items table
        Schema::table('order_items', function (Blueprint $table) {
            if (!Schema::hasColumn('order_items', 'currency')) {
                $table->string('currency', 3)->default('EUR')->after('price');
            }
        });

        // Create currencies table for exchange rates and settings
        Schema::create('currencies', function (Blueprint $table) {
            $table->id();
            $table->string('code', 3)->unique(); // EUR, USD, XOF, CNY
            $table->string('name'); // Euro, US Dollar, CFA Franc, Chinese Yuan
            $table->string('symbol'); // €, $, CFA, ¥
            $table->decimal('exchange_rate', 10, 6)->default(1.000000); // Rate to base currency (EUR)
            $table->boolean('is_active')->default(true);
            $table->boolean('is_default')->default(false);
            $table->timestamps();
        });

        // Create user currency preferences table
        Schema::create('user_currency_preferences', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('currency_code', 3);
            $table->timestamps();
            
            $table->unique(['user_id']);
            $table->foreign('currency_code')->references('code')->on('currencies');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_currency_preferences');
        Schema::dropIfExists('currencies');
        
        Schema::table('order_items', function (Blueprint $table) {
            if (Schema::hasColumn('order_items', 'currency')) {
                $table->dropColumn('currency');
            }
        });

        Schema::table('orders', function (Blueprint $table) {
            if (Schema::hasColumn('orders', 'currency')) {
                $table->dropColumn('currency');
            }
        });

        Schema::table('products', function (Blueprint $table) {
            if (Schema::hasColumn('products', 'currency')) {
                $table->dropColumn('currency');
            }
        });
    }
};
