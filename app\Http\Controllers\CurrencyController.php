<?php

namespace App\Http\Controllers;

use App\Services\CurrencyService;
use Illuminate\Http\Request;

class CurrencyController extends Controller
{
    /**
     * Change the user's currency preference.
     */
    public function changeCurrency(Request $request)
    {
        $request->validate([
            'currency' => 'required|string|size:3'
        ]);

        $currencyCode = strtoupper($request->currency);
        
        if (CurrencyService::setUserCurrency($currencyCode)) {
            return response()->json([
                'success' => true,
                'message' => 'Currency changed successfully',
                'currency' => $currencyCode,
                'symbol' => CurrencyService::getSymbol($currencyCode)
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Invalid or inactive currency'
        ], 400);
    }

    /**
     * Get all active currencies.
     */
    public function getCurrencies()
    {
        $currencies = CurrencyService::getActiveCurrencies();
        
        return response()->json([
            'success' => true,
            'currencies' => $currencies->map(function ($currency) {
                return [
                    'code' => $currency->code,
                    'name' => $currency->name,
                    'symbol' => $currency->symbol,
                    'is_default' => $currency->is_default
                ];
            }),
            'current' => CurrencyService::getUserCurrency()
        ]);
    }

    /**
     * Convert amount between currencies.
     */
    public function convert(Request $request)
    {
        $request->validate([
            'amount' => 'required|numeric|min:0',
            'from' => 'required|string|size:3',
            'to' => 'required|string|size:3'
        ]);

        $convertedAmount = CurrencyService::convert(
            $request->amount,
            strtoupper($request->from),
            strtoupper($request->to)
        );

        return response()->json([
            'success' => true,
            'original_amount' => $request->amount,
            'converted_amount' => $convertedAmount,
            'from_currency' => strtoupper($request->from),
            'to_currency' => strtoupper($request->to),
            'formatted' => CurrencyService::format($convertedAmount, strtoupper($request->to))
        ]);
    }
}
