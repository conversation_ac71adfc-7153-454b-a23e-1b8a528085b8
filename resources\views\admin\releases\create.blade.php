@extends('layouts.admin')

@section('title', 'Create Release')

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Create New Release</h1>
        <a href="{{ route('admin.releases.index') }}" class="btn btn-secondary">
            <i class="bi bi-arrow-left"></i> Back to Releases
        </a>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold">Release Information</h6>
        </div>
        <div class="card-body">
            <form action="{{ route('admin.releases.store') }}" method="POST">
                @csrf
                <div class="mb-3">
                    <label for="version" class="form-label">Version <span class="text-danger">*</span></label>
                    <input type="text" class="form-control @error('version') is-invalid @enderror" id="version" name="version" value="{{ old('version') }}" required>
                    @error('version')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                    <small class="form-text text-muted">Example: 1.0.0, 2.3.1, etc.</small>
                </div>

                <div class="mb-3">
                    <label for="name" class="form-label">Name <span class="text-danger">*</span></label>
                    <input type="text" class="form-control @error('name') is-invalid @enderror" id="name" name="name" value="{{ old('name') }}" required>
                    @error('name')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                    <small class="form-text text-muted">A descriptive name for this release.</small>
                </div>

                <div class="mb-3">
                    <label for="description" class="form-label">Description</label>
                    <textarea class="form-control @error('description') is-invalid @enderror" id="description" name="description" rows="5">{{ old('description') }}</textarea>
                    @error('description')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                    <small class="form-text text-muted">Provide a detailed description of what's included in this release.</small>
                </div>

                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-save"></i> Create Release
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection
