<?php $__env->startPush('styles'); ?>
<style>
    :root {
        --natryon-green: #024C3D;
        --natryon-cream: #F6E8D2;
        --natryon-cream-light: #FAF2E8;
        --natryon-text: #1A2E29;
        --natryon-text-light: #4D6661;
        --natryon-border: rgba(2, 76, 61, 0.1);
        --natryon-green-pale: rgba(2, 76, 61, 0.05);
    }

    .auth-container {
        min-height: 100vh;
        background: linear-gradient(135deg, var(--natryon-cream-light) 0%, var(--natryon-cream) 100%);
        position: relative;
        overflow: hidden;
    }

    .auth-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='rgba(2, 76, 61, 0.03)' fill-rule='evenodd'/%3E%3C/svg%3E");
        opacity: 0.5;
        z-index: 1;
    }

    .auth-card {
        background: white;
        border-radius: 1rem;
        box-shadow: 0 20px 40px rgba(2, 76, 61, 0.08);
        border: 1px solid var(--natryon-border);
        backdrop-filter: blur(10px);
        position: relative;
        z-index: 2;
        overflow: hidden;
    }

    .auth-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 4px;
        background: linear-gradient(90deg, var(--natryon-green) 0%, var(--natryon-cream) 100%);
    }

    .auth-header {
        text-align: center;
        margin-bottom: 2rem;
        position: relative;
        z-index: 2;
    }

    .auth-title {
        font-size: 2.5rem;
        font-weight: 700;
        color: var(--natryon-text);
        margin-bottom: 0.5rem;
        letter-spacing: -0.02em;
    }

    .auth-subtitle {
        color: var(--natryon-text-light);
        font-size: 1.1rem;
        font-weight: 400;
    }

    .auth-form-group {
        margin-bottom: 1.5rem;
    }

    .auth-label {
        font-weight: 600;
        color: var(--natryon-text);
        margin-bottom: 0.5rem;
        font-size: 0.95rem;
        letter-spacing: 0.3px;
    }

    .auth-input-group {
        position: relative;
    }

    .auth-input-icon {
        position: absolute;
        left: 1rem;
        top: 50%;
        transform: translateY(-50%);
        color: var(--natryon-text-light);
        z-index: 3;
        font-size: 1.1rem;
    }

    .auth-input {
        width: 100%;
        padding: 0.875rem 1rem 0.875rem 3rem;
        border: 2px solid var(--natryon-border);
        border-radius: 0.75rem;
        font-size: 1rem;
        background-color: var(--natryon-green-pale);
        color: var(--natryon-text);
        transition: all 0.3s ease;
    }

    .auth-input:focus {
        outline: none;
        border-color: var(--natryon-green);
        background-color: white;
        box-shadow: 0 0 0 3px rgba(2, 76, 61, 0.1);
    }

    .auth-input.is-invalid {
        border-color: #dc3545;
        background-color: rgba(220, 53, 69, 0.05);
    }

    .auth-error {
        color: #dc3545;
        font-size: 0.875rem;
        margin-top: 0.5rem;
        font-weight: 500;
    }

    .auth-forgot-link {
        color: var(--natryon-green);
        text-decoration: none;
        font-size: 0.875rem;
        font-weight: 500;
        transition: color 0.3s ease;
    }

    .auth-forgot-link:hover {
        color: var(--natryon-text);
    }

    .auth-checkbox {
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .auth-checkbox input[type="checkbox"] {
        width: 1.25rem;
        height: 1.25rem;
        accent-color: var(--natryon-green);
        border-radius: 0.25rem;
    }

    .auth-checkbox label {
        color: var(--natryon-text);
        font-size: 0.95rem;
        font-weight: 500;
        cursor: pointer;
    }

    .auth-btn {
        width: 100%;
        padding: 1rem 2rem;
        background: linear-gradient(135deg, var(--natryon-green) 0%, #1a5a4a 100%);
        color: white;
        border: none;
        border-radius: 0.75rem;
        font-size: 1.1rem;
        font-weight: 600;
        letter-spacing: 0.5px;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .auth-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s ease;
    }

    .auth-btn:hover::before {
        left: 100%;
    }

    .auth-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(2, 76, 61, 0.3);
    }

    .auth-btn:active {
        transform: translateY(0);
    }

    .auth-link {
        color: var(--natryon-green);
        text-decoration: none;
        font-weight: 600;
        transition: color 0.3s ease;
    }

    .auth-link:hover {
        color: var(--natryon-text);
    }

    .auth-footer-text {
        color: var(--natryon-text-light);
        font-size: 0.95rem;
    }

    .logo-container {
        text-align: center;
        margin-bottom: 2rem;
    }

    .logo-container img {
        max-height: 80px;
        width: auto;
    }

    @media (max-width: 768px) {
        .auth-title {
            font-size: 2rem;
        }

        .auth-card {
            margin: 1rem;
        }
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="auth-container d-flex align-items-center justify-content-center">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8 col-lg-5 col-xl-4">
                <div class="auth-card p-4 p-md-5">
                    <div class="logo-container">
                        <img src="<?php echo e(asset('images/logos/logo natryon noir copie.png')); ?>" alt="NATRYON" class="img-fluid">
                    </div>

                    <div class="auth-header">
                        <h1 class="auth-title"><?php echo e(__('Welcome Back')); ?></h1>
                        <p class="auth-subtitle"><?php echo e(__('Sign in to your NATRYON account')); ?></p>
                    </div>

                    <form method="POST" action="<?php echo e(route('login')); ?>">
                        <?php echo csrf_field(); ?>

                        <div class="auth-form-group">
                            <label for="email" class="auth-label"><?php echo e(__('Email Address')); ?></label>
                            <div class="auth-input-group">
                                <i class="bi bi-envelope auth-input-icon"></i>
                                <input id="email" type="email" class="auth-input <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" name="email" value="<?php echo e(old('email')); ?>" required autocomplete="email" autofocus placeholder="<?php echo e(__('Enter your email address')); ?>">
                            </div>
                            <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="auth-error">
                                    <strong><?php echo e($message); ?></strong>
                                </div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="auth-form-group">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <label for="password" class="auth-label mb-0"><?php echo e(__('Password')); ?></label>
                                <?php if(Route::has('password.request')): ?>
                                    <a class="auth-forgot-link" href="<?php echo e(route('password.request')); ?>">
                                        <?php echo e(__('Forgot Password?')); ?>

                                    </a>
                                <?php endif; ?>
                            </div>
                            <div class="auth-input-group">
                                <i class="bi bi-lock auth-input-icon"></i>
                                <input id="password" type="password" class="auth-input <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" name="password" required autocomplete="current-password" placeholder="<?php echo e(__('Enter your password')); ?>">
                            </div>
                            <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="auth-error">
                                    <strong><?php echo e($message); ?></strong>
                                </div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="auth-form-group">
                            <div class="auth-checkbox">
                                <input type="checkbox" name="remember" id="remember" <?php echo e(old('remember') ? 'checked' : ''); ?>>
                                <label for="remember"><?php echo e(__('Remember me')); ?></label>
                            </div>
                        </div>

                        <div class="auth-form-group">
                            <button type="submit" class="auth-btn">
                                <i class="bi bi-box-arrow-in-right me-2"></i><?php echo e(__('Sign In')); ?>

                            </button>
                        </div>

                        <div class="text-center">
                            <p class="auth-footer-text mb-0">
                                <?php echo e(__("Don't have an account?")); ?>

                                <a href="<?php echo e(route('register')); ?>" class="auth-link"><?php echo e(__('Create one now')); ?></a>
                            </p>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\projects\natryon\resources\views/auth/login.blade.php ENDPATH**/ ?>