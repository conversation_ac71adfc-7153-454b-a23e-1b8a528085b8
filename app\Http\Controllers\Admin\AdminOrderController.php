<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Order;
use App\Models\User;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Facades\Mail;
use App\Mail\OrderInvoice;

class AdminOrderController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Order::query();

        // Search
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('order_number', 'like', "%{$search}%")
                  ->orWhere('billing_name', 'like', "%{$search}%")
                  ->orWhere('billing_email', 'like', "%{$search}%");
            });
        }

        // Status filter
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        // Payment filter
        if ($request->has('payment')) {
            $query->where('is_paid', $request->payment === 'paid');
        }

        // User filter
        if ($request->has('user')) {
            $query->where('user_id', $request->user);
        }

        // Product filter
        if ($request->has('product')) {
            $query->whereHas('items', function ($q) use ($request) {
                $q->where('product_id', $request->product);
            });
        }

        // Sort
        if ($request->has('sort')) {
            switch ($request->sort) {
                case 'newest':
                    $query->orderBy('created_at', 'desc');
                    break;
                case 'oldest':
                    $query->orderBy('created_at', 'asc');
                    break;
                case 'total_high':
                    $query->orderBy('total', 'desc');
                    break;
                case 'total_low':
                    $query->orderBy('total', 'asc');
                    break;
                default:
                    $query->orderBy('created_at', 'desc');
            }
        } else {
            $query->orderBy('created_at', 'desc');
        }

        $orders = $query->paginate(10);

        return view('admin.orders.index', compact('orders'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $users = User::all();
        return view('admin.orders.create', compact('users'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // Order creation logic
        return redirect()->route('admin.orders.index')->with('success', 'Order created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $order = Order::with(['items.product', 'user'])->findOrFail($id);

        // Get affiliate user if applicable
        $affiliateUser = null;
        $affiliateCommission = 0;

        if ($order->affiliate_code) {
            $affiliateCode = \App\Models\AffiliateCode::where('code', $order->affiliate_code)->first();
            if ($affiliateCode) {
                $affiliateUser = $affiliateCode->user;
                $affiliateCommission = ($order->total * $affiliateCode->commission_rate) / 100;
            }
        }

        return view('admin.orders.show', compact('order', 'affiliateUser', 'affiliateCommission'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $order = Order::with('items.product')->findOrFail($id);
        $users = User::all();
        return view('admin.orders.edit', compact('order', 'users'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $order = Order::findOrFail($id);

        // Update status
        if ($request->has('status')) {
            $order->status = $request->status;
        }

        // Update payment status
        if ($request->has('is_paid')) {
            $order->is_paid = $request->is_paid;
            if ($request->is_paid && !$order->paid_at) {
                $order->paid_at = now();
            } elseif (!$request->is_paid) {
                $order->paid_at = null;
            }
        }

        $order->save();

        return redirect()->route('admin.orders.show', $order->id)->with('success', 'Order updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $order = Order::findOrFail($id);
        $order->delete();

        return redirect()->route('admin.orders.index')->with('success', 'Order deleted successfully.');
    }

    /**
     * Generate PDF invoice for the order
     */
    public function generatePdf(string $id)
    {
        $order = Order::with(['items.product', 'user'])->findOrFail($id);

        // Get affiliate user if applicable
        $affiliateUser = null;
        $affiliateCommission = 0;

        if ($order->affiliate_code) {
            $affiliateCode = \App\Models\AffiliateCode::where('code', $order->affiliate_code)->first();
            if ($affiliateCode) {
                $affiliateUser = $affiliateCode->user;
                $affiliateCommission = ($order->total * $affiliateCode->commission_rate) / 100;
            }
        }

        $pdf = PDF::loadView('admin.orders.pdf', compact('order', 'affiliateUser', 'affiliateCommission'));

        return $pdf->download('invoice-' . $order->order_number . '.pdf');
    }

    /**
     * Send invoice email to customer
     */
    public function sendEmail(string $id)
    {
        $order = Order::with(['items.product', 'user'])->findOrFail($id);

        // Generate PDF
        $pdf = PDF::loadView('admin.orders.pdf', compact('order'));

        // Send email with PDF attachment
        Mail::to($order->billing_email)->send(new OrderInvoice($order, $pdf));

        return redirect()->route('admin.orders.show', $order->id)->with('success', 'Invoice email sent successfully to ' . $order->billing_email);
    }
}
