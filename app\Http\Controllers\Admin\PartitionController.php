<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Partition;
use App\Models\Release;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class PartitionController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $partitions = Partition::with('release')
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return view('admin.partitions.index', compact('partitions'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $releases = Release::where('status', 'draft')
            ->orderBy('created_at', 'desc')
            ->get();

        $partitionTypes = [
            'frontend' => 'Frontend',
            'backend' => 'Backend',
            'database' => 'Database',
            'config' => 'Configuration',
            'assets' => 'Assets',
            'other' => 'Other',
        ];

        return view('admin.partitions.create', compact('releases', 'partitionTypes'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'release_id' => 'required|exists:releases,id',
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'type' => 'required|in:frontend,backend,database,config,assets,other',
            'deployment_order' => 'required|integer|min:0',
            'file_paths' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Check if the release is in draft status
        $release = Release::findOrFail($request->release_id);
        if ($release->status !== 'draft') {
            return redirect()->back()
                ->with('error', 'Partitions can only be added to releases in draft status.')
                ->withInput();
        }

        $partition = new Partition();
        $partition->release_id = $request->release_id;
        $partition->name = $request->name;
        $partition->description = $request->description;
        $partition->type = $request->type;
        $partition->deployment_order = $request->deployment_order;
        $partition->status = 'pending';

        // Process file paths
        if ($request->file_paths) {
            $paths = array_map('trim', explode("\n", $request->file_paths));
            $partition->file_paths = array_filter($paths);
        }

        $partition->save();

        return redirect()->route('admin.releases.show', $partition->release_id)
            ->with('success', 'Partition created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $partition = Partition::with('release')
            ->findOrFail($id);

        return view('admin.partitions.show', compact('partition'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $partition = Partition::findOrFail($id);

        // Only allow editing of partitions for releases in draft status
        if ($partition->release->status !== 'draft') {
            return redirect()->route('admin.partitions.show', $partition->id)
                ->with('error', 'Only partitions for draft releases can be edited.');
        }

        $partitionTypes = [
            'frontend' => 'Frontend',
            'backend' => 'Backend',
            'database' => 'Database',
            'config' => 'Configuration',
            'assets' => 'Assets',
            'other' => 'Other',
        ];

        return view('admin.partitions.edit', compact('partition', 'partitionTypes'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $partition = Partition::findOrFail($id);

        // Only allow updating of partitions for releases in draft status
        if ($partition->release->status !== 'draft') {
            return redirect()->route('admin.partitions.show', $partition->id)
                ->with('error', 'Only partitions for draft releases can be updated.');
        }

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'type' => 'required|in:frontend,backend,database,config,assets,other',
            'deployment_order' => 'required|integer|min:0',
            'file_paths' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $partition->name = $request->name;
        $partition->description = $request->description;
        $partition->type = $request->type;
        $partition->deployment_order = $request->deployment_order;

        // Process file paths
        if ($request->file_paths) {
            $paths = array_map('trim', explode("\n", $request->file_paths));
            $partition->file_paths = array_filter($paths);
        } else {
            $partition->file_paths = [];
        }

        $partition->save();

        return redirect()->route('admin.partitions.show', $partition->id)
            ->with('success', 'Partition updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $partition = Partition::findOrFail($id);

        // Only allow deletion of partitions for releases in draft status
        if ($partition->release->status !== 'draft') {
            return redirect()->route('admin.partitions.show', $partition->id)
                ->with('error', 'Only partitions for draft releases can be deleted.');
        }

        $releaseId = $partition->release_id;
        $partition->delete();

        return redirect()->route('admin.releases.show', $releaseId)
            ->with('success', 'Partition deleted successfully.');
    }

    /**
     * Deploy the partition.
     */
    public function deploy(Request $request, string $id)
    {
        $partition = Partition::findOrFail($id);

        // Only allow deployment of partitions for releases in approved status
        if ($partition->release->status !== 'approved') {
            return redirect()->route('admin.partitions.show', $partition->id)
                ->with('error', 'Only partitions for approved releases can be deployed.');
        }

        // Only allow deployment of partitions in pending status
        if ($partition->status !== 'pending') {
            return redirect()->route('admin.partitions.show', $partition->id)
                ->with('error', 'Only pending partitions can be deployed.');
        }

        $validator = Validator::make($request->all(), [
            'deployment_notes' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // In a real application, you would implement the actual deployment logic here
        // For now, we'll just mark the partition as deployed
        $partition->markAsDeployed($request->deployment_notes);

        // Check if all partitions are deployed
        $allDeployed = $partition->release->partitions()
            ->where('status', '!=', 'deployed')
            ->count() === 0;

        if ($allDeployed) {
            // Mark the release as deployed
            $partition->release->status = 'deployed';
            $partition->release->deployed_at = now();
            $partition->release->save();
        }

        return redirect()->route('admin.releases.show', $partition->release_id)
            ->with('success', 'Partition deployed successfully.');
    }

    /**
     * Mark the partition as failed.
     */
    public function fail(Request $request, string $id)
    {
        $partition = Partition::findOrFail($id);

        // Only allow failing of partitions for releases in approved status
        if ($partition->release->status !== 'approved') {
            return redirect()->route('admin.partitions.show', $partition->id)
                ->with('error', 'Only partitions for approved releases can be marked as failed.');
        }

        $validator = Validator::make($request->all(), [
            'deployment_notes' => 'required|string',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $partition->markAsFailed($request->deployment_notes);

        // Mark the release as failed
        $partition->release->status = 'failed';
        $partition->release->save();

        return redirect()->route('admin.releases.show', $partition->release_id)
            ->with('success', 'Partition marked as failed.');
    }

    /**
     * Roll back the partition.
     */
    public function rollback(Request $request, string $id)
    {
        $partition = Partition::findOrFail($id);

        // Only allow rollback of deployed partitions
        if ($partition->status !== 'deployed') {
            return redirect()->route('admin.partitions.show', $partition->id)
                ->with('error', 'Only deployed partitions can be rolled back.');
        }

        $validator = Validator::make($request->all(), [
            'deployment_notes' => 'required|string',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // In a real application, you would implement the actual rollback logic here
        // For now, we'll just mark the partition as rolled back
        $partition->markAsRolledBack($request->deployment_notes);

        return redirect()->route('admin.releases.show', $partition->release_id)
            ->with('success', 'Partition rolled back successfully.');
    }
}
