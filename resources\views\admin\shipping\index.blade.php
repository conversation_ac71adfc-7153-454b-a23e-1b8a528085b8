@extends('layouts.admin')

@section('title', 'Manage Shipping Options')

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-natryon-primary">
            <i class="bi bi-truck me-2"></i>Shipping Options
        </h1>
        <a href="{{ route('admin.shipping.create') }}" class="btn btn-natryon-primary">
            <i class="bi bi-plus-circle me-1"></i> Add New Shipping Option
        </a>
    </div>

    <div class="card card-natryon">
        <div class="card-header bg-natryon-primary text-white py-3">
            <h5 class="mb-0 fw-bold"><i class="bi bi-box me-2"></i>Shipping Methods</h5>
        </div>
        <div class="card-body">
            <div class="alert alert-natryon mb-4">
                <div class="d-flex">
                    <div class="me-3">
                        <i class="bi bi-info-circle-fill fs-4 text-natryon-primary"></i>
                    </div>
                    <div>
                        <h5 class="alert-heading text-natryon-primary">Shipping Configuration</h5>
                        <p class="mb-0">Drag and drop shipping options to change their display order. Active options will be shown to customers during checkout.</p>
                    </div>
                </div>
            </div>

            <div class="table-responsive">
                <table class="table table-natryon table-hover align-middle">
                    <thead>
                        <tr>
                            <th width="50">Order</th>
                            <th>Name</th>
                            <th>Cost</th>
                            <th>Location Type</th>
                            <th>Delivery Time</th>
                            <th>Status</th>
                            <th width="150">Actions</th>
                        </tr>
                    </thead>
                    <tbody id="shipping-options-table">
                        @foreach($shippingOptions as $option)
                            <tr data-id="{{ $option->id }}">
                                <td class="text-center">
                                    <span class="handle btn btn-sm btn-outline-natryon-grey rounded-circle">
                                        <i class="bi bi-grip-vertical"></i>
                                    </span>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-natryon me-2">
                                            <i class="bi bi-box2"></i>
                                        </div>
                                        <div>
                                            <div class="fw-medium text-natryon-primary">{{ $option->name }}</div>
                                            @if($option->description)
                                                <small class="text-natryon-grey">{{ $option->description }}</small>
                                            @endif
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    @if($option->cost > 0)
                                        <span class="fw-bold">${{ number_format($option->cost, 2) }}</span>
                                    @else
                                        <span class="badge badge-natryon-secondary rounded-pill px-3 py-2">
                                            <i class="bi bi-tag-fill me-1"></i>Free
                                        </span>
                                    @endif
                                </td>
                                <td>
                                    <span class="badge badge-natryon-blue rounded-pill px-3 py-2">
                                        <i class="bi bi-geo-alt-fill me-1"></i>
                                        @if($option->location_type === 'all')
                                            All Locations
                                        @elseif($option->location_type === 'country')
                                            Specific Countries
                                        @elseif($option->location_type === 'region')
                                            Specific Regions
                                        @endif
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-light text-dark rounded-pill px-3 py-2">
                                        <i class="bi bi-clock-fill me-1 text-natryon-grey"></i>
                                        {{ $option->delivery_time ?? 'Not specified' }}
                                    </span>
                                </td>
                                <td>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="status{{ $option->id }}" {{ $option->is_active ? 'checked' : '' }}
                                            onchange="document.getElementById('toggleForm{{ $option->id }}').submit()">
                                        <form id="toggleForm{{ $option->id }}" action="{{ route('admin.shipping.update', $option->id) }}" method="POST" class="d-none">
                                            @csrf
                                            @method('PUT')
                                            <input type="hidden" name="is_active" value="{{ $option->is_active ? 0 : 1 }}">
                                        </form>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex gap-1">
                                        <a href="{{ route('admin.shipping.edit', $option->id) }}" class="btn btn-sm btn-outline-natryon-primary rounded-circle" title="Edit Shipping Option">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <button type="button" class="btn btn-sm btn-outline-danger rounded-circle" data-bs-toggle="modal" data-bs-target="#deleteModal{{ $option->id }}" title="Delete Shipping Option">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>

                                    <!-- Delete Modal -->
                                    <div class="modal fade" id="deleteModal{{ $option->id }}" tabindex="-1" aria-labelledby="deleteModalLabel{{ $option->id }}" aria-hidden="true">
                                        <div class="modal-dialog">
                                            <div class="modal-content">
                                                <div class="modal-header bg-natryon-primary text-white">
                                                    <h5 class="modal-title" id="deleteModalLabel{{ $option->id }}">
                                                        <i class="bi bi-exclamation-triangle-fill me-2"></i>Confirm Delete
                                                    </h5>
                                                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                                                </div>
                                                <div class="modal-body">
                                                    <p class="mb-0">Are you sure you want to delete <strong class="text-natryon-primary">{{ $option->name }}</strong>?</p>
                                                    <p class="text-danger mb-0"><small><i class="bi bi-exclamation-circle me-1"></i>This action cannot be undone.</small></p>
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                                                        <i class="bi bi-x-circle me-1"></i>Cancel
                                                    </button>
                                                    <form action="{{ route('admin.shipping.destroy', $option->id) }}" method="POST">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="btn btn-danger delete-confirm" data-name="{{ $option->name }}">
                                                            <i class="bi bi-trash me-1"></i>Delete
                                                        </button>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        @endforeach

                        @if(count($shippingOptions) === 0)
                            <tr>
                                <td colspan="7" class="text-center py-5">
                                    <div class="mb-4">
                                        <span class="d-inline-block p-3 bg-natryon-grey-1 bg-opacity-25 rounded-circle">
                                            <i class="bi bi-truck text-natryon-grey" style="font-size: 3rem;"></i>
                                        </span>
                                    </div>
                                    <h4 class="text-natryon-primary mb-3">No shipping options found</h4>
                                    <p class="text-natryon-grey mb-4">
                                        Start by adding your first shipping option to offer to your customers.
                                    </p>
                                    <a href="{{ route('admin.shipping.create') }}" class="btn btn-natryon-primary mt-3">
                                        <i class="bi bi-plus-circle me-1"></i> Add New Shipping Option
                                    </a>
                                </td>
                            </tr>
                        @endif
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.14.0/Sortable.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const table = document.getElementById('shipping-options-table');

        new Sortable(table, {
            handle: '.handle',
            animation: 150,
            onEnd: function() {
                updateOrder();
            }
        });

        function updateOrder() {
            const rows = table.querySelectorAll('tr');
            const orders = Array.from(rows).map(row => row.dataset.id);

            fetch('{{ route("admin.shipping.update-order") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                body: JSON.stringify({ orders })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    console.log('Order updated successfully');
                } else {
                    console.error('Error updating order');
                }
            })
            .catch(error => {
                console.error('Error:', error);
            });
        }
    });
</script>
@endpush
