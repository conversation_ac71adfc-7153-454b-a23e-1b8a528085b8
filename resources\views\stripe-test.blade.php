@extends('layouts.app')

@section('title', 'Stripe Test')

@section('content')
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">Stripe Test Payment</div>

                <div class="card-body">
                    <div class="alert alert-info">
                        <p>This is a test page to verify that <PERSON>e is working correctly. You can use the following test card details:</p>
                        <ul>
                            <li>Card Number: 4242 4242 4242 4242</li>
                            <li>Expiry Date: Any future date (e.g., 12/25)</li>
                            <li>CVC: Any 3 digits (e.g., 123)</li>
                            <li>ZIP: Any 5 digits (e.g., 12345)</li>
                        </ul>
                    </div>

                    <form id="payment-form" class="mt-4">
                        <div class="mb-3">
                            <label for="card-element" class="form-label">Credit or debit card</label>
                            <div id="card-element" class="form-control" style="height: 2.4em; padding-top: .7em;"></div>
                            <div id="card-errors" class="invalid-feedback d-block" role="alert"></div>
                        </div>

                        <div class="mb-3">
                            <label for="amount" class="form-label">Amount (USD)</label>
                            <input type="number" class="form-control" id="amount" min="1" step="0.01" value="10.00">
                        </div>

                        <button type="submit" class="btn btn-primary" id="submit-button">
                            <span class="spinner-border spinner-border-sm d-none" id="spinner" role="status" aria-hidden="true"></span>
                            Pay Now
                        </button>
                    </form>

                    <div class="mt-4">
                        <div id="payment-result"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@endsection

@push('scripts')
<script src="https://js.stripe.com/v3/"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Create a Stripe client
        const stripe = Stripe('{{ env('STRIPE_KEY') }}');
        const elements = stripe.elements();

        // Create an instance of the card Element
        const cardElement = elements.create('card');

        // Add an instance of the card Element into the `card-element` div
        cardElement.mount('#card-element');

        // Handle real-time validation errors from the card Element
        cardElement.addEventListener('change', function(event) {
            const displayError = document.getElementById('card-errors');
            if (event.error) {
                displayError.textContent = event.error.message;
            } else {
                displayError.textContent = '';
            }
        });

        // Handle form submission
        const form = document.getElementById('payment-form');
        const submitButton = document.getElementById('submit-button');
        const spinner = document.getElementById('spinner');
        const paymentResult = document.getElementById('payment-result');

        form.addEventListener('submit', async function(event) {
            event.preventDefault();

            // Disable the submit button and show spinner
            submitButton.disabled = true;
            spinner.classList.remove('d-none');
            paymentResult.innerHTML = '';

            const amount = parseFloat(document.getElementById('amount').value);
            if (isNaN(amount) || amount <= 0) {
                paymentResult.innerHTML = '<div class="alert alert-danger">Please enter a valid amount.</div>';
                submitButton.disabled = false;
                spinner.classList.add('d-none');
                return;
            }

            try {
                // Create a PaymentIntent on the server
                const response = await fetch('/api/create-payment-intent', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    body: JSON.stringify({ amount: amount * 100 }) // Convert to cents
                });

                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }

                const data = await response.json();

                // Confirm the PaymentIntent with the card Element
                const result = await stripe.confirmCardPayment(data.clientSecret, {
                    payment_method: {
                        card: cardElement,
                        billing_details: {
                            name: 'Test Customer'
                        }
                    }
                });

                if (result.error) {
                    // Show error to your customer
                    paymentResult.innerHTML = `<div class="alert alert-danger">${result.error.message}</div>`;
                } else {
                    // The payment succeeded!
                    paymentResult.innerHTML = `
                        <div class="alert alert-success">
                            <h5>Payment successful!</h5>
                            <p>Payment ID: ${result.paymentIntent.id}</p>
                            <p>Amount: $${(result.paymentIntent.amount / 100).toFixed(2)}</p>
                        </div>
                    `;
                    form.reset();
                    cardElement.clear();
                }
            } catch (error) {
                paymentResult.innerHTML = `<div class="alert alert-danger">Error: ${error.message}</div>`;
            } finally {
                // Re-enable the submit button and hide spinner
                submitButton.disabled = false;
                spinner.classList.add('d-none');
            }
        });
    });
</script>
@endpush
