@extends('layouts.admin')

@section('title', 'Manage Affiliates')

@section('content')
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-natryon-primary">
            <i class="bi bi-diagram-3 me-2"></i>Affiliates
        </h1>
        <div class="d-flex">
            <a href="{{ route('admin.affiliates.index', ['export' => 'csv']) }}" class="btn btn-outline-natryon-primary me-2">
                <i class="bi bi-download me-1"></i> Export CSV
            </a>
            <div class="dropdown">
                <button class="btn btn-natryon-primary dropdown-toggle" type="button" id="affiliateActions" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="bi bi-gear me-1"></i> Actions
                </button>
                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="affiliateActions">
                    <li><a class="dropdown-item" href="{{ route('admin.affiliates.index', ['status' => 'active']) }}">
                        <i class="bi bi-check-circle me-2 text-natryon-secondary"></i>View Active Affiliates
                    </a></li>
                    <li><a class="dropdown-item" href="{{ route('admin.affiliates.index', ['status' => 'inactive']) }}">
                        <i class="bi bi-x-circle me-2 text-danger"></i>View Inactive Affiliates
                    </a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="{{ route('admin.affiliates.index', ['earnings' => 'pending']) }}">
                        <i class="bi bi-hourglass me-2 text-natryon-grey"></i>View Pending Earnings
                    </a></li>
                    <li><a class="dropdown-item" href="{{ route('admin.affiliates.index', ['earnings' => 'approved']) }}">
                        <i class="bi bi-check-circle me-2 text-natryon-secondary"></i>View Approved Earnings
                    </a></li>
                    <li><a class="dropdown-item" href="{{ route('admin.affiliates.index', ['earnings' => 'paid']) }}">
                        <i class="bi bi-cash-coin me-2 text-natryon-blue"></i>View Paid Earnings
                    </a></li>
                </ul>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card card-natryon h-100">
                <div class="card-body text-center p-4">
                    <div class="d-inline-flex align-items-center justify-content-center rounded-circle mb-3" style="width: 60px; height: 60px; background-color: rgba(45, 80, 75, 0.1);">
                        <i class="bi bi-people-fill fs-4 text-natryon-primary"></i>
                    </div>
                    <h6 class="text-natryon-grey mb-2">Total Affiliates</h6>
                    <h3 class="mb-0 text-natryon-primary">{{ $totalAffiliates ?? 0 }}</h3>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card card-natryon h-100">
                <div class="card-body text-center p-4">
                    <div class="d-inline-flex align-items-center justify-content-center rounded-circle mb-3" style="width: 60px; height: 60px; background-color: rgba(66, 134, 119, 0.1);">
                        <i class="bi bi-cash-stack fs-4 text-natryon-secondary"></i>
                    </div>
                    <h6 class="text-natryon-grey mb-2">Total Earnings</h6>
                    <h3 class="mb-0 text-natryon-secondary">${{ number_format($totalEarnings ?? 0, 2) }}</h3>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card card-natryon h-100">
                <div class="card-body text-center p-4">
                    <div class="d-inline-flex align-items-center justify-content-center rounded-circle mb-3" style="width: 60px; height: 60px; background-color: rgba(122, 134, 141, 0.1);">
                        <i class="bi bi-hourglass-split fs-4 text-natryon-grey"></i>
                    </div>
                    <h6 class="text-natryon-grey mb-2">Pending Earnings</h6>
                    <h3 class="mb-0 text-natryon-grey">${{ number_format($pendingEarnings ?? 0, 2) }}</h3>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card card-natryon h-100">
                <div class="card-body text-center p-4">
                    <div class="d-inline-flex align-items-center justify-content-center rounded-circle mb-3" style="width: 60px; height: 60px; background-color: rgba(36, 68, 90, 0.1);">
                        <i class="bi bi-check-circle-fill fs-4 text-natryon-blue"></i>
                    </div>
                    <h6 class="text-natryon-grey mb-2">Paid Earnings</h6>
                    <h3 class="mb-0 text-natryon-blue">${{ number_format($paidEarnings ?? 0, 2) }}</h3>
                </div>
            </div>
        </div>
    </div>

    <div class="card card-natryon">
        <div class="card-header bg-natryon-primary text-white py-3">
            <h5 class="mb-0 fw-bold"><i class="bi bi-people me-2"></i>Affiliate Partners</h5>
        </div>
        <div class="card-body">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <form action="{{ route('admin.affiliates.index') }}" method="GET" class="d-flex">
                    <div class="input-group">
                        <input type="text" name="search" class="form-control" placeholder="Search affiliates..." value="{{ request('search') }}">
                        <button type="submit" class="btn btn-outline-natryon-primary">
                            <i class="bi bi-search"></i>
                        </button>
                    </div>
                </form>
                <div class="d-flex">
                    <div class="dropdown me-2">
                        <button class="btn btn-outline-natryon-primary dropdown-toggle" type="button" id="filterDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi bi-funnel me-1"></i> Filter
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="filterDropdown">
                            <li><h6 class="dropdown-header">Affiliate Status</h6></li>
                            <li><a class="dropdown-item" href="{{ route('admin.affiliates.index', ['status' => 'active']) }}">
                                <i class="bi bi-check-circle me-2 text-natryon-secondary"></i>Active
                            </a></li>
                            <li><a class="dropdown-item" href="{{ route('admin.affiliates.index', ['status' => 'inactive']) }}">
                                <i class="bi bi-x-circle me-2 text-danger"></i>Inactive
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><h6 class="dropdown-header">Earnings Status</h6></li>
                            <li><a class="dropdown-item" href="{{ route('admin.affiliates.index', ['earnings' => 'pending']) }}">
                                <i class="bi bi-hourglass me-2 text-natryon-grey"></i>Pending Earnings
                            </a></li>
                            <li><a class="dropdown-item" href="{{ route('admin.affiliates.index', ['earnings' => 'approved']) }}">
                                <i class="bi bi-check-circle me-2 text-natryon-secondary"></i>Approved Earnings
                            </a></li>
                            <li><a class="dropdown-item" href="{{ route('admin.affiliates.index', ['earnings' => 'paid']) }}">
                                <i class="bi bi-cash-coin me-2 text-natryon-blue"></i>Paid Earnings
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ route('admin.affiliates.index') }}">
                                <i class="bi bi-x-circle me-2"></i>Clear Filters
                            </a></li>
                        </ul>
                    </div>
                    <div class="dropdown">
                        <button class="btn btn-outline-natryon-secondary dropdown-toggle" type="button" id="sortDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi bi-sort-down me-1"></i> Sort
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="sortDropdown">
                            <li><a class="dropdown-item" href="{{ route('admin.affiliates.index', ['sort' => 'name_asc']) }}">
                                <i class="bi bi-sort-alpha-down me-2"></i>Name (A-Z)
                            </a></li>
                            <li><a class="dropdown-item" href="{{ route('admin.affiliates.index', ['sort' => 'name_desc']) }}">
                                <i class="bi bi-sort-alpha-up me-2"></i>Name (Z-A)
                            </a></li>
                            <li><a class="dropdown-item" href="{{ route('admin.affiliates.index', ['sort' => 'earnings_high']) }}">
                                <i class="bi bi-arrow-down me-2"></i>Earnings (High to Low)
                            </a></li>
                            <li><a class="dropdown-item" href="{{ route('admin.affiliates.index', ['sort' => 'earnings_low']) }}">
                                <i class="bi bi-arrow-up me-2"></i>Earnings (Low to High)
                            </a></li>
                            <li><a class="dropdown-item" href="{{ route('admin.affiliates.index', ['sort' => 'referrals_high']) }}">
                                <i class="bi bi-arrow-down me-2"></i>Referrals (High to Low)
                            </a></li>
                            <li><a class="dropdown-item" href="{{ route('admin.affiliates.index', ['sort' => 'referrals_low']) }}">
                                <i class="bi bi-arrow-up me-2"></i>Referrals (Low to High)
                            </a></li>
                        </ul>
                    </div>
                </div>
            </div>

            @if(isset($affiliates) && count($affiliates) > 0)
                <div class="table-responsive">
                    <table class="table table-natryon table-hover align-middle">
                        <thead>
                            <tr>
                                <th>Affiliate</th>
                                <th>Code</th>
                                <th>Commission Rate</th>
                                <th>Referrals</th>
                                <th>Earnings</th>
                                <th>Status</th>
                                <th style="width: 150px;">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($affiliates as $affiliate)
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-natryon me-3">
                                                {{ strtoupper(substr($affiliate->user->name, 0, 1)) }}
                                            </div>
                                            <div>
                                                <div class="fw-medium text-natryon-primary">{{ $affiliate->user->name }}</div>
                                                <small class="text-natryon-grey">{{ $affiliate->user->email }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge badge-natryon-blue rounded-pill px-3 py-2">
                                            <i class="bi bi-tag-fill me-1"></i>{{ $affiliate->code }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge badge-natryon-secondary rounded-pill px-3 py-2">
                                            <i class="bi bi-percent me-1"></i>{{ $affiliate->commission_rate }}%
                                        </span>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="bi bi-people me-2 text-natryon-grey"></i>
                                            <span class="fw-medium">{{ $affiliate->user->referral_count ?? 0 }}</span>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="fw-bold">${{ number_format($affiliate->user->affiliate_earnings ?? 0, 2) }}</span>
                                    </td>
                                    <td>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="status{{ $affiliate->id }}" {{ $affiliate->is_active ? 'checked' : '' }}
                                                onchange="document.getElementById('toggleForm{{ $affiliate->id }}').submit()">
                                            <form id="toggleForm{{ $affiliate->id }}" action="{{ route('admin.affiliates.update', $affiliate->id) }}" method="POST" class="d-none">
                                                @csrf
                                                @method('PUT')
                                                <input type="hidden" name="is_active" value="{{ $affiliate->is_active ? 0 : 1 }}">
                                            </form>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex gap-1">
                                            <a href="{{ route('admin.affiliates.show', $affiliate->id) }}" class="btn btn-sm btn-outline-natryon-blue rounded-circle" title="View Affiliate">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                            <a href="{{ route('admin.affiliates.edit', $affiliate->id) }}" class="btn btn-sm btn-outline-natryon-primary rounded-circle" title="Edit Affiliate">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-outline-danger rounded-circle" data-bs-toggle="modal" data-bs-target="#deleteModal{{ $affiliate->id }}" title="Delete Affiliate">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </div>

                                        <!-- Delete Modal -->
                                        <div class="modal fade" id="deleteModal{{ $affiliate->id }}" tabindex="-1" aria-labelledby="deleteModalLabel{{ $affiliate->id }}" aria-hidden="true">
                                            <div class="modal-dialog">
                                                <div class="modal-content">
                                                    <div class="modal-header bg-natryon-primary text-white">
                                                        <h5 class="modal-title" id="deleteModalLabel{{ $affiliate->id }}">
                                                            <i class="bi bi-exclamation-triangle-fill me-2"></i>Confirm Delete
                                                        </h5>
                                                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                                                    </div>
                                                    <div class="modal-body">
                                                        <p class="mb-0">Are you sure you want to delete the affiliate code <strong class="text-natryon-primary">{{ $affiliate->code }}</strong> for {{ $affiliate->user->name }}?</p>
                                                        <p class="text-danger mb-0"><small><i class="bi bi-exclamation-circle me-1"></i>This action cannot be undone and will remove all associated earnings.</small></p>
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                                                            <i class="bi bi-x-circle me-1"></i>Cancel
                                                        </button>
                                                        <form action="{{ route('admin.affiliates.destroy', $affiliate->id) }}" method="POST">
                                                            @csrf
                                                            @method('DELETE')
                                                            <button type="submit" class="btn btn-danger delete-confirm" data-name="{{ $affiliate->code }}">
                                                                <i class="bi bi-trash me-1"></i>Delete
                                                            </button>
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <div class="d-flex justify-content-between align-items-center mt-4">
                    <div class="text-natryon-grey">
                        <i class="bi bi-info-circle me-1"></i> Showing {{ $affiliates->firstItem() ?? 0 }} to {{ $affiliates->lastItem() ?? 0 }} of {{ $affiliates->total() ?? 0 }} affiliates
                    </div>
                    <div>
                        {{ $affiliates->withQueryString()->links() }}
                    </div>
                </div>
            @else
                <div class="text-center py-5">
                    <div class="mb-4">
                        <span class="d-inline-block p-3 bg-natryon-grey-1 bg-opacity-25 rounded-circle">
                            <i class="bi bi-diagram-3 text-natryon-grey" style="font-size: 3rem;"></i>
                        </span>
                    </div>
                    <h4 class="text-natryon-primary mb-3">No affiliates found</h4>
                    <p class="text-natryon-grey mb-4">
                        @if(request('search'))
                            No affiliates match your search criteria. <a href="{{ route('admin.affiliates.index') }}" class="text-natryon-secondary">Clear search</a>
                        @elseif(request('status'))
                            No {{ request('status') }} affiliates found. <a href="{{ route('admin.affiliates.index') }}" class="text-natryon-secondary">View all affiliates</a>
                        @elseif(request('earnings'))
                            No affiliates with {{ request('earnings') }} earnings found. <a href="{{ route('admin.affiliates.index') }}" class="text-natryon-secondary">View all affiliates</a>
                        @else
                            There are no affiliates in the system yet. Start by adding your first affiliate partner.
                        @endif
                    </p>
                    <a href="{{ route('admin.affiliates.create') }}" class="btn btn-natryon-primary mt-3">
                        <i class="bi bi-plus-circle me-1"></i> Add New Affiliate
                    </a>
                </div>
            @endif
        </div>
    </div>

    <div class="card card-natryon mt-4">
        <div class="card-header bg-natryon-blue text-white py-3">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0 fw-bold"><i class="bi bi-cash-coin me-2"></i>Recent Affiliate Earnings</h5>
                <a href="{{ route('admin.affiliates.earnings') }}" class="btn btn-sm btn-light">
                    <i class="bi bi-list-ul me-1"></i>View All
                </a>
            </div>
        </div>
        <div class="card-body">
            @if(isset($recentEarnings) && count($recentEarnings) > 0)
                <div class="table-responsive">
                    <table class="table table-natryon table-hover align-middle">
                        <thead>
                            <tr>
                                <th>Affiliate</th>
                                <th>Order</th>
                                <th>Amount</th>
                                <th>Commission</th>
                                <th>Date</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($recentEarnings as $earning)
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-natryon me-2">
                                                {{ strtoupper(substr($earning->user->name, 0, 1)) }}
                                            </div>
                                            <div class="fw-medium text-natryon-primary">{{ $earning->user->name }}</div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge badge-natryon-grey rounded-pill px-3 py-2">
                                            <i class="bi bi-receipt me-1"></i>{{ $earning->order->order_number }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="fw-medium">${{ number_format($earning->order_amount, 2) }}</span>
                                    </td>
                                    <td>
                                        <span class="fw-bold text-natryon-secondary">${{ number_format($earning->commission_amount, 2) }}</span>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="bi bi-calendar-date me-2 text-natryon-grey"></i>
                                            {{ $earning->created_at->format('M d, Y') }}
                                        </div>
                                    </td>
                                    <td>
                                        <div class="dropdown">
                                            <button class="btn btn-sm dropdown-toggle rounded-pill
                                                @if($earning->status == 'pending') btn-outline-natryon-grey
                                                @elseif($earning->status == 'approved') btn-outline-natryon-secondary
                                                @elseif($earning->status == 'paid') btn-outline-natryon-blue
                                                @elseif($earning->status == 'rejected') btn-outline-danger
                                                @endif"
                                                type="button" id="earningStatus{{ $earning->id }}" data-bs-toggle="dropdown" aria-expanded="false">
                                                <i class="bi
                                                    @if($earning->status == 'pending') bi-hourglass
                                                    @elseif($earning->status == 'approved') bi-check-circle
                                                    @elseif($earning->status == 'paid') bi-cash-coin
                                                    @elseif($earning->status == 'rejected') bi-x-circle
                                                    @endif me-1"></i>
                                                {{ ucfirst($earning->status) }}
                                            </button>
                                            <ul class="dropdown-menu" aria-labelledby="earningStatus{{ $earning->id }}">
                                                <li>
                                                    <form action="{{ route('admin.affiliates.update-earning', $earning->id) }}" method="POST">
                                                        @csrf
                                                        @method('PUT')
                                                        <input type="hidden" name="status" value="pending">
                                                        <button type="submit" class="dropdown-item status-confirm" data-status="pending" data-name="Earning #{{ $earning->id }}">
                                                            <i class="bi bi-hourglass me-2 text-natryon-grey"></i>Pending
                                                        </button>
                                                    </form>
                                                </li>
                                                <li>
                                                    <form action="{{ route('admin.affiliates.update-earning', $earning->id) }}" method="POST">
                                                        @csrf
                                                        @method('PUT')
                                                        <input type="hidden" name="status" value="approved">
                                                        <button type="submit" class="dropdown-item status-confirm" data-status="approved" data-name="Earning #{{ $earning->id }}">
                                                            <i class="bi bi-check-circle me-2 text-natryon-secondary"></i>Approve
                                                        </button>
                                                    </form>
                                                </li>
                                                <li>
                                                    <form action="{{ route('admin.affiliates.update-earning', $earning->id) }}" method="POST">
                                                        @csrf
                                                        @method('PUT')
                                                        <input type="hidden" name="status" value="paid">
                                                        <button type="submit" class="dropdown-item status-confirm" data-status="paid" data-name="Earning #{{ $earning->id }}">
                                                            <i class="bi bi-cash-coin me-2 text-natryon-blue"></i>Mark as Paid
                                                        </button>
                                                    </form>
                                                </li>
                                                <li>
                                                    <form action="{{ route('admin.affiliates.update-earning', $earning->id) }}" method="POST">
                                                        @csrf
                                                        @method('PUT')
                                                        <input type="hidden" name="status" value="rejected">
                                                        <button type="submit" class="dropdown-item status-confirm" data-status="rejected" data-name="Earning #{{ $earning->id }}">
                                                            <i class="bi bi-x-circle me-2 text-danger"></i>Reject
                                                        </button>
                                                    </form>
                                                </li>
                                            </ul>
                                        </div>
                                    </td>
                                    <td>
                                        <a href="{{ route('admin.orders.show', $earning->order_id) }}" class="btn btn-sm btn-outline-natryon-blue rounded-circle" title="View Order">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            @else
                <div class="text-center py-4">
                    <div class="mb-3">
                        <span class="d-inline-block p-3 bg-natryon-grey-1 bg-opacity-25 rounded-circle">
                            <i class="bi bi-cash text-natryon-grey" style="font-size: 2rem;"></i>
                        </span>
                    </div>
                    <p class="text-natryon-grey mb-0">No affiliate earnings yet.</p>
                </div>
            @endif
        </div>
    </div>
@endsection
